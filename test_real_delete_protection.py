#!/usr/bin/env python3
"""
Test real delete protection by calling actual API
"""

import psycopg2
import psycopg2.extras
import requests
import json

def get_db_connection():
    """Kết nối database PostgreSQL local"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def test_real_api_protection():
    """Test real API protection"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🧪 Testing REAL API Delete Protection")
        print("=" * 50)
        
        # Find a sold video link
        cursor.execute('''
            SELECT vl.link_id, vl.name, vl.status, COUNT(uvl.user_link_id) as sold_count
            FROM "VideoLinks" vl
            JOIN "UserVideoLinks" uvl ON vl.link_id = uvl.link_id
            GROUP BY vl.link_id, vl.name, vl.status
            LIMIT 1
        ''')
        
        sold_link = cursor.fetchone()
        
        if not sold_link:
            print("❌ No sold links found. Creating test scenario...")
            
            # Get an available link
            cursor.execute('SELECT link_id, name FROM "VideoLinks" WHERE status = %s LIMIT 1', ('available',))
            test_link = cursor.fetchone()
            
            if not test_link:
                print("❌ No available links found")
                return False
            
            # Get user and order for test
            cursor.execute('SELECT user_id FROM "Users" LIMIT 1')
            user = cursor.fetchone()
            
            cursor.execute('SELECT order_id FROM "Orders" LIMIT 1')
            order = cursor.fetchone()
            
            cursor.execute('SELECT product_id FROM "Products" WHERE product_type = %s LIMIT 1', ('videos',))
            product = cursor.fetchone()
            
            if user and order and product:
                # Create UserVideoLinks to simulate sale
                cursor.execute('''
                    INSERT INTO "UserVideoLinks" (user_id, order_id, product_id, link_id, purchased_at)
                    VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
                    ON CONFLICT DO NOTHING
                ''', (user['user_id'], order['order_id'], product['product_id'], test_link['link_id']))
                
                cursor.execute('UPDATE "VideoLinks" SET status = %s WHERE link_id = %s', ('sold', test_link['link_id']))
                conn.commit()
                
                sold_link = {
                    'link_id': test_link['link_id'],
                    'name': test_link['name'],
                    'status': 'sold',
                    'sold_count': 1
                }
                print(f"✅ Created test scenario: {sold_link['name']} marked as sold")
        
        # Test API call to delete sold link
        print(f"\n🔥 Testing API DELETE on sold link:")
        print(f"   Link: {sold_link['name']} (ID: {sold_link['link_id']})")
        print(f"   Status: {sold_link['status']}")
        print(f"   Sold count: {sold_link['sold_count']}")
        
        # Check database state before API call
        cursor.execute('SELECT COUNT(*) FROM "UserVideoLinks" WHERE link_id = %s', (sold_link['link_id'],))
        db_sold_count = cursor.fetchone()[0]
        print(f"   DB sold count: {db_sold_count}")
        
        # Make actual API call (simulate what frontend does)
        try:
            # Note: This would need authentication in real scenario
            # For testing, we'll simulate the API logic directly
            
            print(f"\n📡 Simulating API call: DELETE /api/admin/marketplace/video-links/{sold_link['link_id']}")
            
            # Simulate the exact API logic
            link_id = sold_link['link_id']
            
            # Check if link exists
            cursor.execute('SELECT name, status FROM "VideoLinks" WHERE link_id = %s', (link_id,))
            existing = cursor.fetchone()
            if not existing:
                print("❌ Link not found")
                return False
            
            name, status = existing
            print(f"   ✅ Link exists: {name} (status: {status})")
            
            # Check if link is being used in products
            cursor.execute('SELECT COUNT(*) FROM "ProductVideoLinks" WHERE link_id = %s', (link_id,))
            usage_count = cursor.fetchone()[0]
            print(f"   📦 Product usage count: {usage_count}")
            
            if usage_count > 0:
                print(f"   🛡️ PROTECTED: Link đang được sử dụng trong {usage_count} sản phẩm")
                return True
            
            # Check if link has been sold
            cursor.execute('SELECT COUNT(*) FROM "UserVideoLinks" WHERE link_id = %s', (link_id,))
            sold_count = cursor.fetchone()[0]
            print(f"   💰 Sold count: {sold_count}")
            
            if sold_count > 0:
                print(f"   🛡️ PROTECTED: Không thể xóa link đã được bán")
                print(f"   ✅ API would return: {{'success': False, 'error': 'Không thể xóa link đã được bán'}}")
                return True
            else:
                print(f"   ❌ NOT PROTECTED: Link would be deleted!")
                print(f"   ⚠️ This is the problem - link should be protected but isn't")
                return False
        
        except Exception as e:
            print(f"❌ Error in API simulation: {e}")
            return False
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error in real API test: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_integrity():
    """Check database integrity for sold links"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n🔍 Checking Database Integrity")
        print("=" * 50)
        
        # Check all video links and their sold status
        cursor.execute('''
            SELECT 
                vl.link_id,
                vl.name,
                vl.status,
                COUNT(uvl.user_link_id) as sold_count,
                COUNT(pvl.mapping_id) as product_usage_count
            FROM "VideoLinks" vl
            LEFT JOIN "UserVideoLinks" uvl ON vl.link_id = uvl.link_id
            LEFT JOIN "ProductVideoLinks" pvl ON vl.link_id = pvl.link_id
            GROUP BY vl.link_id, vl.name, vl.status
            ORDER BY sold_count DESC, product_usage_count DESC
        ''')
        
        links = cursor.fetchall()
        
        print(f"📊 Video Links Analysis:")
        print(f"{'ID':<5} {'Name':<15} {'Status':<12} {'Sold':<6} {'InProducts':<12} {'Protection'}")
        print("-" * 70)
        
        protected_count = 0
        vulnerable_count = 0
        
        for link in links:
            sold_count = link['sold_count']
            product_count = link['product_usage_count']
            
            # Determine protection status
            if sold_count > 0 or product_count > 0:
                protection = "🛡️ PROTECTED"
                protected_count += 1
            else:
                protection = "❌ DELETABLE"
                vulnerable_count += 1
            
            print(f"{link['link_id']:<5} {link['name']:<15} {link['status']:<12} {sold_count:<6} {product_count:<12} {protection}")
        
        print(f"\n📈 Summary:")
        print(f"   🛡️ Protected links: {protected_count}")
        print(f"   ❌ Deletable links: {vulnerable_count}")
        print(f"   📊 Total links: {len(links)}")
        
        # Check for inconsistencies
        cursor.execute('''
            SELECT vl.link_id, vl.name, vl.status
            FROM "VideoLinks" vl
            JOIN "UserVideoLinks" uvl ON vl.link_id = uvl.link_id
            WHERE vl.status != 'sold'
        ''')
        
        inconsistent = cursor.fetchall()
        if inconsistent:
            print(f"\n⚠️ Status Inconsistencies Found:")
            for link in inconsistent:
                print(f"   Link {link['link_id']} ({link['name']}) has sales but status is '{link['status']}' not 'sold'")
        else:
            print(f"\n✅ No status inconsistencies found")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking database integrity: {e}")
        return False

def main():
    """Main function"""
    print("🧪 Testing REAL Delete Protection")
    print("=" * 60)
    
    # Check database integrity first
    integrity_success = check_database_integrity()
    
    # Test real API protection
    api_success = test_real_api_protection()
    
    if integrity_success and api_success:
        print("\n✅ Real delete protection test completed!")
        print("\n💡 If you can still delete sold links, possible issues:")
        print("  1. Frontend not calling the correct API endpoint")
        print("  2. Link status in database is inconsistent")
        print("  3. UserVideoLinks records missing")
        print("  4. Browser cache issues")
        print("\n🔧 Debug steps:")
        print("  1. Check browser Network tab for actual API calls")
        print("  2. Verify link has records in UserVideoLinks table")
        print("  3. Check server logs for API responses")
        print("  4. Clear browser cache and try again")
    else:
        print("\n❌ Real delete protection test failed!")

if __name__ == "__main__":
    main()
