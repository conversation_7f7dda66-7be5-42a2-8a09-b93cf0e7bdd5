#!/usr/bin/env python3
"""
Marketplace Migration Script
Alternative Python script for database migrations
"""

import psycopg2
import psycopg2.extras
import sys
import os
from datetime import datetime

# Import database configuration
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

try:
    from db_config import PG_CONFIG
    DB_CONFIG = PG_CONFIG
    print(f"✅ Loaded database config from {APP_DIR}/db_config.py")
except ImportError as e:
    print(f"❌ Failed to load database config: {e}")
    print(f"Please ensure db_config.py exists in {APP_DIR}")
    sys.exit(1)

def run_migration():
    """Run all marketplace database migrations"""
    
    print("🚀 Starting Marketplace Database Migration...")
    print("=" * 50)
    
    try:
        # Connect to database
        conn = psycopg2.connect(**DB_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # List of migrations to run
        migrations = [
            {
                'name': 'Create Products table',
                'sql': '''
                CREATE TABLE IF NOT EXISTS "Products" (
                    product_id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    price INTEGER NOT NULL,
                    category_id INTEGER,
                    image_url VARCHAR(500),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    stock_quantity INTEGER DEFAULT 0,
                    is_featured BOOLEAN DEFAULT FALSE
                );
                '''
            },
            {
                'name': 'Create Categories table',
                'sql': '''
                CREATE TABLE IF NOT EXISTS "Categories" (
                    category_id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    image_url VARCHAR(500),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                '''
            },
            {
                'name': 'Create Orders table',
                'sql': '''
                CREATE TABLE IF NOT EXISTS "Orders" (
                    order_id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    total_amount INTEGER NOT NULL,
                    status VARCHAR(50) DEFAULT 'pending',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                '''
            },
            {
                'name': 'Create OrderItems table',
                'sql': '''
                CREATE TABLE IF NOT EXISTS "OrderItems" (
                    order_item_id SERIAL PRIMARY KEY,
                    order_id INTEGER REFERENCES "Orders"(order_id),
                    product_id INTEGER REFERENCES "Products"(product_id),
                    quantity INTEGER NOT NULL,
                    price INTEGER NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                '''
            },
            {
                'name': 'Create AFFPackages table',
                'sql': '''
                CREATE TABLE IF NOT EXISTS "AFFPackages" (
                    package_id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    description TEXT,
                    price INTEGER NOT NULL,
                    account_limit INTEGER NOT NULL,
                    duration_days INTEGER NOT NULL,
                    is_active BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                '''
            },
            {
                'name': 'Create UserSubscriptions table',
                'sql': '''
                CREATE TABLE IF NOT EXISTS "UserSubscriptions" (
                    subscription_id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    product_id INTEGER,
                    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    end_date TIMESTAMP,
                    status VARCHAR(50) DEFAULT 'active',
                    account_limit INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                '''
            },
            {
                'name': 'Create Config table',
                'sql': '''
                CREATE TABLE IF NOT EXISTS "Config" (
                    config_id SERIAL PRIMARY KEY,
                    config_key VARCHAR(255) UNIQUE NOT NULL,
                    config_value TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                '''
            },
            {
                'name': 'Add revenue_enabled column to Accounts',
                'sql': '''
                ALTER TABLE "Accounts" 
                ADD COLUMN IF NOT EXISTS revenue_enabled BOOLEAN DEFAULT FALSE;
                '''
            },
            {
                'name': 'Insert default coffee cup configs',
                'sql': '''
                INSERT INTO "Config" (config_key, config_value) 
                VALUES 
                    ('enable_coffee_display', 'false'),
                    ('coffee_cup_value', '50000'),
                    ('coffee_cup_icon', '☕'),
                    ('coffee_icon_type', 'emoji'),
                    ('coffee_icon_width', '20'),
                    ('coffee_icon_height', '20')
                ON CONFLICT (config_key) DO NOTHING;
                '''
            },
            {
                'name': 'Insert default categories',
                'sql': '''
                INSERT INTO "Categories" (name, description, is_active) 
                VALUES 
                    ('Gói AFF', 'Các gói affiliate marketing', TRUE),
                    ('Tools', 'Công cụ hỗ trợ', TRUE),
                    ('Services', 'Dịch vụ khác', TRUE)
                ON CONFLICT DO NOTHING;
                '''
            }
        ]
        
        # Run each migration
        for migration in migrations:
            try:
                print(f"🔄 Running: {migration['name']}")
                cursor.execute(migration['sql'])
                conn.commit()
                print(f"✅ Success: {migration['name']}")
            except Exception as e:
                print(f"⚠️  Warning: {migration['name']} - {str(e)}")
                conn.rollback()
        
        # Validate installation
        print("\n🔍 Validating installation...")
        
        tables_to_check = ['Products', 'Categories', 'Orders', 'OrderItems', 'AFFPackages', 'UserSubscriptions', 'Config']
        
        for table in tables_to_check:
            try:
                cursor.execute(f'SELECT 1 FROM "{table}" LIMIT 1;')
                print(f"✅ Table {table} exists")
            except Exception as e:
                print(f"❌ Table {table} missing: {str(e)}")
        
        # Check revenue_enabled column
        try:
            cursor.execute('SELECT revenue_enabled FROM "Accounts" LIMIT 1;')
            print("✅ Column revenue_enabled exists in Accounts")
        except Exception as e:
            print(f"❌ Column revenue_enabled missing: {str(e)}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Migration completed successfully at {datetime.now()}")
        
    except psycopg2.Error as e:
        print(f"❌ Database error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

def create_directories():
    """Create required upload directories"""
    
    print("\n📁 Creating upload directories...")
    
    base_dir = f"{APP_DIR}/static/uploads"
    directories = [
        f"{base_dir}/coffee-icons",
        f"{base_dir}/products", 
        f"{base_dir}/categories"
    ]
    
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            os.chmod(directory, 0o755)
            print(f"✅ Created: {directory}")
        except Exception as e:
            print(f"❌ Failed to create {directory}: {str(e)}")

if __name__ == "__main__":
    run_migration()
    create_directories()
    
    print("\n📝 Next steps:")
    print("   1. Push your code changes to the server")
    print("   2. Restart the application service")
    print("   3. Test the marketplace functionality")
