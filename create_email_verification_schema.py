#!/usr/bin/env python3
"""
Create email verification schema
"""

import psycopg2
import psycopg2.extras
from db_config import PG_CONFIG

def get_db_connection():
    """Kết nối database PostgreSQL từ config"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def create_email_verification_tables():
    """Tạo tables cho email verification system"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        print("📧 Creating Email Verification Schema")
        print("=" * 50)
        
        # 1. Add email verification columns to Users table
        print("📋 1. Updating Users table...")
        
        # Check if columns exist first
        cursor.execute('''
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'Users' AND column_name IN ('email_verified', 'email_verified_at')
        ''')
        existing_columns = [row[0] for row in cursor.fetchall()]
        
        if 'email_verified' not in existing_columns:
            cursor.execute('ALTER TABLE "Users" ADD COLUMN email_verified BOOLEAN DEFAULT FALSE')
            print("   ✅ Added email_verified column")
        else:
            print("   ℹ️ email_verified column already exists")
        
        if 'email_verified_at' not in existing_columns:
            cursor.execute('ALTER TABLE "Users" ADD COLUMN email_verified_at TIMESTAMP NULL')
            print("   ✅ Added email_verified_at column")
        else:
            print("   ℹ️ email_verified_at column already exists")
        
        # Handle NULL emails before making it required
        cursor.execute('SELECT COUNT(*) FROM "Users" WHERE email IS NULL OR email = \'\'')
        null_email_count = cursor.fetchone()[0]

        if null_email_count > 0:
            print(f"   ⚠️ Found {null_email_count} users with NULL/empty email")
            # Update NULL emails with placeholder
            cursor.execute('''
                UPDATE "Users"
                SET email = 'user' || user_id || '@placeholder.local'
                WHERE email IS NULL OR email = ''
            ''')
            print(f"   ✅ Updated {null_email_count} users with placeholder emails")

        # Make email required
        cursor.execute('''
            ALTER TABLE "Users"
            ALTER COLUMN email SET NOT NULL
        ''')
        print("   ✅ Made email column required")
        
        # 2. Create EmailVerificationCodes table
        print("\n📋 2. Creating EmailVerificationCodes table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "EmailVerificationCodes" (
                code_id SERIAL PRIMARY KEY,
                email VARCHAR(255) NOT NULL,
                code VARCHAR(6) NOT NULL,
                code_type VARCHAR(20) NOT NULL, -- 'registration' or 'password_reset'
                user_id INTEGER REFERENCES "Users"(user_id) ON DELETE CASCADE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                used_at TIMESTAMP NULL,
                attempts INTEGER DEFAULT 0,
                max_attempts INTEGER DEFAULT 3,
                is_used BOOLEAN DEFAULT FALSE,
                ip_address INET,
                user_agent TEXT
            )
        ''')
        print("   ✅ EmailVerificationCodes table created")
        
        # 3. Create indexes for performance
        print("\n📋 3. Creating indexes...")
        
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_email_verification_codes_email ON "EmailVerificationCodes"(email)',
            'CREATE INDEX IF NOT EXISTS idx_email_verification_codes_code ON "EmailVerificationCodes"(code)',
            'CREATE INDEX IF NOT EXISTS idx_email_verification_codes_type ON "EmailVerificationCodes"(code_type)',
            'CREATE INDEX IF NOT EXISTS idx_email_verification_codes_expires ON "EmailVerificationCodes"(expires_at)',
            'CREATE INDEX IF NOT EXISTS idx_email_verification_codes_used ON "EmailVerificationCodes"(is_used)',
            'CREATE INDEX IF NOT EXISTS idx_users_email_verified ON "Users"(email_verified)',
            'CREATE INDEX IF NOT EXISTS idx_users_email ON "Users"(email)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("   ✅ All indexes created")
        
        # 4. Create cleanup function for expired codes
        print("\n📋 4. Creating cleanup function...")
        
        cursor.execute('''
            CREATE OR REPLACE FUNCTION cleanup_expired_verification_codes()
            RETURNS INTEGER AS $$
            DECLARE
                deleted_count INTEGER;
            BEGIN
                DELETE FROM "EmailVerificationCodes"
                WHERE expires_at < CURRENT_TIMESTAMP
                AND is_used = FALSE;
                
                GET DIAGNOSTICS deleted_count = ROW_COUNT;
                RETURN deleted_count;
            END;
            $$ LANGUAGE plpgsql;
        ''')
        print("   ✅ Cleanup function created")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("\n🎉 Email verification schema created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error creating schema: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        return False

def verify_schema():
    """Verify schema was created correctly"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n🔍 Verifying Schema")
        print("=" * 50)
        
        # Check Users table columns
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' 
            AND column_name IN ('email', 'email_verified', 'email_verified_at')
            ORDER BY column_name
        ''')
        
        print("📋 Users table columns:")
        for col in cursor.fetchall():
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            print(f"   {col['column_name']}: {col['data_type']} {nullable}")
        
        # Check EmailVerificationCodes table
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'EmailVerificationCodes'
            )
        ''')
        
        table_exists = cursor.fetchone()[0]
        if table_exists:
            print("✅ EmailVerificationCodes table exists")
            
            # Count indexes
            cursor.execute('''
                SELECT COUNT(*) FROM pg_indexes 
                WHERE tablename = 'EmailVerificationCodes'
            ''')
            index_count = cursor.fetchone()[0]
            print(f"✅ EmailVerificationCodes has {index_count} indexes")
        else:
            print("❌ EmailVerificationCodes table missing")
            return False
        
        # Check cleanup function
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM pg_proc 
                WHERE proname = 'cleanup_expired_verification_codes'
            )
        ''')
        
        function_exists = cursor.fetchone()[0]
        if function_exists:
            print("✅ Cleanup function exists")
        else:
            print("❌ Cleanup function missing")
            return False
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verifying schema: {e}")
        return False

def main():
    """Main function"""
    print("📧 Email Verification Schema Setup")
    print("=" * 60)
    
    # Create schema
    schema_success = create_email_verification_tables()
    
    # Verify schema
    verify_success = verify_schema()
    
    if schema_success and verify_success:
        print("\n🎉 Email verification schema setup completed!")
        print("\n📋 What was created:")
        print("  ✅ Users.email_verified column")
        print("  ✅ Users.email_verified_at column")
        print("  ✅ Users.email made required")
        print("  ✅ EmailVerificationCodes table")
        print("  ✅ Performance indexes")
        print("  ✅ Cleanup function")
        print("\n🎯 Ready for:")
        print("  - Registration with email verification")
        print("  - Password reset via email")
        print("  - OTP code management")
    else:
        print("\n❌ Schema setup failed!")

if __name__ == "__main__":
    main()
