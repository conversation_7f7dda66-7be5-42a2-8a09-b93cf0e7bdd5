#!/usr/bin/env python3
"""
Add is_sold and marketplace_status columns to Accounts table
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_is_sold_column():
    """Add is_sold and marketplace_status columns to Accounts table"""
    
    print("🚀 Adding is_sold and marketplace_status columns to Accounts table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current Accounts table structure
        print("\n🔧 Step 1: Checking current Accounts table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            AND column_name IN ('is_sold', 'marketplace_status')
            ORDER BY column_name;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Existing marketplace columns: {existing_column_names}")
        
        # Step 2: Add is_sold column if not exists
        if 'is_sold' not in existing_column_names:
            print("\n🔧 Step 2: Adding is_sold column...")
            
            cursor.execute('''
                ALTER TABLE "Accounts" 
                ADD COLUMN is_sold BOOLEAN DEFAULT FALSE;
            ''')
            
            print("  ✅ Added is_sold column (BOOLEAN DEFAULT FALSE)")
        else:
            print("\n✅ Step 2: is_sold column already exists")
        
        # Step 3: Add marketplace_status column if not exists
        if 'marketplace_status' not in existing_column_names:
            print("\n🔧 Step 3: Adding marketplace_status column...")
            
            cursor.execute('''
                ALTER TABLE "Accounts" 
                ADD COLUMN marketplace_status VARCHAR(20) DEFAULT 'available';
            ''')
            
            print("  ✅ Added marketplace_status column (VARCHAR(20) DEFAULT 'available')")
        else:
            print("\n✅ Step 3: marketplace_status column already exists")
        
        # Step 4: Update existing data
        print("\n🔧 Step 4: Updating existing data...")
        
        # Set is_sold = FALSE for all existing accounts
        cursor.execute('UPDATE "Accounts" SET is_sold = FALSE WHERE is_sold IS NULL;')
        updated_is_sold = cursor.rowcount
        print(f"  ✅ Updated {updated_is_sold} accounts: is_sold = FALSE")
        
        # Set marketplace_status based on account status
        cursor.execute('''
            UPDATE "Accounts" 
            SET marketplace_status = CASE 
                WHEN status IN ('Có giỏ', 'Live') THEN 'available'
                WHEN status = 'Die' THEN 'unavailable'
                ELSE 'available'
            END
            WHERE marketplace_status IS NULL OR marketplace_status = '';
        ''')
        updated_status = cursor.rowcount
        print(f"  ✅ Updated {updated_status} accounts: marketplace_status based on status")
        
        # Step 5: Create indexes for performance
        print("\n🔧 Step 5: Creating indexes...")
        
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_accounts_is_sold ON "Accounts"(is_sold);',
            'CREATE INDEX IF NOT EXISTS idx_accounts_marketplace_status ON "Accounts"(marketplace_status);',
            'CREATE INDEX IF NOT EXISTS idx_accounts_marketplace_combo ON "Accounts"(is_sold, marketplace_status) WHERE is_deleted = 0;'
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
                index_name = index_sql.split('idx_')[1].split()[0] if 'idx_' in index_sql else 'index'
                print(f"  ✅ Created index: {index_name}")
            except Exception as e:
                print(f"  ⚠️  Index creation: {e}")
        
        # Step 6: Verify final structure
        print("\n🔧 Step 6: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            AND column_name IN ('is_sold', 'marketplace_status')
            ORDER BY column_name;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Final marketplace columns:")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 7: Test data
        print("\n🔧 Step 7: Testing data...")
        
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN is_sold = FALSE THEN 1 END) as not_sold,
                COUNT(CASE WHEN is_sold = TRUE THEN 1 END) as sold,
                COUNT(CASE WHEN marketplace_status = 'available' THEN 1 END) as available,
                COUNT(CASE WHEN marketplace_status = 'unavailable' THEN 1 END) as unavailable
            FROM "Accounts" 
            WHERE is_deleted = 0;
        ''')
        
        stats = cursor.fetchone()
        print(f"📊 Account statistics:")
        print(f"  - Total accounts: {stats[0]}")
        print(f"  - Not sold: {stats[1]}")
        print(f"  - Sold: {stats[2]}")
        print(f"  - Available: {stats[3]}")
        print(f"  - Unavailable: {stats[4]}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Migration completed successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test marketplace functionality")
        print("   3. Test account product type selection")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_is_sold_column()
    sys.exit(0 if success else 1)
