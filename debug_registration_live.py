#!/usr/bin/env python3
"""
Live debug registration flow by monitoring database and testing exact same flow
"""

import requests
import json
import time
import psycopg2
import psycopg2.extras
from db_config import PG_CONFIG

def monitor_database_changes():
    """Monitor database for new entries during registration"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Get current max code_id
        cursor.execute('SELECT COALESCE(MAX(code_id), 0) FROM "EmailVerificationCodes"')
        max_code_id = cursor.fetchone()[0]
        
        print(f"📊 Current max code_id: {max_code_id}")
        
        cursor.close()
        conn.close()
        return max_code_id
        
    except Exception as e:
        print(f"❌ Error monitoring database: {e}")
        return 0

def check_new_codes(previous_max_id):
    """Check for new verification codes"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        cursor.execute('''
            SELECT code_id, email, code, code_type, created_at, expires_at, user_id
            FROM "EmailVerificationCodes"
            WHERE code_id > %s
            ORDER BY code_id DESC
        ''', (previous_max_id,))
        
        new_codes = cursor.fetchall()
        
        if new_codes:
            print(f"📧 Found {len(new_codes)} new verification codes:")
            for code in new_codes:
                print(f"   Code ID {code['code_id']}: {code['email']} -> {code['code']} ({code['code_type']})")
                print(f"      Created: {code['created_at']}, Expires: {code['expires_at']}")
        else:
            print("❌ No new verification codes found in database!")
        
        cursor.close()
        conn.close()
        return new_codes
        
    except Exception as e:
        print(f"❌ Error checking new codes: {e}")
        return []

def test_registration_api_call():
    """Test registration API call with detailed debugging"""
    print(f"\n🧪 Testing Registration API Call")
    print("=" * 60)
    
    # Test data
    test_data = {
        'username': f'debuguser{int(time.time())}',
        'email': f'debug{int(time.time())}@test.com',
        'phone': '0123456789',
        'password': 'testpass123',
        'confirm_password': 'testpass123'
    }
    
    print(f"📝 Registration data:")
    for key, value in test_data.items():
        if 'password' in key:
            print(f"   {key}: {'*' * len(value)}")
        else:
            print(f"   {key}: {value}")
    
    # Monitor database before
    max_id_before = monitor_database_changes()
    
    try:
        # Make registration request
        print(f"\n📤 Sending registration request...")
        
        session = requests.Session()
        
        # First get the registration page to establish session
        print("📋 1. Getting registration page...")
        reg_page = session.get('http://localhost:5000/register')
        print(f"   Status: {reg_page.status_code}")

        # Submit registration
        print("📋 2. Submitting registration...")
        response = session.post('http://localhost:5000/register', data=test_data)
        
        print(f"📊 Response details:")
        print(f"   Status Code: {response.status_code}")
        print(f"   Final URL: {response.url}")
        print(f"   Headers: {dict(response.headers)}")
        
        # Check if redirected to verify page
        if 'verify-email' in response.url:
            print("✅ Redirected to email verification page")
        else:
            print("❌ NOT redirected to verification page")
            print(f"📄 Response content (first 1000 chars):")
            print(response.text[:1000])
            
            # Look for error messages
            if 'Không thể gửi email xác thực' in response.text:
                print("🔍 Found error message in response!")
                # Extract error details
                import re
                error_match = re.search(r'Không thể gửi email xác thực: ([^<]+)', response.text)
                if error_match:
                    error_detail = error_match.group(1).strip()
                    print(f"   Error detail: {error_detail}")
        
        # Monitor database after
        print(f"\n📊 Checking database changes...")
        time.sleep(1)  # Wait a moment
        new_codes = check_new_codes(max_id_before)
        
        return response, new_codes
        
    except Exception as e:
        print(f"❌ Registration API call failed: {e}")
        import traceback
        traceback.print_exc()
        return None, []

def test_create_verification_code_directly():
    """Test create_verification_code with exact same parameters as registration"""
    print(f"\n🧪 Testing create_verification_code directly")
    print("=" * 60)
    
    try:
        from email_verification_service import create_verification_code
        
        # Use same email as API test
        test_email = f'direct{int(time.time())}@test.com'
        
        print(f"📧 Testing create_verification_code with:")
        print(f"   Email: {test_email}")
        print(f"   Type: registration")
        print(f"   User ID: None")
        print(f"   IP: 127.0.0.1")
        print(f"   User Agent: Test")
        
        result = create_verification_code(
            email=test_email,
            code_type='registration',
            user_id=None,
            ip_address='127.0.0.1',
            user_agent='Test User Agent'
        )
        
        if result:
            print(f"✅ Direct create_verification_code SUCCESS!")
            print(f"   Code ID: {result['code_id']}")
            print(f"   Code: {result['code']}")
            print(f"   Expires: {result['expires_at']}")
            return True
        else:
            print(f"❌ Direct create_verification_code returned None")
            return False
            
    except Exception as e:
        print(f"❌ Direct create_verification_code failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_mip_system_imports():
    """Check if mip_system.py has correct imports"""
    print(f"\n🧪 Checking mip_system.py imports")
    print("=" * 60)
    
    try:
        with open('mip_system.py', 'r') as f:
            content = f.read()
        
        # Check for email_verification_service import
        if 'from email_verification_service import' in content:
            print("✅ Found email_verification_service import")
            
            # Extract import line
            import re
            import_match = re.search(r'from email_verification_service import ([^\n]+)', content)
            if import_match:
                imports = import_match.group(1)
                print(f"   Imports: {imports}")
        else:
            print("❌ email_verification_service import NOT found!")
            return False
        
        # Check for EMAIL_SERVICE_AVAILABLE
        if 'EMAIL_SERVICE_AVAILABLE' in content:
            print("✅ Found EMAIL_SERVICE_AVAILABLE check")
        else:
            print("❌ EMAIL_SERVICE_AVAILABLE check NOT found!")
            return False
        
        # Check for send_otp_email usage in register function
        if 'send_otp_email(' in content:
            print("✅ Found send_otp_email usage")
        else:
            print("❌ send_otp_email usage NOT found!")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking mip_system.py: {e}")
        return False

def check_web_server_process():
    """Check what Python processes are running"""
    print(f"\n🧪 Checking web server processes")
    print("=" * 60)
    
    import subprocess
    
    try:
        # Check Python processes
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        lines = result.stdout.split('\n')
        
        python_processes = [line for line in lines if 'python' in line and 'mip_system' in line]
        
        if python_processes:
            print(f"📋 Found {len(python_processes)} Python processes running mip_system:")
            for i, process in enumerate(python_processes, 1):
                print(f"   {i}. {process}")
        else:
            print("❌ No Python processes running mip_system found!")
        
        # Check gunicorn processes
        gunicorn_processes = [line for line in lines if 'gunicorn' in line]
        if gunicorn_processes:
            print(f"\n📋 Found {len(gunicorn_processes)} Gunicorn processes:")
            for i, process in enumerate(gunicorn_processes, 1):
                print(f"   {i}. {process}")
        
        return len(python_processes) > 0 or len(gunicorn_processes) > 0
        
    except Exception as e:
        print(f"❌ Error checking processes: {e}")
        return False

def main():
    """Main debug function"""
    print("🔍 Live Registration Debug Tool")
    print("=" * 70)
    print("This tool will:")
    print("1. Check mip_system.py imports")
    print("2. Check web server processes")
    print("3. Test create_verification_code directly")
    print("4. Test registration API and monitor database")
    print("=" * 70)
    
    # Check mip_system.py
    imports_ok = check_mip_system_imports()
    
    # Check web server
    server_ok = check_web_server_process()
    
    # Test direct function
    direct_ok = test_create_verification_code_directly()
    
    # Test API call
    response, new_codes = test_registration_api_call()
    
    print(f"\n📊 Debug Summary:")
    print(f"   mip_system.py imports: {'✅' if imports_ok else '❌'}")
    print(f"   Web server running: {'✅' if server_ok else '❌'}")
    print(f"   Direct function test: {'✅' if direct_ok else '❌'}")
    print(f"   API registration: {'✅' if response and 'verify-email' in response.url else '❌'}")
    print(f"   Database codes created: {'✅' if new_codes else '❌'}")
    
    if not imports_ok:
        print(f"\n🔧 FIX: Update mip_system.py with correct imports")
    elif not server_ok:
        print(f"\n🔧 FIX: Start web server")
    elif direct_ok and not new_codes:
        print(f"\n🔧 FIX: Web server is running old code - restart needed")
    elif not direct_ok:
        print(f"\n🔧 FIX: email_verification_service.py has issues")
    else:
        print(f"\n🎉 All components working - check web server logs for detailed errors")

if __name__ == "__main__":
    main()
