#!/usr/bin/env python3
"""
Test registration redirect flow
"""

import requests
import re
from urllib.parse import urlparse, parse_qs

def test_registration_redirect():
    """Test registration redirect to login with username"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 Testing registration redirect flow...")
    print("=" * 60)
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    try:
        # Step 1: Get registration page
        print("📄 Step 1: Getting registration page...")
        reg_response = session.get(f"{base_url}/register")
        
        if reg_response.status_code != 200:
            print(f"❌ Failed to get registration page: {reg_response.status_code}")
            return False
        
        print("✅ Registration page loaded successfully")
        
        # Step 2: Submit registration form
        print("\n📝 Step 2: Submitting registration...")
        
        # Test data
        test_data = {
            'username': 'Test.User-123',  # Will be normalized to 'testuser123'
            'email': '<EMAIL>',
            'phone': '0123456789',
            'password': 'password123',
            'confirm_password': 'password123'
        }
        
        print(f"   Username: {test_data['username']}")
        print(f"   Email: {test_data['email']}")
        print(f"   Phone: {test_data['phone']}")
        
        # Submit registration
        reg_submit_response = session.post(f"{base_url}/register", data=test_data, allow_redirects=False)
        
        print(f"   Response status: {reg_submit_response.status_code}")
        
        # Step 3: Check redirect
        if reg_submit_response.status_code == 302:
            print("✅ Registration successful - got redirect")
            
            # Get redirect location
            redirect_url = reg_submit_response.headers.get('Location')
            print(f"   Redirect URL: {redirect_url}")
            
            # Parse redirect URL
            parsed_url = urlparse(redirect_url)
            query_params = parse_qs(parsed_url.query)
            
            print(f"   Path: {parsed_url.path}")
            print(f"   Query params: {query_params}")
            
            # Check if redirected to login
            if parsed_url.path == '/login':
                print("✅ Redirected to login page")
                
                # Check username parameter
                if 'username' in query_params:
                    username = query_params['username'][0]
                    print(f"✅ Username parameter found: '{username}'")
                    
                    # Check if username is normalized
                    expected_username = 'testuser123'  # normalized from 'Test.User-123'
                    if username == expected_username:
                        print(f"✅ Username correctly normalized: '{username}'")
                    else:
                        print(f"❌ Username not normalized correctly. Expected: '{expected_username}', Got: '{username}'")
                        return False
                else:
                    print("❌ Username parameter not found in redirect")
                    return False
                
                # Check success message
                if 'success' in query_params:
                    success_msg = query_params['success'][0]
                    print(f"✅ Success message found: '{success_msg}'")
                else:
                    print("❌ Success message not found in redirect")
                    return False
                
            else:
                print(f"❌ Not redirected to login page. Redirected to: {parsed_url.path}")
                return False
                
        else:
            print(f"❌ Registration failed or no redirect. Status: {reg_submit_response.status_code}")
            print(f"   Response text: {reg_submit_response.text[:500]}...")
            return False
        
        # Step 4: Follow redirect to login page
        print("\n🔗 Step 4: Following redirect to login page...")
        
        login_response = session.get(redirect_url)
        
        if login_response.status_code == 200:
            print("✅ Login page loaded successfully")
            
            # Check if username is pre-filled in the form
            login_html = login_response.text
            
            # Look for username value in input field
            username_pattern = r'<input[^>]*name=["\']username["\'][^>]*value=["\']([^"\']*)["\'][^>]*>'
            username_match = re.search(username_pattern, login_html)
            
            if username_match:
                prefilled_username = username_match.group(1)
                print(f"✅ Username pre-filled in form: '{prefilled_username}'")
                
                if prefilled_username == expected_username:
                    print("✅ Pre-filled username is correct")
                else:
                    print(f"❌ Pre-filled username incorrect. Expected: '{expected_username}', Got: '{prefilled_username}'")
                    return False
            else:
                print("❌ Username not pre-filled in login form")
                return False
            
            # Check for success message in HTML
            if 'alert-success' in login_html and 'Đăng ký thành công' in login_html:
                print("✅ Success message displayed on login page")
            else:
                print("❌ Success message not displayed on login page")
                return False
                
        else:
            print(f"❌ Failed to load login page: {login_response.status_code}")
            return False
        
        print("\n🎉 All tests passed! Registration redirect flow works correctly!")
        return True
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server. Make sure the Flask app is running on http://localhost:5000")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_login_with_prefilled_username():
    """Test login with pre-filled username"""
    
    base_url = "http://localhost:5000"
    
    print("\n🧪 Testing login with pre-filled username...")
    print("=" * 60)
    
    session = requests.Session()
    
    try:
        # Direct access to login with username parameter
        test_username = "testuser123"
        success_msg = "Đăng ký thành công! Vui lòng đăng nhập."
        
        login_url = f"{base_url}/login?username={test_username}&success={success_msg}"
        
        print(f"📄 Accessing login URL: {login_url}")
        
        response = session.get(login_url)
        
        if response.status_code == 200:
            print("✅ Login page loaded successfully")
            
            html = response.text
            
            # Check username pre-fill
            if f'value="{test_username}"' in html:
                print(f"✅ Username '{test_username}' is pre-filled")
            else:
                print(f"❌ Username '{test_username}' is not pre-filled")
                return False
            
            # Check success message
            if 'alert-success' in html and success_msg in html:
                print("✅ Success message is displayed")
            else:
                print("❌ Success message is not displayed")
                return False
            
            print("✅ Login page with pre-filled username works correctly!")
            return True
            
        else:
            print(f"❌ Failed to load login page: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing registration redirect functionality...")
    print("🔧 Make sure Flask app is running on http://localhost:5000")
    print()
    
    # Test 1: Full registration flow
    success1 = test_registration_redirect()
    
    # Test 2: Direct login with parameters
    success2 = test_login_with_prefilled_username()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 All tests passed! Registration redirect is working perfectly!")
    else:
        print("❌ Some tests failed!")
        
    print("\n📋 Summary:")
    print(f"  - Registration redirect: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"  - Login pre-fill: {'✅ PASS' if success2 else '❌ FAIL'}")

if __name__ == "__main__":
    main()
