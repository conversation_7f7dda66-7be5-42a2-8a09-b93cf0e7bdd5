#!/usr/bin/env python3
"""
Debug email verification issues on server
"""

import psycopg2
import psycopg2.extras
from db_config import PG_CONFIG

def check_database_structure():
    """Check if all required tables and columns exist"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🔍 Checking Database Structure")
        print("=" * 50)
        
        # Check EmailVerificationCodes table
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'EmailVerificationCodes'
            )
        ''')
        
        table_exists = cursor.fetchone()[0]
        if table_exists:
            print("✅ EmailVerificationCodes table exists")
            
            # Check table structure
            cursor.execute('''
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns 
                WHERE table_name = 'EmailVerificationCodes'
                ORDER BY ordinal_position
            ''')
            
            print("📋 EmailVerificationCodes columns:")
            for col in cursor.fetchall():
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                print(f"   {col['column_name']}: {col['data_type']} {nullable}")
        else:
            print("❌ EmailVerificationCodes table MISSING!")
            return False
        
        # Check Users table email columns
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' 
            AND column_name IN ('email', 'email_verified', 'email_verified_at')
            ORDER BY column_name
        ''')
        
        user_columns = cursor.fetchall()
        print(f"\n📋 Users table email columns:")
        
        required_columns = ['email', 'email_verified', 'email_verified_at']
        existing_columns = [col['column_name'] for col in user_columns]
        
        for col in user_columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            print(f"   ✅ {col['column_name']}: {col['data_type']} {nullable}")
        
        missing_columns = [col for col in required_columns if col not in existing_columns]
        if missing_columns:
            print(f"❌ Missing columns in Users table: {missing_columns}")
            return False
        
        # Check SystemConfig email settings
        cursor.execute('''
            SELECT config_key, config_value
            FROM "SystemConfig"
            WHERE config_key IN (
                'mailtrap_api_token',
                'mailtrap_sender_email', 
                'mailtrap_sender_name',
                'email_verification_enabled',
                'otp_expiry_minutes',
                'max_otp_attempts'
            )
            ORDER BY config_key
        ''')
        
        configs = cursor.fetchall()
        print(f"\n📋 Email configurations:")
        
        if configs:
            for config in configs:
                value = config['config_value']
                if 'token' in config['config_key'].lower():
                    value = value[:10] + '...' if len(value) > 10 else value
                print(f"   ✅ {config['config_key']}: {value}")
        else:
            print("❌ No email configurations found!")
            return False
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_create_verification_code():
    """Test creating verification code"""
    try:
        print(f"\n🧪 Testing create_verification_code function")
        print("=" * 50)
        
        # Import and test
        from email_verification_service import create_verification_code
        
        test_email = "<EMAIL>"
        result = create_verification_code(test_email, 'registration')
        
        if result:
            print(f"✅ create_verification_code working!")
            print(f"   Code ID: {result['code_id']}")
            print(f"   Code: {result['code']}")
            print(f"   Expires: {result['expires_at']}")
            print(f"   Max attempts: {result['max_attempts']}")
            return True
        else:
            print(f"❌ create_verification_code returned None")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   → email_verification_service.py missing or has syntax errors")
        return False
    except Exception as e:
        print(f"❌ Function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_email_config():
    """Test email configuration loading"""
    try:
        print(f"\n🧪 Testing get_email_config function")
        print("=" * 50)
        
        from email_verification_service import get_email_config
        
        config = get_email_config()
        
        if config:
            print(f"✅ get_email_config working!")
            for key, value in config.items():
                if 'token' in key.lower():
                    display_value = value[:10] + '...' if len(value) > 10 else value
                else:
                    display_value = value
                print(f"   {key}: {display_value}")
            return True
        else:
            print(f"❌ get_email_config returned None")
            return False
            
    except Exception as e:
        print(f"❌ Email config test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_full_registration_flow():
    """Test the full registration OTP flow"""
    try:
        print(f"\n🧪 Testing full registration flow")
        print("=" * 50)
        
        from email_verification_service import send_otp_email
        
        test_email = "<EMAIL>"
        result = send_otp_email(test_email, 'registration')
        
        print(f"send_otp_email result: {result}")
        
        if result['success']:
            print(f"✅ Full registration flow working!")
            print(f"   Code ID: {result['code_id']}")
            print(f"   Expires: {result['expires_at']}")
            return True
        else:
            print(f"❌ Registration flow failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Registration flow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_fix_instructions():
    """Show instructions to fix common issues"""
    print(f"\n🔧 Fix Instructions")
    print("=" * 50)
    print("1. If EmailVerificationCodes table missing:")
    print("   → Run: python3 deploy_email_verification_system.py")
    print()
    print("2. If Users table columns missing:")
    print("   → Run migration script to add email_verified columns")
    print()
    print("3. If email configs missing:")
    print("   → Go to /config page and set up email settings")
    print()
    print("4. If import errors:")
    print("   → Upload email_verification_service.py to server")
    print("   → Install: pip install mailtrap")
    print()
    print("5. If database connection fails:")
    print("   → Check db_config.py settings")
    print("   → Verify PostgreSQL is running")

def main():
    """Main debug function"""
    print("🔍 Email Verification Debug Tool")
    print("=" * 60)
    
    # Check database structure
    db_ok = check_database_structure()
    
    if not db_ok:
        print("\n❌ Database structure issues found!")
        show_fix_instructions()
        return
    
    # Test email config
    config_ok = test_email_config()
    
    # Test create verification code
    create_ok = test_create_verification_code()
    
    # Test full flow
    flow_ok = test_full_registration_flow()
    
    if db_ok and config_ok and create_ok and flow_ok:
        print(f"\n🎉 All tests passed! Email verification should work.")
    else:
        print(f"\n❌ Some tests failed. Check the issues above.")
        show_fix_instructions()

if __name__ == "__main__":
    main()
