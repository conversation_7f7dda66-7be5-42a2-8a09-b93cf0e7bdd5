<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>Đặt lại mật khẩu - MIP System</title>
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='images/logo.png') }}">

    <!-- CoreUI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/css/coreui.min.css">
    <!-- CoreUI Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/icons@2.1.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .reset-container {
            width: 100%;
            max-width: 450px;
            padding: 20px;
        }

        .reset-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .reset-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .reset-header {
            background: linear-gradient(135deg, #00C6AE 0%, #00A693 100%);
            padding: 30px 20px;
            text-align: center;
            color: white;
        }

        .reset-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .reset-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .reset-body {
            padding: 40px 30px;
        }

        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            outline: none;
            border-color: #00C6AE;
            box-shadow: 0 0 0 3px rgba(0, 198, 174, 0.1);
            background: white;
        }

        .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .btn-reset {
            background: linear-gradient(135deg, #00C6AE 0%, #00A693 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 12px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-reset:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 198, 174, 0.3);
        }

        .btn-reset:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }

        .alert {
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .email-display {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            margin-bottom: 25px;
        }

        .email-display strong {
            color: #00C6AE;
            font-size: 1.1rem;
        }

        .password-requirements {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .password-requirements h6 {
            color: #00C6AE;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .password-requirements ul {
            margin: 0;
            padding-left: 20px;
        }

        .password-requirements li {
            margin-bottom: 5px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .password-strength {
            height: 4px;
            background: #e9ecef;
            border-radius: 2px;
            margin-top: 8px;
            overflow: hidden;
        }

        .password-strength-bar {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }

        .strength-weak { background: #dc3545; width: 25%; }
        .strength-fair { background: #fd7e14; width: 50%; }
        .strength-good { background: #ffc107; width: 75%; }
        .strength-strong { background: #28a745; width: 100%; }

        .back-link {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .back-link a {
            color: #6c757d;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .back-link a:hover {
            color: #00C6AE;
        }
    </style>
</head>
<body>
    <div class="reset-container">
        <div class="reset-card">
            <div class="reset-header">
                <h1><i class="cil-lock-unlocked me-2"></i>Đặt lại mật khẩu</h1>
                <p>Tạo mật khẩu mới cho tài khoản của bạn</p>
            </div>
            <div class="reset-body">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="cil-warning me-2"></i>{{ error }}
                </div>
                {% endif %}

                <div class="email-display">
                    <p class="mb-1">Đặt lại mật khẩu cho:</p>
                    <strong>{{ email }}</strong>
                </div>

                <div class="password-requirements">
                    <h6><i class="cil-shield-alt me-2"></i>Yêu cầu mật khẩu:</h6>
                    <ul>
                        <li>Ít nhất 6 ký tự</li>
                        <li>Nên có chữ hoa, chữ thường</li>
                        <li>Nên có số và ký tự đặc biệt</li>
                    </ul>
                </div>

                <form method="POST" id="resetForm">
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="cil-lock-locked me-2"></i>Mật khẩu mới
                        </label>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="Nhập mật khẩu mới" required>
                        <div class="password-strength">
                            <div class="password-strength-bar" id="strengthBar"></div>
                        </div>
                        <div class="form-text" id="strengthText">Độ mạnh mật khẩu</div>
                    </div>

                    <div class="mb-4">
                        <label for="confirm_password" class="form-label">
                            <i class="cil-lock-locked me-2"></i>Xác nhận mật khẩu
                        </label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               placeholder="Nhập lại mật khẩu mới" required>
                        <div class="form-text" id="confirmText"></div>
                    </div>

                    <button type="submit" class="btn btn-reset" id="submitBtn">
                        <span id="submitSpinner" class="spinner-border spinner-border-sm d-none me-2"></span>
                        <i id="submitIcon" class="cil-check me-2"></i>
                        Đặt lại mật khẩu
                    </button>
                </form>

                <div class="back-link">
                    <a href="/login">
                        <i class="cil-arrow-left me-1"></i>Quay lại đăng nhập
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- CoreUI JS -->
    <script src="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/js/coreui.bundle.min.js"></script>

    <script>
        const passwordInput = document.getElementById('password');
        const confirmInput = document.getElementById('confirm_password');
        const strengthBar = document.getElementById('strengthBar');
        const strengthText = document.getElementById('strengthText');
        const confirmText = document.getElementById('confirmText');

        // Password strength checker
        function checkPasswordStrength(password) {
            let strength = 0;
            let feedback = [];

            if (password.length >= 6) strength += 1;
            else feedback.push('Ít nhất 6 ký tự');

            if (/[a-z]/.test(password)) strength += 1;
            if (/[A-Z]/.test(password)) strength += 1;
            if (/[0-9]/.test(password)) strength += 1;
            if (/[^A-Za-z0-9]/.test(password)) strength += 1;

            return { strength, feedback };
        }

        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const result = checkPasswordStrength(password);
            
            // Update strength bar
            strengthBar.className = 'password-strength-bar';
            if (result.strength <= 1) {
                strengthBar.classList.add('strength-weak');
                strengthText.textContent = 'Mật khẩu yếu';
                strengthText.style.color = '#dc3545';
            } else if (result.strength <= 2) {
                strengthBar.classList.add('strength-fair');
                strengthText.textContent = 'Mật khẩu trung bình';
                strengthText.style.color = '#fd7e14';
            } else if (result.strength <= 3) {
                strengthBar.classList.add('strength-good');
                strengthText.textContent = 'Mật khẩu tốt';
                strengthText.style.color = '#ffc107';
            } else {
                strengthBar.classList.add('strength-strong');
                strengthText.textContent = 'Mật khẩu mạnh';
                strengthText.style.color = '#28a745';
            }

            // Check confirm password
            checkPasswordMatch();
        });

        confirmInput.addEventListener('input', checkPasswordMatch);

        function checkPasswordMatch() {
            const password = passwordInput.value;
            const confirm = confirmInput.value;

            if (confirm === '') {
                confirmText.textContent = '';
                confirmText.style.color = '';
            } else if (password === confirm) {
                confirmText.textContent = '✓ Mật khẩu khớp';
                confirmText.style.color = '#28a745';
            } else {
                confirmText.textContent = '✗ Mật khẩu không khớp';
                confirmText.style.color = '#dc3545';
            }
        }

        document.getElementById('resetForm').addEventListener('submit', function(e) {
            const password = passwordInput.value;
            const confirm = confirmInput.value;
            const submitBtn = document.getElementById('submitBtn');
            const submitSpinner = document.getElementById('submitSpinner');
            const submitIcon = document.getElementById('submitIcon');

            // Validation
            if (password.length < 6) {
                e.preventDefault();
                alert('Mật khẩu phải có ít nhất 6 ký tự');
                return;
            }

            if (password !== confirm) {
                e.preventDefault();
                alert('Mật khẩu xác nhận không khớp');
                return;
            }

            // Show loading state
            submitBtn.disabled = true;
            submitSpinner.classList.remove('d-none');
            submitIcon.classList.add('d-none');
        });

        // Auto focus password input
        document.addEventListener('DOMContentLoaded', function() {
            passwordInput.focus();
        });
    </script>
</body>
</html>
