<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>Đ<PERSON>ng nhập - SAPMMO System</title>
    
    <!-- CoreUI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/css/coreui.min.css">
    <!-- CoreUI Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/icons@2.1.0/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }
        
        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 15px;
        }
        
        .login-card {
            border-radius: 0.5rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .login-header {
            background-color: #3c4b64;
            padding: 2rem 1rem;
            text-align: center;
            color: white;
        }
        
        .login-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: bold;
        }
        
        .login-body {
            padding: 2rem;
            background-color: white;
        }
        
        .form-floating {
            margin-bottom: 1.5rem;
        }
        
        .form-floating > label {
            padding-left: 1rem;
        }
        
        .form-control:focus {
            box-shadow: none;
            border-color: #3c4b64;
        }
        
        .btn-login {
            background-color: #3c4b64;
            border-color: #3c4b64;
            width: 100%;
            padding: 0.75rem;
            font-weight: 500;
        }
        
        .btn-login:hover, .btn-login:focus {
            background-color: #303c54;
            border-color: #303c54;
        }
        
        .alert {
            margin-bottom: 1.5rem;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 1.5rem;
            color: #6c757d;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>MIP SYSTEM</h1>
            </div>
            <div class="login-body">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    {{ error }}
                </div>
                {% endif %}
                
                <form method="POST">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username" placeholder="Tên đăng nhập" required>
                        <label for="username">Tên đăng nhập</label>
                    </div>
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" placeholder="Mật khẩu" required>
                        <label for="password">Mật khẩu</label>
                    </div>
                    <button type="submit" class="btn btn-primary btn-login">Đăng nhập</button>
                </form>
            </div>
        </div>
        <div class="login-footer">
            <p>&copy; 2023 MIP System. All rights reserved.</p>
        </div>
    </div>
    
    <!-- CoreUI JS -->
    <script src="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/js/coreui.bundle.min.js"></script>
</body>
</html>
