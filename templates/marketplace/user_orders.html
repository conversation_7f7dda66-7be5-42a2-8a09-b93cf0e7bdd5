{% extends "base_coreui.html" %}
{% block title %}Đ<PERSON><PERSON> hàng của tôi - Marketplace{% endblock %}

{% block head_extra %}
<style>
    .order-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }
    .order-card:hover {
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #e9ecef;
    }
    .order-number {
        font-weight: 600;
        color: #00C6AE;
        font-size: 1.1rem;
    }
    .order-status {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 500;
    }
    .status-completed {
        background: #d4edda;
        color: #155724;
    }
    .status-pending {
        background: #fff3cd;
        color: #856404;
    }
    .status-cancelled {
        background: #f8d7da;
        color: #721c24;
    }
    .account-table {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .account-row {
        transition: all 0.3s ease;
    }
    .account-row:hover {
        background-color: #f8f9fa;
    }
    .warranty-badge {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    .warranty-active {
        background: #d4edda;
        color: #155724;
    }
    .warranty-expired {
        background: #f8d7da;
        color: #721c24;
    }
    .warranty-expiring {
        background: #fff3cd;
        color: #856404;
    }
    .export-section {
        background: white;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
        border: 2px dashed #dee2e6;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/marketplace">Marketplace</a></li>
                    <li class="breadcrumb-item active">Đơn hàng của tôi</li>
                </ol>
            </nav>
            <h2><i class="cil-cart text-primary"></i> Đơn hàng của tôi</h2>
        </div>
    </div>

    <!-- Product Type Tabs -->
    <div class="card mb-4">
        <div class="card-header">
            <ul class="nav nav-tabs card-header-tabs" id="productTypeTabs" role="tablist">
                <!-- Dynamic tabs will be loaded here -->
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="productTypeTabContent">
                <!-- Dynamic tabs content will be loaded here -->
                
                <!-- Account Package Tab (Special handling) -->
                <div class="tab-pane fade" id="account-package" role="tabpanel">
                    <!-- Filter Section -->
                    <div class="filter-section">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">Tìm theo tên</label>
                                <input type="text" class="form-control" id="accountNameFilter" placeholder="Tên account..." onkeyup="filterAccounts()">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Đơn hàng</label>
                                <select class="form-select" id="orderFilter" onchange="filterAccounts()">
                                    <option value="">Tất cả đơn hàng</option>
                                    <!-- Will be populated dynamically -->
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Gói sản phẩm</label>
                                <select class="form-select" id="packageFilter" onchange="filterAccounts()">
                                    <option value="">Tất cả gói</option>
                                    <!-- Will be populated dynamically -->
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button class="btn btn-primary w-100" onclick="filterAccounts()">
                                    <i class="cil-filter"></i> Lọc
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Account Table -->
                    <div class="account-table">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5><i class="cil-list"></i> Danh sách tài khoản đã mua</h5>
                            <div>
                                <button class="btn btn-outline-primary me-2" onclick="selectAllAccounts()">
                                    <i class="cil-check-alt"></i> Chọn tất cả
                                </button>
                                <button class="btn btn-success" onclick="showExportModal()">
                                    <i class="cil-cloud-download"></i> Xuất dữ liệu
                                </button>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="accountsTable">
                                <thead>
                                    <tr>
                                        <th style="width: 40px;">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                            </div>
                                        </th>
                                        <th>ID</th>
                                        <th>Tên</th>
                                        <th>Trạng thái</th>
                                        <th>Follower</th>
                                        <th>Like</th>
                                        <th>Gói sản phẩm</th>
                                        <th>Đơn hàng</th>
                                        <th>Ngày mua</th>
                                        <th style="min-width: 140px;">Thời hạn bảo hành</th>
                                        <th>Hành động</th>
                                    </tr>
                                </thead>
                                <tbody id="accountsTableBody">
                                    <!-- Account data will be loaded here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination and Controls -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div class="d-flex align-items-center">
                                <span class="me-2">Hiển thị:</span>
                                <select id="accountPageSize" class="form-select form-select-sm" style="width: auto;" onchange="changeAccountPageSize()">
                                    <option value="10">10</option>
                                    <option value="20" selected>20</option>
                                    <option value="50">50</option>
                                    <option value="100">100</option>
                                </select>
                                <span class="ms-2">tài khoản/trang</span>
                            </div>

                            <div class="d-flex align-items-center">
                                <span id="accountsInfo" class="me-3">Hiển thị 0 - 0 của 0 tài khoản</span>
                                <span id="selectedAccountsInfo" class="badge bg-primary me-3" style="display: none;">Đã chọn: 0</span>
                            </div>

                            <nav aria-label="Accounts pagination">
                                <ul id="accountsPagination" class="pagination pagination-sm mb-0">
                                    <!-- Pagination will be generated here -->
                                </ul>
                            </nav>
                        </div>

                        <!-- Pagination -->
                        <div id="accountsPagination" class="d-flex justify-content-center mt-4"></div>
                    </div>
                </div>

                <!-- Other Product Type Tabs -->
                <div class="tab-pane fade" id="other-products" role="tabpanel">
                    <div id="otherProductsContainer">
                        <!-- Other product orders will be displayed here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Warranty Tab Content -->
        <div class="tab-pane fade" id="warranty" role="tabpanel">
            <div class="account-table">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="cil-shield-alt"></i> Yêu cầu bảo hành của tôi</h5>
                    <div>
                        <select id="warrantyStatusFilter" class="form-select form-select-sm" style="width: auto;" onchange="loadUserWarrantyRequests()">
                            <option value="">Tất cả trạng thái</option>
                            <option value="Pending">Chờ xử lý</option>
                            <option value="Approved">Đã chấp nhận</option>
                            <option value="Rejected">Đã từ chối</option>
                            <option value="Replaced">Đã thay thế</option>
                        </select>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Tài khoản</th>
                                <th>Lý do</th>
                                <th>Trạng thái</th>
                                <th>Ngày yêu cầu</th>
                                <th>Ngày xử lý</th>
                                <th>Hành động</th>
                            </tr>
                        </thead>
                        <tbody id="warrantyRequestsTableBody">
                            <!-- Warranty requests will be loaded here -->
                        </tbody>
                    </table>
                </div>

                <!-- Warranty Pagination -->
                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span id="warrantyRequestsInfo">Hiển thị 0 - 0 của 0 yêu cầu</span>
                    </div>
                    <nav aria-label="Warranty requests pagination">
                        <ul id="warrantyRequestsPagination" class="pagination pagination-sm mb-0">
                            <!-- Pagination will be generated here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xuất dữ liệu tài khoản</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Chọn các cột dữ liệu muốn xuất:</p>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="export_id" checked>
                            <label class="form-check-label" for="export_id">ID</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="export_name" checked>
                            <label class="form-check-label" for="export_name">Tên tài khoản</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="export_email" checked>
                            <label class="form-check-label" for="export_email">Email</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="export_full_email" checked>
                            <label class="form-check-label" for="export_full_email">Full Email</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="export_full_info" checked>
                            <label class="form-check-label" for="export_full_info">Full Info</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="export_follower">
                            <label class="form-check-label" for="export_follower">Follower</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="export_package">
                            <label class="form-check-label" for="export_package">Gói sản phẩm</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="export_order">
                            <label class="form-check-label" for="export_order">Đơn hàng</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-success" onclick="exportSelectedAccounts()">
                    <i class="cil-cloud-download"></i> Xuất Excel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Warranty Request Modal -->
<div class="modal fade" id="warrantyRequestModal" tabindex="-1" aria-labelledby="warrantyRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="warrantyRequestModalLabel">Yêu cầu bảo hành tài khoản</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="warrantyRequestForm" enctype="multipart/form-data">
                    <input type="hidden" id="warrantyAccountId" name="account_id">

                    <div class="mb-3">
                        <label for="warrantyReason" class="form-label">Lý do bảo hành <span class="text-danger">*</span></label>
                        <select class="form-select" id="warrantyReason" name="reason" required>
                            <option value="">Chọn lý do bảo hành</option>
                            <option value="Account không tồn tại">Account không tồn tại</option>
                            <option value="Sai Password">Sai Password</option>
                            <option value="Sai Mail">Sai Mail</option>
                            <option value="Thu Giỏ">Thu Giỏ</option>
                            <option value="Die giỏ">Die giỏ</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="warrantyDescription" class="form-label">Mô tả chi tiết</label>
                        <textarea class="form-control" id="warrantyDescription" name="description" rows="3"
                                  placeholder="Mô tả chi tiết vấn đề gặp phải..."></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="warrantyEvidence" class="form-label">Hình ảnh minh chứng <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="warrantyEvidence" name="evidence_image"
                               accept="image/*" required>
                        <div class="form-text">Chỉ chấp nhận file ảnh (JPG, PNG, GIF). Tối đa 5MB.</div>
                    </div>

                    <div id="warrantyImagePreview" class="mb-3" style="display: none;">
                        <img id="warrantyPreviewImg" src="" alt="Preview" class="img-thumbnail" style="max-width: 200px;">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="submitWarrantyRequest()">
                    <span id="warrantySubmitSpinner" class="spinner-border spinner-border-sm d-none" role="status"></span>
                    Gửi yêu cầu
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables
let currentProductType = 'all';
let userAccounts = [];
let selectedAccountIds = [];
let currentPage = 1;
let pageSize = 20;

// Initialize page
$(document).ready(function() {
    loadProductTypeTabs();
});

function loadProductTypeTabs() {
    const tabsContainer = document.getElementById('productTypeTabs');

    fetch('/api/marketplace/user/product-types')
        .then(response => response.json())
        .then(data => {
            if (data.success) {

                console.log('📋 Product types loaded:', data.product_types);

                // Add product type tabs
                data.product_types.forEach((type, index) => {
                    const tabId = type.toLowerCase().replace(/\s+/g, '-');
                    const isFirst = index === 0;

                    // Create tab content div if it doesn't exist
                    const tabContentContainer = document.getElementById('productTypeTabContent');
                    if (!document.getElementById(tabId)) {
                        const tabContentHTML = `
                            <div class="tab-pane fade ${isFirst ? 'show active' : ''}" id="${tabId}" role="tabpanel">
                                <div id="${tabId}Container">
                                    <!-- Content will be loaded here -->
                                </div>
                            </div>
                        `;
                        tabContentContainer.insertAdjacentHTML('beforeend', tabContentHTML);
                    }

                    const displayName = data.type_names ? data.type_names[type] : type;
                    const tabHTML = `
                        <li class="nav-item" role="presentation">
                            <button class="nav-link ${isFirst ? 'active' : ''}" id="${tabId}-tab" data-coreui-toggle="tab" data-coreui-target="#${tabId}"
                                    type="button" role="tab" onclick="loadOrdersByType('${type}')">
                                ${displayName} (${data.counts[type] || 0})
                            </button>
                        </li>
                    `;
                    tabsContainer.insertAdjacentHTML('beforeend', tabHTML);
                });

                // Load first tab content
                if (data.product_types.length > 0) {
                    loadOrdersByType(data.product_types[0]);
                }
            }

            // Add warranty tab (HIDDEN per user request)
            // Tab "Bảo hành" đã được ẩn theo yêu cầu user
            // Tính năng bảo hành vẫn hoạt động, chỉ ẩn tab này
            /*
            if (data.success) {
                const warrantyTabHTML = `
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="warranty-tab" data-coreui-toggle="tab" data-coreui-target="#warranty"
                                type="button" role="tab" onclick="loadUserWarrantyRequests()">
                            Bảo hành (<span id="warrantyCount">0</span>)
                        </button>
                    </li>
                `;
                tabsContainer.insertAdjacentHTML('beforeend', warrantyTabHTML);

                // Load warranty count
                loadWarrantyCount();
            }
            */
        })
        .catch(error => console.error('Error loading product types:', error));
}

function loadOrdersByType(productType) {
    currentProductType = productType;

    console.log('🔍 Loading orders for product type:', productType);

    // Hide all tab content first
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('show', 'active');
    });

    // Special handling for "account" product type - show table format
    if (productType === 'account') {
        console.log('📋 Loading Account table format for product type: account');
        // Show account package tab (special table handling)
        const accountTab = document.getElementById('account-package');
        if (accountTab) {
            accountTab.classList.add('show', 'active');
            loadUserAccounts();
            loadFilterOptions();
        } else {
            console.error('Account package tab not found');
        }
    } else {
        // Show dynamic product type tab
        const tabId = productType.toLowerCase().replace(/\s+/g, '-');
        const dynamicTab = document.getElementById(tabId);
        if (dynamicTab) {
            dynamicTab.classList.add('show', 'active');
            loadRegularOrdersInTab(productType, tabId + 'Container');
        } else {
            console.error('Tab not found for product type:', productType);
        }
    }
}

function loadUserAccounts(page = 1) {
    currentPage = page;

    const params = new URLSearchParams({
        page: page,
        page_size: pageSize,
        name_filter: document.getElementById('accountNameFilter')?.value || '',
        order_filter: document.getElementById('orderFilter')?.value || '',
        package_filter: document.getElementById('packageFilter')?.value || ''
    });

    fetch(`/api/marketplace/user/accounts?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                userAccounts = data.accounts;
                displayUserAccounts();
                updateAccountsPagination(data.pagination);
                updateAccountsInfo(data.pagination);
            } else {
                console.error('Error loading user accounts:', data.error);
            }
        })
        .catch(error => console.error('Error:', error));
}

function changeAccountPageSize() {
    pageSize = parseInt(document.getElementById('accountPageSize').value);
    loadUserAccounts(1); // Reset to first page
}

function updateAccountsInfo(pagination) {
    const start = (pagination.current_page - 1) * pagination.page_size + 1;
    const end = Math.min(pagination.current_page * pagination.page_size, pagination.total);
    const total = pagination.total;

    document.getElementById('accountsInfo').textContent =
        `Hiển thị ${start} - ${end} của ${total} tài khoản`;
}

function updateAccountsPagination(pagination) {
    const paginationContainer = document.getElementById('accountsPagination');
    paginationContainer.innerHTML = '';

    if (pagination.total_pages <= 1) return;

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${pagination.current_page === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadUserAccounts(${pagination.current_page - 1})">‹</a>`;
    paginationContainer.appendChild(prevLi);

    // Page numbers
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === pagination.current_page ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadUserAccounts(${i})">${i}</a>`;
        paginationContainer.appendChild(li);
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadUserAccounts(${pagination.current_page + 1})">›</a>`;
    paginationContainer.appendChild(nextLi);
}

function loadAccountProducts() {
    fetch('/api/marketplace/user/account-products')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const packageFilter = document.getElementById('packageFilter');
                packageFilter.innerHTML = '<option value="">Tất cả gói</option>';

                data.products.forEach(product => {
                    const option = document.createElement('option');
                    option.value = product.product_id;
                    option.textContent = product.name;
                    packageFilter.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading products:', error));
}

function displayUserAccounts() {
    const tbody = document.getElementById('accountsTableBody');
    tbody.innerHTML = '';
    
    if (userAccounts.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="12" class="text-center text-muted py-4">
                    <i class="cil-info"></i> Chưa có tài khoản nào
                </td>
            </tr>
        `;
        return;
    }
    
    userAccounts.forEach(account => {
        const row = createAccountRow(account);
        tbody.appendChild(row);
    });
}

function createAccountRow(account) {
    const tr = document.createElement('tr');
    tr.className = 'account-row';
    
    const warrantyStatus = getWarrantyStatus(account.warranty_end_date);
    const warrantyBadge = `<span class="warranty-badge warranty-${warrantyStatus.class}">${warrantyStatus.text}</span>`;
    
    const accountName = account.account_name ?
        `<a href="https://www.tiktok.com/@${account.account_name}" target="_blank" class="text-decoration-none text-primary fw-bold">
            ${account.account_name}
        </a>` : 'N/A';

    tr.innerHTML = `
        <td>
            <div class="form-check">
                <input class="form-check-input account-checkbox" type="checkbox" value="${account.account_id}"
                       onchange="updateSelectedAccounts()">
            </div>
        </td>
        <td>${account.account_id}</td>
        <td>${accountName}</td>
        <td><span class="badge bg-primary">${account.status || 'N/A'}</span></td>
        <td>${account.follower_count ? account.follower_count.toLocaleString() : 'N/A'}</td>
        <td>${account.like_count ? account.like_count.toLocaleString() : 'N/A'}</td>
        <td><span class="badge bg-info">${account.product_name || 'N/A'}</span></td>
        <td><a href="#" class="text-decoration-none">#${account.order_number || 'N/A'}</a></td>
        <td>${account.purchase_date ? new Date(account.purchase_date).toLocaleString('vi-VN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }) : 'N/A'}</td>
        <td>${warrantyBadge}</td>
        <td>
            <div class="btn-group" role="group">
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="getEmailCode(${account.account_id})" title="Lấy mã mail">
                    <i class="cil-envelope-closed"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="editAccount(${account.account_id})" title="Edit">
                    <i class="cil-pencil"></i>
                </button>
                ${getWarrantyButton(account)}
            </div>
        </td>
    `;
    
    return tr;
}

function getWarrantyButton(account) {
    // Check if account has pending warranty request
    if (account.has_pending_warranty) {
        return `
            <button type="button" class="btn btn-sm btn-outline-info"
                    onclick="showWarrantyStatus(${account.account_id})"
                    title="Đang yêu cầu bảo hành">
                <i class="cil-shield-alt"></i> Đang xử lý
            </button>
        `;
    }

    // Check if warranty expired
    if (account.warranty_expired) {
        return `
            <button type="button" class="btn btn-sm btn-outline-secondary disabled"
                    title="Hết thời hạn bảo hành" disabled>
                <i class="cil-shield-alt"></i> Hết hạn
            </button>
        `;
    }

    // Normal warranty button
    return `
        <button type="button" class="btn btn-sm btn-outline-warning"
                onclick="requestWarranty(${account.account_id})"
                title="Yêu cầu bảo hành">
            <i class="cil-shield-alt"></i> Bảo hành
        </button>
    `;
}

function getWarrantyStatus(warrantyEndDate) {
    if (!warrantyEndDate) return { class: 'expired', text: 'Hết hạn' };

    // Database already in VN timezone, no conversion needed
    const now = new Date();
    const endDate = new Date(warrantyEndDate);

    const hoursLeft = Math.ceil((endDate - now) / (1000 * 60 * 60)); // Calculate in hours

    if (hoursLeft < 0) return { class: 'expired', text: 'Hết hạn' };

    // Convert hours to days and hours for display
    if (hoursLeft < 24) {
        return { class: 'expiring', text: `${hoursLeft}h` };
    } else {
        const daysLeft = Math.floor(hoursLeft / 24);
        const remainingHours = hoursLeft % 24;
        if (remainingHours > 0) {
            return { class: daysLeft <= 1 ? 'expiring' : 'active', text: `${daysLeft}d ${remainingHours}h` };
        } else {
            return { class: daysLeft <= 1 ? 'expiring' : 'active', text: `${daysLeft} ngày` };
        }
    }
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const accountCheckboxes = document.querySelectorAll('.account-checkbox');

    accountCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedAccounts();
}

function updateSelectedAccounts() {
    const selectedCheckboxes = document.querySelectorAll('.account-checkbox:checked');
    const selectedCount = selectedCheckboxes.length;
    const totalCheckboxes = document.querySelectorAll('.account-checkbox');

    // Update selectedAccountIds array
    selectedAccountIds = Array.from(selectedCheckboxes).map(cb => parseInt(cb.value));

    // Update select all checkbox state
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    if (selectedCount === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (selectedCount === totalCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }

    // Update selected count display
    const selectedInfo = document.getElementById('selectedAccountsInfo');
    if (selectedCount > 0) {
        selectedInfo.textContent = `Đã chọn: ${selectedCount}`;
        selectedInfo.style.display = 'inline-block';
    } else {
        selectedInfo.style.display = 'none';
    }
}

function loadRegularOrders(productType) {
    const container = document.getElementById('allOrdersContainer');
    loadRegularOrdersInTab(productType, container);
}

function loadVideoOrders(containerId) {
    const container = typeof containerId === 'string' ? document.getElementById(containerId) : containerId;
    if (!container) {
        console.error('Container not found:', containerId);
        return;
    }

    container.innerHTML = '<div class="text-center py-4"><i class="cil-reload fa-spin"></i> Đang tải video links...</div>';

    fetch('/api/marketplace/user/video-orders')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayVideoOrders(data.video_orders, container);
            } else {
                container.innerHTML = '<div class="text-center py-4 text-muted">Lỗi tải dữ liệu video</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            container.innerHTML = '<div class="text-center py-4 text-muted">Lỗi kết nối</div>';
        });
}

function displayVideoOrders(videoOrders, container) {
    if (videoOrders.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="cil-video fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Chưa có video nào</h5>
                <p class="text-muted">Hãy mua sản phẩm video để có video links đầu tiên</p>
                <a href="/marketplace" class="btn btn-primary">
                    <i class="cil-cart"></i> Đi mua video
                </a>
            </div>
        `;
        return;
    }

    container.innerHTML = '';

    // Group by order
    const orderGroups = {};
    videoOrders.forEach(item => {
        if (!orderGroups[item.order_number]) {
            orderGroups[item.order_number] = {
                order_info: item,
                video_links: []
            };
        }
        orderGroups[item.order_number].video_links.push(...item.assigned_video_links);
    });

    Object.values(orderGroups).forEach(orderGroup => {
        const orderCard = createVideoOrderCard(orderGroup);
        container.appendChild(orderCard);
    });
}

function createVideoOrderCard(orderGroup) {
    const order = orderGroup.order_info;
    const videoLinks = orderGroup.video_links;

    const div = document.createElement('div');
    div.className = 'card mb-4';

    const statusClass = `status-${order.status}`;
    const statusText = {
        'completed': 'Hoàn thành',
        'pending': 'Đang xử lý',
        'cancelled': 'Đã hủy'
    }[order.status] || order.status;

    div.innerHTML = `
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-0">
                        <i class="cil-video text-primary me-2"></i>
                        Đơn hàng #${order.order_number}
                    </h6>
                    <small class="text-muted">
                        <i class="cil-clock me-1"></i>
                        ${new Date(order.created_at).toLocaleDateString('vi-VN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit'
                        })}
                    </small>
                </div>
                <div class="text-end">
                    <span class="badge bg-success">${statusText}</span>
                    <div class="mt-1">
                        <strong class="text-primary">${order.final_amount.toLocaleString()} MP</strong>
                    </div>
                </div>
            </div>
        </div>

        <div class="card-body">
            <div class="mb-3">
                <strong class="text-dark">${order.product_name}</strong>
                <span class="badge bg-info ms-2">${videoLinks.length} video links</span>
            </div>

            <div class="row">
                ${videoLinks.map(link => `
                    <div class="col-md-6 mb-3">
                        <div class="card border-primary">
                            <div class="card-body p-3">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="card-title mb-0 text-primary">
                                        <i class="cil-folder me-1"></i>
                                        ${link.name}
                                    </h6>
                                    <span class="badge bg-primary">${link.video_count} videos</span>
                                </div>

                                <p class="card-text text-muted small mb-2">
                                    ${link.description || 'Không có mô tả'}
                                </p>

                                <div class="mb-2">
                                    <span class="badge bg-info">${link.video_type || 'Chưa phân loại'}</span>
                                </div>

                                <div class="d-grid">
                                    <a href="${link.drive_url}" target="_blank" class="btn btn-primary btn-sm">
                                        <i class="cil-cloud-download me-1"></i>
                                        Truy cập Google Drive
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;

    return div;
}



function loadRegularOrdersInTab(productType, containerId) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error('Container not found:', containerId);
        return;
    }

    container.innerHTML = '<div class="text-center py-4"><i class="cil-reload fa-spin"></i> Đang tải...</div>';

    fetch(`/api/marketplace/user/orders?product_type=${encodeURIComponent(productType)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayRegularOrdersInContainer(data.orders, container);
            } else {
                container.innerHTML = '<div class="text-center py-4 text-muted">Lỗi tải dữ liệu</div>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            container.innerHTML = '<div class="text-center py-4 text-muted">Lỗi kết nối</div>';
        });
}

function displayRegularOrders(orders) {
    const container = document.getElementById('allOrdersContainer');
    displayRegularOrdersInContainer(orders, container);
}

function displayRegularOrdersInContainer(orders, container) {
    if (orders.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="cil-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Chưa có đơn hàng nào</h5>
                <p class="text-muted">Hãy mua sắm để có đơn hàng đầu tiên</p>
                <a href="/marketplace" class="btn btn-primary">
                    <i class="cil-cart"></i> Đi mua sắm
                </a>
            </div>
        `;
        return;
    }

    container.innerHTML = '';
    orders.forEach(order => {
        const orderCard = createOrderCard(order);
        container.appendChild(orderCard);
    });
}

function createOrderCard(order) {
    const div = document.createElement('div');
    div.className = 'order-card';
    
    const statusClass = `status-${order.status}`;
    const statusText = {
        'completed': 'Hoàn thành',
        'pending': 'Đang xử lý',
        'cancelled': 'Đã hủy'
    }[order.status] || order.status;
    
    div.innerHTML = `
        <div class="order-header">
            <div>
                <div class="order-number">#${order.order_number}</div>
                <small class="text-muted">
                    <i class="cil-clock"></i> ${new Date(order.created_at).toLocaleDateString('vi-VN')}
                </small>
            </div>
            <div class="text-end">
                <div class="order-status ${statusClass}">${statusText}</div>
                <div class="mt-1">
                    <strong class="text-primary">${order.final_amount.toLocaleString()} MP</strong>
                </div>
            </div>
        </div>
        
        <div class="order-items">
            ${order.items.map(item => `
                <div class="d-flex justify-content-between align-items-center py-2 border-bottom">
                    <div>
                        <strong>${item.product_name}</strong>
                        <br><small class="text-muted">Số lượng: ${item.quantity}</small>
                    </div>
                    <div class="text-end">
                        <strong>${item.total_price.toLocaleString()} MP</strong>
                    </div>
                </div>
            `).join('')}
        </div>

        ${hasNonAccountProducts(order.items) ? `
            <div class="order-actions mt-3 text-end">
                <a href="/marketplace/orders/${order.order_id}" class="btn btn-primary btn-sm">
                    <i class="cil-info"></i> Xem chi tiết
                </a>
            </div>
        ` : ''}
    `;
    
    return div;
}

// Helper function to check if order has non-account products
function hasNonAccountProducts(items) {
    return items.some(item => item.product_type !== 'account');
}

// Filter and export functions
function filterAccounts() {
    loadUserAccounts(1);
}

function loadFilterOptions() {
    // Load order options
    fetch('/api/marketplace/user/orders/list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const orderSelect = document.getElementById('orderFilter');
                orderSelect.innerHTML = '<option value="">Tất cả đơn hàng</option>';
                data.orders.forEach(order => {
                    orderSelect.innerHTML += `<option value="${order.order_number}">#${order.order_number}</option>`;
                });
            }
        });
    
    // Load account products for package filter
    loadAccountProducts();
}

// Removed duplicate functions - using the ones above

function selectAllAccounts() {
    document.getElementById('selectAllCheckbox').checked = true;
    toggleSelectAll();
}

function showExportModal() {
    if (selectedAccountIds.length === 0) {
        alert('Vui lòng chọn ít nhất một tài khoản để xuất');
        return;
    }
    
    new coreui.Modal(document.getElementById('exportModal')).show();
}

function exportSelectedAccounts() {
    const selectedColumns = [];
    document.querySelectorAll('#exportModal input[type="checkbox"]:checked').forEach(checkbox => {
        selectedColumns.push(checkbox.id.replace('export_', ''));
    });
    
    if (selectedColumns.length === 0) {
        alert('Vui lòng chọn ít nhất một cột để xuất');
        return;
    }
    
    const params = new URLSearchParams({
        account_ids: selectedAccountIds.join(','),
        columns: selectedColumns.join(',')
    });
    
    window.open(`/api/marketplace/user/accounts/export?${params}`, '_blank');
    
    // Close modal
    coreui.Modal.getInstance(document.getElementById('exportModal')).hide();
}

// Action functions
function getEmailCode(accountId) {
    fetch(`/api/marketplace/user/accounts/${accountId}/email-code`, { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Mã email: ${data.email_code}`);
            } else {
                alert('Lỗi: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Lỗi kết nối');
        });
}

function editAccount(accountId) {
    // Implement edit account functionality
    alert('Chức năng edit đang được phát triển');
}

function showWarrantyStatus(accountId) {
    // Show alert that warranty request is already pending
    alert('Tài khoản này đã có yêu cầu bảo hành đang được xử lý. Vui lòng kiểm tra tab "Yêu cầu bảo hành" để xem chi tiết hoặc hủy yêu cầu nếu cần.');
}



function updateAccountsPagination(pagination) {
    const container = document.getElementById('accountsPagination');
    
    if (pagination.total_pages <= 1) {
        container.innerHTML = '';
        return;
    }
    
    let paginationHTML = '<nav><ul class="pagination">';
    
    // Previous button
    if (pagination.current_page > 1) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadUserAccounts(${pagination.current_page - 1}); return false;">Trước</a></li>`;
    }
    
    // Page numbers
    for (let i = Math.max(1, pagination.current_page - 2); i <= Math.min(pagination.total_pages, pagination.current_page + 2); i++) {
        const activeClass = i === pagination.current_page ? 'active' : '';
        paginationHTML += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadUserAccounts(${i}); return false;">${i}</a></li>`;
    }
    
    // Next button
    if (pagination.current_page < pagination.total_pages) {
        paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadUserAccounts(${pagination.current_page + 1}); return false;">Sau</a></li>`;
    }
    
    paginationHTML += '</ul></nav>';
    container.innerHTML = paginationHTML;
}

// Warranty Request Functions
function requestWarranty(accountId) {
    document.getElementById('warrantyAccountId').value = accountId;
    document.getElementById('warrantyRequestForm').reset();
    document.getElementById('warrantyImagePreview').style.display = 'none';

    const modal = new coreui.Modal(document.getElementById('warrantyRequestModal'));
    modal.show();
}

function submitWarrantyRequest() {
    const form = document.getElementById('warrantyRequestForm');
    const formData = new FormData(form);

    // Validate form
    if (!formData.get('reason')) {
        alert('Vui lòng chọn lý do bảo hành');
        return;
    }

    if (!formData.get('evidence_image')) {
        alert('Vui lòng đính kèm hình ảnh minh chứng');
        return;
    }

    // Show loading
    const submitBtn = document.querySelector('#warrantyRequestModal .btn-primary');
    const spinner = document.getElementById('warrantySubmitSpinner');
    submitBtn.disabled = true;
    spinner.classList.remove('d-none');

    fetch('/api/marketplace/warranty/request', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Yêu cầu bảo hành đã được gửi thành công!');
            coreui.Modal.getInstance(document.getElementById('warrantyRequestModal')).hide();
            loadUserAccounts(); // Reload accounts to update status
        } else {
            alert('Lỗi: ' + (data.error || 'Không thể gửi yêu cầu bảo hành'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi gửi yêu cầu bảo hành');
    })
    .finally(() => {
        submitBtn.disabled = false;
        spinner.classList.add('d-none');
    });
}

// Image preview for warranty evidence
document.getElementById('warrantyEvidence').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File quá lớn. Vui lòng chọn file nhỏ hơn 5MB.');
            e.target.value = '';
            return;
        }

        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Vui lòng chọn file ảnh.');
            e.target.value = '';
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('warrantyPreviewImg').src = e.target.result;
            document.getElementById('warrantyImagePreview').style.display = 'block';
        };
        reader.readAsDataURL(file);
    } else {
        document.getElementById('warrantyImagePreview').style.display = 'none';
    }
});

// User Warranty Tracking Functions
function loadWarrantyCount() {
    fetch('/api/marketplace/user/warranty/count')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('warrantyCount').textContent = data.count || 0;
            }
        })
        .catch(error => console.error('Error loading warranty count:', error));
}

function loadUserWarrantyRequests(page = 1) {
    const status = document.getElementById('warrantyStatusFilter')?.value || '';

    const params = new URLSearchParams({
        page: page,
        page_size: 20,
        status: status
    });

    fetch(`/api/marketplace/user/warranty/requests?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayUserWarrantyRequests(data.requests);
                updateWarrantyRequestsPagination(data.pagination);
            } else {
                console.error('Error loading warranty requests:', data.error);
            }
        })
        .catch(error => console.error('Error:', error));
}

function displayUserWarrantyRequests(requests) {
    const tbody = document.getElementById('warrantyRequestsTableBody');
    tbody.innerHTML = '';

    if (requests.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="cil-info"></i> Chưa có yêu cầu bảo hành nào
                </td>
            </tr>
        `;
        return;
    }

    requests.forEach(request => {
        const tr = document.createElement('tr');

        const statusBadge = getUserWarrantyStatusBadge(request.status);
        const actionButton = getUserWarrantyActionButton(request);

        tr.innerHTML = `
            <td>${request.id}</td>
            <td>
                <strong>${request.account_name}</strong><br>
                <small class="text-muted">ID: ${request.account_id}</small>
            </td>
            <td>
                <span class="badge bg-warning">${request.reason}</span>
            </td>
            <td>${statusBadge}</td>
            <td>${new Date(request.created_at).toLocaleString('vi-VN')}</td>
            <td>${request.processed_at ? new Date(request.processed_at).toLocaleString('vi-VN') : 'Chưa xử lý'}</td>
            <td>${actionButton}</td>
        `;

        tbody.appendChild(tr);
    });
}

function getUserWarrantyStatusBadge(status) {
    const badges = {
        'Pending': '<span class="badge bg-warning">Chờ xử lý</span>',
        'Approved': '<span class="badge bg-success">Đã chấp nhận</span>',
        'Rejected': '<span class="badge bg-danger">Đã từ chối</span>',
        'Replaced': '<span class="badge bg-primary">Đã thay thế</span>',
        'Cancelled': '<span class="badge bg-secondary">Đã hủy</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Không xác định</span>';
}

function getUserWarrantyActionButton(request) {
    let buttons = `
        <button class="btn btn-sm btn-outline-info" onclick="viewUserWarrantyDetails(${request.id})" title="Xem chi tiết">
            <i class="cil-info"></i> Chi tiết
        </button>
    `;

    // Add cancel button for pending requests
    if (request.status === 'Pending') {
        buttons += `
            <button class="btn btn-sm btn-outline-danger ms-1" onclick="cancelWarrantyRequest(${request.id})" title="Hủy yêu cầu">
                <i class="cil-x"></i> Hủy
            </button>
        `;
    }

    return buttons;
}

function updateWarrantyRequestsPagination(pagination) {
    const container = document.getElementById('warrantyRequestsPagination');
    container.innerHTML = '';

    if (pagination.total_pages <= 1) return;

    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${pagination.current_page === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadUserWarrantyRequests(${pagination.current_page - 1})">‹</a>`;
    container.appendChild(prevLi);

    // Page numbers
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === pagination.current_page ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadUserWarrantyRequests(${i})">${i}</a>`;
        container.appendChild(li);
    }

    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${pagination.current_page === pagination.total_pages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadUserWarrantyRequests(${pagination.current_page + 1})">›</a>`;
    container.appendChild(nextLi);

    // Update info
    const start = (pagination.current_page - 1) * pagination.page_size + 1;
    const end = Math.min(pagination.current_page * pagination.page_size, pagination.total);
    document.getElementById('warrantyRequestsInfo').textContent =
        `Hiển thị ${start} - ${end} của ${pagination.total} yêu cầu`;
}

function viewUserWarrantyDetails(warrantyId) {
    fetch(`/api/marketplace/user/warranty/details/${warrantyId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayUserWarrantyDetails(data.warranty);
            } else {
                alert('Lỗi: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi tải chi tiết bảo hành');
        });
}

function cancelWarrantyRequest(warrantyId) {
    if (confirm('Bạn có chắc chắn muốn hủy yêu cầu bảo hành này?')) {
        fetch(`/api/marketplace/user/warranty/${warrantyId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Đã hủy yêu cầu bảo hành thành công');
                loadUserWarrantyRequests(); // Reload warranty requests
                loadUserAccounts(currentPage); // Reload accounts to update button status
            } else {
                alert('Lỗi: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Lỗi kết nối');
        });
    }
}

function displayUserWarrantyDetails(warranty) {
    let content = `
        <div class="modal fade" id="userWarrantyDetailsModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Chi tiết yêu cầu bảo hành #${warranty.id}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Thông tin tài khoản</h6>
                                <p><strong>Tên:</strong> ${warranty.account_name}</p>
                                <p><strong>ID:</strong> ${warranty.account_id}</p>
                            </div>
                            <div class="col-md-6">
                                <h6>Thông tin yêu cầu</h6>
                                <p><strong>Lý do:</strong> ${warranty.reason}</p>
                                <p><strong>Trạng thái:</strong> ${getUserWarrantyStatusBadge(warranty.status)}</p>
                                <p><strong>Ngày yêu cầu:</strong> ${new Date(warranty.created_at).toLocaleString('vi-VN')}</p>
                            </div>
                        </div>

                        ${warranty.description ? `
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>Mô tả chi tiết</h6>
                                <p>${warranty.description}</p>
                            </div>
                        </div>
                        ` : ''}

                        ${warranty.evidence_image ? `
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>Hình ảnh minh chứng</h6>
                                <img src="/static/${warranty.evidence_image}" alt="Evidence" class="img-fluid" style="max-width: 400px;">
                            </div>
                        </div>
                        ` : ''}

                        ${warranty.admin_response ? `
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>Phản hồi của admin</h6>
                                <p>${warranty.admin_response}</p>
                                ${warranty.admin_evidence_image ? `
                                <img src="/static/${warranty.admin_evidence_image}" alt="Admin Evidence" class="img-fluid" style="max-width: 400px;">
                                ` : ''}
                            </div>
                        </div>
                        ` : ''}

                        ${warranty.replacement_account_name ? `
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>Tài khoản thay thế</h6>
                                <p><strong>Tên:</strong> ${warranty.replacement_account_name}</p>
                                <p><strong>ID:</strong> ${warranty.replacement_account_id}</p>
                            </div>
                        </div>
                        ` : ''}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('userWarrantyDetailsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', content);

    // Show modal
    const modal = new coreui.Modal(document.getElementById('userWarrantyDetailsModal'));
    modal.show();

    // Remove modal from DOM when hidden
    document.getElementById('userWarrantyDetailsModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

</script>
{% endblock %}
