{% extends "base_coreui.html" %}

{% block title %}B<PERSON>o hành tài khoản{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="fas fa-shield-alt"></i> Bảo hành tài khoản
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Thông tin bảo hành:</strong>
                        <ul class="mb-0 mt-2">
                            <li>Thời gian bảo hành được tính từ ngày mua tài khoản</li>
                            <li>Chỉ bảo hành các lỗi kỹ thuật do hệ thống</li>
                            <li>Vui lòng cung cấp đầy đủ thông tin và hình ảnh minh chứng</li>
                        </ul>
                    </div>

                    <!-- Warranty Requests Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tài khoản</th>
                                    <th>Đơn hàng</th>
                                    <th>Gói sản phẩm</th>
                                    <th>Lý do</th>
                                    <th>Trạng thái</th>
                                    <th>Ngày tạo</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody id="warrantyRequestsTable">
                                <!-- Data will be loaded via JavaScript -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <nav aria-label="Warranty requests pagination">
                        <ul class="pagination justify-content-center" id="warrantyPagination">
                            <!-- Pagination will be loaded via JavaScript -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Warranty Request Details Modal -->
<div class="modal fade" id="warrantyDetailsModal" tabindex="-1" aria-labelledby="warrantyDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="warrantyDetailsModalLabel">Chi tiết yêu cầu bảo hành</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="warrantyDetailsContent">
                <!-- Content will be loaded via JavaScript -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<script>
let currentPage = 1;
const pageSize = 20;

// Load warranty requests
function loadWarrantyRequests(page = 1) {
    currentPage = page;
    
    fetch(`/api/marketplace/user/warranty/requests?page=${page}&page_size=${pageSize}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayWarrantyRequests(data.requests);
                displayPagination(data.total_pages, page);
            } else {
                console.error('Error loading warranty requests:', data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

// Display warranty requests
function displayWarrantyRequests(requests) {
    const tbody = document.getElementById('warrantyRequestsTable');
    tbody.innerHTML = '';
    
    if (requests.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center">Không có yêu cầu bảo hành nào</td></tr>';
        return;
    }
    
    requests.forEach(request => {
        const statusBadge = getStatusBadge(request.status);

        // Tạo nút thao tác
        let actionButtons = `
            <button class="btn btn-sm btn-info" onclick="viewWarrantyDetails(${request.id})">
                <i class="fas fa-eye"></i> Xem
            </button>
        `;

        // Thêm nút hủy cho yêu cầu đang chờ xử lý
        if (request.status === 'Pending') {
            actionButtons += `
                <button class="btn btn-sm btn-danger ms-1" onclick="cancelWarrantyRequest(${request.id})" title="Hủy yêu cầu">
                    <i class="fas fa-times"></i> Hủy
                </button>
            `;
        }

        const row = `
            <tr>
                <td>${request.id}</td>
                <td>${request.account_name || 'N/A'}</td>
                <td><span class="badge bg-info">${request.order_number || 'N/A'}</span></td>
                <td>${request.product_name || 'N/A'}</td>
                <td>${request.reason}</td>
                <td>${statusBadge}</td>
                <td>${new Date(request.created_at).toLocaleDateString('vi-VN')}</td>
                <td>${actionButtons}</td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

// Get status badge
function getStatusBadge(status) {
    const badges = {
        'Pending': '<span class="badge bg-warning">Đang chờ</span>',
        'Approved': '<span class="badge bg-success">Đã duyệt</span>',
        'Rejected': '<span class="badge bg-danger">Từ chối</span>',
        'Completed': '<span class="badge bg-primary">Hoàn thành</span>',
        'Cancelled': '<span class="badge bg-secondary">Đã hủy</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Không xác định</span>';
}

// Display pagination
function displayPagination(totalPages, currentPage) {
    const pagination = document.getElementById('warrantyPagination');
    pagination.innerHTML = '';
    
    if (totalPages <= 1) return;
    
    // Previous button
    if (currentPage > 1) {
        pagination.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadWarrantyRequests(${currentPage - 1})">Trước</a>
            </li>
        `;
    }
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const active = i === currentPage ? 'active' : '';
        pagination.innerHTML += `
            <li class="page-item ${active}">
                <a class="page-link" href="#" onclick="loadWarrantyRequests(${i})">${i}</a>
            </li>
        `;
    }
    
    // Next button
    if (currentPage < totalPages) {
        pagination.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadWarrantyRequests(${currentPage + 1})">Sau</a>
            </li>
        `;
    }
}

// View warranty details
function viewWarrantyDetails(warrantyId) {
    fetch(`/api/marketplace/user/warranty/details/${warrantyId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayWarrantyDetails(data.warranty);
                const modal = new coreui.Modal(document.getElementById('warrantyDetailsModal'));
                modal.show();
            } else {
                alert('Lỗi khi tải chi tiết: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Lỗi khi tải chi tiết');
        });
}

// Display warranty details
function displayWarrantyDetails(warranty) {
    const content = document.getElementById('warrantyDetailsContent');
    
    let evidenceImage = '';
    if (warranty.evidence_image) {
        evidenceImage = `
            <div class="mb-3">
                <label class="form-label"><strong>Hình ảnh minh chứng:</strong></label>
                <div>
                    <img src="/static/${warranty.evidence_image}"
                         alt="Evidence" class="img-fluid" style="max-width: 300px;">
                </div>
            </div>
        `;
    }
    
    let adminResponse = '';
    if (warranty.admin_response) {
        adminResponse = `
            <div class="mb-3">
                <label class="form-label"><strong>Phản hồi từ admin:</strong></label>
                <div class="alert alert-info">${warranty.admin_response}</div>
            </div>
        `;
    }
    
    let adminEvidence = '';
    if (warranty.admin_evidence_image) {
        adminEvidence = `
            <div class="mb-3">
                <label class="form-label"><strong>Hình ảnh từ admin:</strong></label>
                <div>
                    <img src="/static/${warranty.admin_evidence_image}"
                         alt="Admin Evidence" class="img-fluid" style="max-width: 300px;">
                </div>
            </div>
        `;
    }
    
    // Thông tin account thay thế (chỉ hiển thị khi bảo hành được duyệt)
    let replacementInfo = '';
    if (warranty.status === 'Approved' && warranty.replacement_account_name) {
        replacementInfo = `
            <div class="mb-3">
                <label class="form-label"><strong>Tài khoản thay thế:</strong></label>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> ${warranty.replacement_account_name}
                    <small class="d-block text-muted">Tài khoản này đã được cung cấp để thay thế cho tài khoản bị lỗi</small>
                </div>
            </div>
        `;
    }

    content.innerHTML = `
        <div class="mb-3">
            <label class="form-label"><strong>Tài khoản:</strong></label>
            <div>${warranty.account_name || 'N/A'}</div>
        </div>
        <div class="mb-3">
            <label class="form-label"><strong>Lý do:</strong></label>
            <div>${warranty.reason}</div>
        </div>
        <div class="mb-3">
            <label class="form-label"><strong>Mô tả:</strong></label>
            <div>${warranty.description || 'Không có mô tả'}</div>
        </div>
        ${evidenceImage}
        <div class="mb-3">
            <label class="form-label"><strong>Trạng thái:</strong></label>
            <div>${getStatusBadge(warranty.status)}</div>
        </div>
        <div class="mb-3">
            <label class="form-label"><strong>Ngày tạo:</strong></label>
            <div>${new Date(warranty.created_at).toLocaleString('vi-VN')}</div>
        </div>
        ${replacementInfo}
        ${adminResponse}
        ${adminEvidence}
    `;
}

// Cancel warranty request
function cancelWarrantyRequest(warrantyId) {
    if (!confirm('Bạn có chắc chắn muốn hủy yêu cầu bảo hành này?')) {
        return;
    }

    fetch(`/api/marketplace/user/warranty/${warrantyId}/cancel`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Đã hủy yêu cầu bảo hành thành công');
            loadWarrantyRequests(); // Reload the list
        } else {
            alert('Lỗi: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi hủy yêu cầu bảo hành');
    });
}

// Load warranty requests on page load
document.addEventListener('DOMContentLoaded', function() {
    loadWarrantyRequests();
});
</script>
{% endblock %}
