{% extends "base_coreui.html" %}
{% block title %}Marketplace - C<PERSON><PERSON> h<PERSON>{% endblock %}

{% block head_extra %}
<script src="/static/js/price-formatter.js"></script>
<style>
    .hero-section {
        background: linear-gradient(135deg, #00C6AE 0%, #00a693 100%);
        color: white;
        padding: 40px 0;
        margin-bottom: 30px;
    }
    .category-card {
        border: none;
        border-radius: 12px;
        transition: all 0.3s ease;
        cursor: pointer;
        height: 100%;
    }
    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    .product-card {
        border: none;
        border-radius: 12px;
        transition: all 0.3s ease;
        height: 100%;
        overflow: hidden;
    }
    .product-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.1);
    }
    .product-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }
    .product-price {
        font-size: 1.25rem;
        font-weight: bold;
        color: #00C6AE;
    }
    .product-type-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 0.75rem;
    }
    .featured-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        background: linear-gradient(45deg, #ff6b6b, #ffa500);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: bold;
    }
    .search-section {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    .categories-section {
        margin-bottom: 25px;
    }
    .categories-section .card-body {
        padding: 15px;
    }
    .categories-section .category-card {
        height: auto;
        min-height: 140px;
    }
    .categories-section .category-card i {
        font-size: 2rem !important;
        margin-bottom: 10px !important;
    }
    .categories-section .card-title {
        font-size: 1rem;
        margin-bottom: 8px;
    }
    .categories-section .card-text {
        font-size: 0.85rem;
        margin-bottom: 10px;
    }
    .category-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .category-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .category-card.active {
        border: 2px solid #00C6AE;
        background: linear-gradient(135deg, #00C6AE10, #00a69310);
    }
    .category-card.active .badge {
        background-color: #00C6AE !important;
    }
    .mp-balance {
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 15px 20px;
        border-radius: 12px;
        margin-bottom: 20px;
    }
    .cart-floating {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 1000;
        background: linear-gradient(135deg, #00C6AE 0%, #00a693 100%);
        color: white;
        border: none;
        border-radius: 60px;
        padding: 18px 25px;
        box-shadow: 0 8px 25px rgba(0,198,174,0.4);
        transition: all 0.3s ease;
        font-size: 1.1rem;
        font-weight: 600;
        min-width: 120px;
        cursor: pointer;
    }
    .cart-floating:hover {
        background: linear-gradient(135deg, #00a693 0%, #008a7a 100%);
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 12px 35px rgba(0,198,174,0.5);
    }
    .cart-floating:active {
        transform: translateY(-1px) scale(1.02);
    }
    .cart-floating .cart-count {
        background: #ff4757;
        color: white;
        border-radius: 50%;
        padding: 4px 8px;
        font-size: 0.8rem;
        font-weight: bold;
        margin-left: 8px;
        min-width: 24px;
        height: 24px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        animation: pulse 2s infinite;
    }
    .cart-floating.shake {
        animation: shake 0.6s ease-in-out;
    }

    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
        20%, 40%, 60%, 80% { transform: translateX(3px); }
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    @keyframes bounceIn {
        0% {
            opacity: 0;
            transform: scale(0.3) translateY(20px);
        }
        50% {
            opacity: 1;
            transform: scale(1.05) translateY(-5px);
        }
        70% {
            transform: scale(0.95) translateY(0);
        }
        100% {
            opacity: 1;
            transform: scale(1) translateY(0);
        }
    }

    .cart-floating.bounce-in {
        animation: bounceIn 0.6s ease-out;
    }

    .cart-floating i {
        font-size: 1.2rem;
        margin-right: 5px;
    }

    .cart-floating .cart-text {
        font-size: 0.9rem;
        margin: 0 5px;
    }

    .description-content {
        line-height: 1.6;
        color: #495057;
        font-size: 0.9rem;
    }
    .description-content h4,
    .description-content h5,
    .description-content h6 {
        margin-top: 1rem;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }
    .description-content h4 {
        font-size: 1.1rem;
    }
    .description-content h5 {
        font-size: 1rem;
    }
    .description-content h6 {
        font-size: 0.95rem;
    }
    .description-content ul,
    .description-content ol {
        padding-left: 1.2rem;
        margin-bottom: 0.8rem;
    }
    .description-content li {
        margin-bottom: 0.3rem;
    }
    .description-content strong {
        color: #212529;
        font-weight: 600;
    }
    .description-content em {
        font-style: italic;
        color: #6c757d;
    }
    .description-content a {
        color: #00C6AE;
        text-decoration: none;
        font-size: 0.85rem;
    }
    .description-content a:hover {
        color: #00a693;
        text-decoration: underline;
    }
    .description-content p {
        margin-bottom: 0.8rem;
    }

    /* Scrollbar styling for description */
    .description-content::-webkit-scrollbar {
        width: 6px;
    }
    .description-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    .description-content::-webkit-scrollbar-thumb {
        background: #00C6AE;
        border-radius: 3px;
    }
    .description-content::-webkit-scrollbar-thumb:hover {
        background: #00a693;
    }
    .description-content {
        line-height: 1.6;
        color: #495057;
        font-size: 0.9rem;
    }
    .description-content h4,
    .description-content h5,
    .description-content h6 {
        margin-top: 1rem;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }
    .description-content h4 {
        font-size: 1.1rem;
    }
    .description-content h5 {
        font-size: 1rem;
    }
    .description-content h6 {
        font-size: 0.95rem;
    }
    .description-content ul,
    .description-content ol {
        padding-left: 1.2rem;
        margin-bottom: 0.8rem;
    }
    .description-content li {
        margin-bottom: 0.3rem;
    }
    .description-content strong {
        color: #212529;
        font-weight: 600;
    }
    .description-content em {
        font-style: italic;
        color: #6c757d;
    }
    .description-content a {
        color: #00C6AE;
        text-decoration: none;
        font-size: 0.85rem;
    }
    .description-content a:hover {
        color: #00a693;
        text-decoration: underline;
    }
    .description-content p {
        margin-bottom: 0.8rem;
    }

    /* Scrollbar styling for description */
    .card-body::-webkit-scrollbar {
        width: 6px;
    }
    .card-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }
    .card-body::-webkit-scrollbar-thumb {
        background: #00C6AE;
        border-radius: 3px;
    }
    .card-body::-webkit-scrollbar-thumb:hover {
        background: #00a693;
    }
    .cart-count {
        position: absolute;
        top: -5px;
        right: -5px;
        background: #ff4757;
        color: white;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        font-size: 0.7rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="display-4 fw-bold mb-3">🛒 SAPMMO Marketplace</h1>
                <p class="lead mb-4">Khám phá các sản phẩm chất lượng cao cho TikTok Marketing</p>

            </div>
            <div class="col-md-4">
                <div class="mp-balance text-center">
                    <h5><i class="cil-wallet"></i> Số dư MP</h5>
                    <h2 id="userMpBalance">0 MP</h2>
                    <a href="/deposit" class="btn btn-light btn-sm">Nạp thêm</a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Search & Filter Section -->
    <div class="search-section">
        <div class="row">
            <div class="col-md-4">
                <div class="input-group">
                    <span class="input-group-text"><i class="cil-search"></i></span>
                    <input type="text" class="form-control" id="searchInput" placeholder="Tìm kiếm sản phẩm...">
                </div>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="categoryFilter">
                    <option value="">Tất cả danh mục</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="typeFilter">
                    <option value="">Tất cả loại</option>
                    <!-- Options will be populated dynamically -->
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="priceFilter">
                    <option value="">Tất cả giá</option>
                    <option value="0-500000">Dưới 500K MP</option>
                    <option value="500000-1000000">500K - 1M MP</option>
                    <option value="1000000-5000000">1M - 5M MP</option>
                    <option value="5000000-999999999">Trên 5M MP</option>
                </select>
            </div>
            <div class="col-md-2">
                <select class="form-select" id="sortFilter">
                    <option value="newest">Mới nhất</option>
                    <option value="price_low">Giá thấp đến cao</option>
                    <option value="price_high">Giá cao đến thấp</option>
                    <option value="popular">Phổ biến</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Categories Section -->
    <div class="row mb-4 categories-section">
        <div class="col-12">
            <h3 class="mb-3"><i class="cil-grid"></i> Danh mục sản phẩm</h3>
            <div class="row" id="categoriesContainer">
                <!-- Categories will be loaded here -->
            </div>
        </div>
    </div>

    <!-- Featured Products -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="mb-4"><i class="cil-star"></i> Sản phẩm nổi bật</h3>
            <div class="row" id="featuredProductsContainer">
                <!-- Featured products will be loaded here -->
            </div>
        </div>
    </div>

    <!-- All Products -->
    <div class="row mb-5">
        <div class="col-12">
            <h3 class="mb-4"><i class="cil-basket"></i> Tất cả sản phẩm</h3>
            <div class="row" id="allProductsContainer">
                <!-- All products will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Floating Cart Button -->
<button class="cart-floating" onclick="showCart()" style="display: none;" id="cartButton">
    <i class="cil-basket"></i>
    <span class="cart-text">Giỏ hàng</span>
    <span class="cart-count" id="cartCount">0</span>
</button>

<!-- Product Detail Modal -->
<div class="modal fade" id="productDetailModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productDetailTitle">Chi tiết sản phẩm</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="productDetailBody">
                <!-- Product details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" id="addToCartBtn">
                    <i class="cil-basket"></i> Thêm vào giỏ
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Shopping Cart Modal -->
<div class="modal fade" id="cartModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="cil-basket"></i> Giỏ hàng của bạn</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="cartItems">
                    <!-- Cart items will be loaded here -->
                </div>

                <!-- Discount Code Section -->
                <div class="card mt-3">
                    <div class="card-body">
                        <h6 class="card-title">Mã giảm giá</h6>
                        <div class="input-group">
                            <input type="text" class="form-control" id="discountCodeInput" placeholder="Nhập mã giảm giá..." style="text-transform: uppercase;">
                            <button class="btn btn-outline-primary" type="button" onclick="applyDiscountCode()">
                                <i class="cil-check"></i> Áp dụng
                            </button>
                        </div>
                        <div id="discountMessage" class="mt-2"></div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="card mt-4">
                    <div class="card-body">
                        <h6 class="card-title">Tóm tắt đơn hàng</h6>
                        <div class="d-flex justify-content-between">
                            <span>Tạm tính:</span>
                            <span id="cartSubtotal">0 MP</span>
                        </div>
                        <div class="d-flex justify-content-between" id="discountRow" style="display: none;">
                            <span>Giảm giá:</span>
                            <span id="cartDiscount" class="text-success">-0 MP</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between fw-bold">
                            <span>Tổng cộng:</span>
                            <span id="cartTotal" class="text-primary">0 MP</span>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">Số dư hiện tại: <span id="currentBalance" class="fw-bold">0 MP</span></small>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Tiếp tục mua sắm</button>
                <button type="button" class="btn btn-primary" onclick="proceedToCheckout()" id="checkoutBtn">
                    <i class="cil-credit-card"></i> Thanh toán
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Checkout Success Modal -->
<div class="modal fade" id="checkoutSuccessModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title"><i class="cil-check"></i> Đặt hàng thành công!</h5>
            </div>
            <div class="modal-body text-center">
                <div class="mb-3">
                    <i class="cil-check-circle fa-3x text-success"></i>
                </div>
                <h5 id="successOrderNumber">Đơn hàng #ORD12345678</h5>
                <p class="text-muted">Cảm ơn bạn đã mua sắm tại SAPMMO Marketplace!</p>
                <div class="alert alert-info">
                    <i class="cil-info"></i> Sản phẩm đã được thêm vào tài khoản của bạn.
                </div>
            </div>
            <div class="modal-footer">
                <a href="/marketplace/orders" class="btn btn-primary">Xem đơn hàng</a>
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables
let products = [];
let categories = [];
let cart = JSON.parse(localStorage.getItem('marketplace_cart') || '[]');
let currentUser = null;
let appliedDiscount = null;

// Initialize page
$(document).ready(function() {
    // Wait a bit for price-formatter.js to load
    setTimeout(() => {
        loadUserInfo();
        loadCategories();
        loadProducts();
        updateCartUI();
        setupEventListeners();
    }, 200);
});

// Helper function to format price with coffee cup support (for backward compatibility)
function formatPrice(amount) {
    // Always return MP format for string usage, use formatPriceToElement for DOM
    return new Intl.NumberFormat('vi-VN').format(amount) + ' MP';
}

function loadUserInfo() {
    // Get user MP balance from session or API
    fetch('/api/user/profile')
        .then(response => response.json())
        .then(data => {
            console.log('User profile response:', data); // Debug log
            if (data.success) {
                currentUser = data.user;
                const balanceElement = document.getElementById('userMpBalance');
                balanceElement.textContent = new Intl.NumberFormat('vi-VN').format(data.user.mp_balance || 0) + ' MP';
                console.log('Current user loaded:', currentUser); // Debug log
            } else {
                console.error('Failed to load user profile:', data.error);
            }
        })
        .catch(error => {
            console.error('Error loading user info:', error);
            // Fallback - check if user is logged in via session
            if (typeof session !== 'undefined' && session.user_id) {
                currentUser = {
                    user_id: session.user_id,
                    username: session.username || 'User',
                    mp_balance: 0 // Will be updated when needed
                };
            }
        });
}

function loadCategories() {
    fetch('/api/marketplace/categories')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                categories = data.categories;
                displayCategories();
                populateCategoryFilter();
            }
        })
        .catch(error => console.error('Error loading categories:', error));
}

function loadProducts() {
    fetch('/api/marketplace/products')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                products = data.products;
                displayProducts();
                populateTypeFilter();
            }
        })
        .catch(error => console.error('Error loading products:', error));
}

function displayCategories() {
    const container = document.getElementById('categoriesContainer');
    container.innerHTML = '';

    categories.forEach(category => {
        const col = document.createElement('div');
        col.className = 'col-lg-3 col-md-4 col-sm-6 mb-3';
        col.innerHTML = `
            <div class="card category-card h-100" onclick="filterByCategory(${category.category_id})">
                <div class="card-body text-center">
                    ${category.thumbnail_url ?
                        `<img src="${category.thumbnail_url}" alt="${category.name}" style="width: 60px; height: 60px; object-fit: cover; border-radius: 8px; margin-bottom: 10px;">` :
                        `<i class="${category.icon} text-primary mb-2"></i>`
                    }
                    <h5 class="card-title">${category.name}</h5>
                    <p class="card-text text-muted">${category.description || ''}</p>
                    <span class="badge bg-primary">${category.product_count} sản phẩm</span>
                </div>
            </div>
        `;
        container.appendChild(col);
    });
}

function displayProducts() {
    displayFeaturedProducts();
    displayAllProducts();
}

function displayFeaturedProducts() {
    const container = document.getElementById('featuredProductsContainer');
    const featuredProducts = products.filter(p => p.is_featured);
    
    if (featuredProducts.length === 0) {
        container.innerHTML = '<div class="col-12"><p class="text-muted">Chưa có sản phẩm nổi bật</p></div>';
        return;
    }

    container.innerHTML = '';
    featuredProducts.slice(0, 4).forEach(product => {
        const col = document.createElement('div');
        col.className = 'col-lg-3 col-md-4 col-sm-6 mb-4';
        col.innerHTML = createProductCard(product);
        container.appendChild(col);
    });

    // Format prices after cards are added
    setTimeout(() => {
        container.querySelectorAll('[data-price]').forEach(element => {
            const price = parseInt(element.getAttribute('data-price'));
            if (window.priceFormatter) {
                window.priceFormatter.formatProductPriceToElement(price, element);
            } else {
                element.textContent = new Intl.NumberFormat('vi-VN').format(price) + ' MP';
            }
        });
    }, 100);
}

function displayAllProducts() {
    const container = document.getElementById('allProductsContainer');
    container.innerHTML = '';

    if (products.length === 0) {
        container.innerHTML = '<div class="col-12"><p class="text-muted">Chưa có sản phẩm nào</p></div>';
        return;
    }

    products.forEach(product => {
        const col = document.createElement('div');
        col.className = 'col-lg-3 col-md-4 col-sm-6 mb-4';
        col.innerHTML = createProductCard(product);
        container.appendChild(col);
    });

    // Format prices after cards are added
    setTimeout(() => {
        container.querySelectorAll('[data-price]').forEach(element => {
            const price = parseInt(element.getAttribute('data-price'));
            if (window.priceFormatter) {
                window.priceFormatter.formatProductPriceToElement(price, element);
            } else {
                element.textContent = new Intl.NumberFormat('vi-VN').format(price) + ' MP';
            }
        });
    }, 100);
}

function createProductCard(product) {
    const stockBadge = product.unlimited_stock ?
        '<span class="badge bg-success">Không giới hạn</span>' :
        `<span class="badge ${product.stock < 5 ? 'bg-warning' : 'bg-primary'}">${product.stock} còn lại</span>`;

    return `
        <div class="card product-card h-100">
            <div class="position-relative">
                <img src="${product.image_url}" class="product-image" alt="${product.name}">
                ${product.is_featured ? '<div class="featured-badge">⭐ Nổi bật</div>' : ''}
                <span class="badge bg-info product-type-badge">${getProductTypeLabel(product.product_type, product.type_name)}</span>
            </div>
            <div class="card-body d-flex flex-column">
                <h6 class="card-title">${product.name}</h6>
                <p class="card-text text-muted small flex-grow-1">${product.short_description || ''}</p>
                <div class="mt-auto">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="product-price fw-bold text-primary" data-price="${product.price}"></span>
                        ${stockBadge}
                    </div>
                    ${product.sold_count > 0 ?
                        `<div class="text-center mb-2"><small class="text-muted">Đã bán: ${product.sold_count}</small></div>` : ''
                    }
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="showProductDetail(${product.product_id})">
                            <i class="cil-info"></i> Chi tiết
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="addToCart(${product.product_id})" 
                                ${(!product.unlimited_stock && product.stock === 0) ? 'disabled' : ''}>
                            <i class="cil-basket"></i> ${(!product.unlimited_stock && product.stock === 0) ? 'Hết hàng' : 'Thêm vào giỏ'}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function getProductTypeLabel(type, typeName) {
    // Ưu tiên hiển thị tên ProductType gốc nếu có
    if (typeName) {
        return typeName;
    }

    // Fallback cho các type cũ
    const labels = {
        'account': 'Account',
        'win_product': 'Win Product',
        'course': 'Khóa học',
        'aff_package': 'AFF Package'
    };
    return labels[type] || type;
}

function populateCategoryFilter() {
    const select = document.getElementById('categoryFilter');
    select.innerHTML = '<option value="">Tất cả danh mục</option>';

    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.category_id;
        option.textContent = category.name;
        select.appendChild(option);
    });
}

function populateTypeFilter() {
    const select = document.getElementById('typeFilter');
    select.innerHTML = '<option value="">Tất cả loại</option>';

    // Get unique product types from products
    const uniqueTypes = [...new Set(products.map(p => p.product_type))];

    uniqueTypes.forEach(type => {
        const option = document.createElement('option');
        option.value = type;
        // Use type_name if available, otherwise use type
        const displayName = products.find(p => p.product_type === type)?.type_name || getProductTypeLabel(type);
        option.textContent = displayName;
        select.appendChild(option);
    });
}



function filterProducts() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    const priceFilter = document.getElementById('priceFilter').value;
    const sortFilter = document.getElementById('sortFilter').value;

    console.log('🔍 Filtering products:', { searchTerm, categoryFilter, typeFilter, priceFilter, sortFilter });

    // Filter products
    let filteredProducts = products.filter(product => {
        // Search filter
        if (searchTerm && !product.name.toLowerCase().includes(searchTerm) &&
            !product.short_description.toLowerCase().includes(searchTerm)) {
            return false;
        }

        // Category filter
        if (categoryFilter && String(product.category_id) !== String(categoryFilter)) {
            return false;
        }

        // Type filter
        if (typeFilter && product.product_type !== typeFilter) {
            return false;
        }

        // Price filter
        if (priceFilter) {
            const [minPrice, maxPrice] = priceFilter.split('-').map(Number);
            if (product.price < minPrice || product.price > maxPrice) {
                return false;
            }
        }

        return true;
    });

    // Sort products
    switch (sortFilter) {
        case 'price_low':
            filteredProducts.sort((a, b) => (a.price || 0) - (b.price || 0));
            break;
        case 'price_high':
            filteredProducts.sort((a, b) => (b.price || 0) - (a.price || 0));
            break;
        case 'popular':
            filteredProducts.sort((a, b) => (b.sold_count || 0) - (a.sold_count || 0));
            break;
        case 'newest':
        default:
            // Sort by product_id descending if no created_at
            if (filteredProducts.length > 0 && filteredProducts[0].created_at) {
                filteredProducts.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            } else {
                filteredProducts.sort((a, b) => (b.product_id || 0) - (a.product_id || 0));
            }
            break;
    }

    console.log(`📊 Filtered ${filteredProducts.length} products from ${products.length} total`);

    // Display filtered products
    displayFilteredProducts(filteredProducts);
}

function displayFilteredProducts(filteredProducts) {
    // Check what types of filters are active
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;
    const priceFilter = document.getElementById('priceFilter').value;

    // Only hide categories for non-category filters (search, type, price)
    const hasNonCategoryFilters = searchTerm || typeFilter || priceFilter;
    const hasCategoryFilter = categoryFilter;

    // Show/hide sections based on filter types
    const categoriesSection = document.querySelector('.categories-section');
    const featuredSection = document.querySelector('.row.mb-5:has(#featuredProductsContainer)');

    if (hasNonCategoryFilters) {
        // Hide categories and featured for search/type/price filters
        if (categoriesSection) categoriesSection.style.display = 'none';
        if (featuredSection) featuredSection.style.display = 'none';
    } else if (hasCategoryFilter) {
        // Keep categories visible for category filters, but hide featured
        if (categoriesSection) categoriesSection.style.display = 'block';
        if (featuredSection) featuredSection.style.display = 'none';

        // Update title for category filter
        const allProductsTitle = document.querySelector('#allProductsContainer').closest('.row').querySelector('h3');
        if (allProductsTitle) {
            const categoryName = categories.find(c => c.category_id == categoryFilter)?.name || 'Danh mục';
            allProductsTitle.innerHTML = `<i class="cil-grid"></i> ${categoryName} (${filteredProducts.length} sản phẩm)`;
        }
    } else {
        // Show all sections when no filters
        if (categoriesSection) categoriesSection.style.display = 'block';
        if (featuredSection) featuredSection.style.display = 'block';

        // Reset all products section title
        const allProductsTitle = document.querySelector('#allProductsContainer').closest('.row').querySelector('h3');
        if (allProductsTitle) {
            allProductsTitle.innerHTML = `<i class="cil-layers"></i> Tất cả sản phẩm`;
        }

        // Display featured products normally
        displayFeaturedProducts();
    }

    // Update title for non-category filters
    if (hasNonCategoryFilters) {
        const allProductsTitle = document.querySelector('#allProductsContainer').closest('.row').querySelector('h3');
        if (allProductsTitle) {
            allProductsTitle.innerHTML = `<i class="cil-search"></i> Kết quả tìm kiếm (${filteredProducts.length} sản phẩm)`;
        }
    }

    // Display filtered products in all products section
    const container = document.getElementById('allProductsContainer');
    container.innerHTML = '';

    if (filteredProducts.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="cil-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Không tìm thấy sản phẩm nào</h5>
                <p class="text-muted">Hãy thử thay đổi từ khóa tìm kiếm hoặc bộ lọc</p>
                <button class="btn btn-outline-primary" onclick="clearFilters()">
                    <i class="cil-x"></i> Xóa bộ lọc
                </button>
            </div>
        `;
        return;
    }

    filteredProducts.forEach(product => {
        const col = document.createElement('div');
        col.className = 'col-lg-3 col-md-4 col-sm-6 mb-4';
        col.innerHTML = createProductCard(product);
        container.appendChild(col);
    });

    // Format prices after cards are added
    setTimeout(() => {
        container.querySelectorAll('[data-price]').forEach(element => {
            const price = parseInt(element.getAttribute('data-price'));
            if (window.priceFormatter) {
                window.priceFormatter.formatProductPriceToElement(price, element);
            } else {
                element.textContent = new Intl.NumberFormat('vi-VN').format(price) + ' MP';
            }
        });
    }, 100);
}

function getProductTypeLabel(type, typeName = null) {
    // Use type_name if available, otherwise use mapping
    if (typeName) {
        return typeName;
    }

    const labels = {
        'account': 'Account',
        'win_product': 'Sản Phẩm Win',
        'videos': 'Videos',
        'course': 'Khóa học',
        'aff_package': 'AFF Package',
        'tiktok_playbook': 'TikTok Playbook'
    };
    return labels[type] || type;
}

function filterByCategory(categoryId) {
    // Set category filter and trigger filtering
    document.getElementById('categoryFilter').value = categoryId;

    // Update category cards visual state
    updateCategoryCardsState(categoryId);

    filterProducts();

    // Scroll to products section with a slight delay to ensure content is rendered
    setTimeout(() => {
        const allProductsSection = document.querySelector('#allProductsContainer').closest('.row');
        if (allProductsSection) {
            allProductsSection.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }, 100);
}

function updateCategoryCardsState(activeCategoryId) {
    // Remove active class from all category cards
    document.querySelectorAll('.category-card').forEach(card => {
        card.classList.remove('active');
    });

    // Add active class to selected category card
    if (activeCategoryId) {
        const activeCard = document.querySelector(`[onclick="filterByCategory(${activeCategoryId})"] .category-card`);
        if (activeCard) {
            activeCard.classList.add('active');
        }
    }
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('typeFilter').value = '';
    document.getElementById('priceFilter').value = '';
    document.getElementById('sortFilter').value = 'newest';

    // Reset category cards state
    updateCategoryCardsState(null);

    // Reset display
    displayProducts();

    // Show all sections
    const categoriesSection = document.querySelector('.categories-section');
    const featuredSection = document.querySelector('.row.mb-5:has(#featuredProductsContainer)');
    if (categoriesSection) categoriesSection.style.display = 'block';
    if (featuredSection) featuredSection.style.display = 'block';
}

function addToCart(productId) {
    const product = products.find(p => p.product_id === productId);
    if (!product) return;

    // Check if already in cart
    const existingItem = cart.find(item => item.product_id === productId);

    // Check max_quantity limit
    if (product.max_quantity && product.max_quantity === 1) {
        if (existingItem) {
            showToast(`Sản phẩm "${product.name}" chỉ có thể mua 1 lần!`, 'warning');
            return;
        }
    }

    if (existingItem) {
        // Check if adding one more would exceed max_quantity
        if (product.max_quantity && existingItem.quantity >= product.max_quantity) {
            showToast(`Sản phẩm "${product.name}" chỉ có thể mua tối đa ${product.max_quantity} sản phẩm!`, 'warning');
            return;
        }
        existingItem.quantity += 1;
    } else {
        cart.push({
            product_id: productId,
            name: product.name,
            price: product.price,
            image_url: product.image_url,
            quantity: 1,
            max_quantity: product.max_quantity  // Store max_quantity in cart item
        });
    }

    localStorage.setItem('marketplace_cart', JSON.stringify(cart));
    updateCartUI();
    triggerCartShake();
    showToast(`Đã thêm "${product.name}" vào giỏ hàng!`, 'success');
}

function updateCartUI() {
    const cartCount = cart.reduce((total, item) => total + item.quantity, 0);
    const cartButton = document.getElementById('cartButton');
    const wasHidden = cartButton.style.display === 'none';

    document.getElementById('cartCount').textContent = cartCount;

    if (cartCount > 0) {
        cartButton.style.display = 'block';

        // Add bounce effect when cart button appears for the first time
        if (wasHidden) {
            cartButton.classList.add('bounce-in');
            setTimeout(() => {
                cartButton.classList.remove('bounce-in');
            }, 600);
        }
    } else {
        cartButton.style.display = 'none';
    }
}

function triggerCartShake() {
    const cartButton = document.getElementById('cartButton');
    if (cartButton) {
        // Add shake class
        cartButton.classList.add('shake');

        // Remove shake class after animation completes
        setTimeout(() => {
            cartButton.classList.remove('shake');
        }, 600);
    }
}

function showProductDetail(productId) {
    const product = products.find(p => p.product_id === productId);
    if (!product) return;

    document.getElementById('productDetailTitle').textContent = product.name;
    document.getElementById('productDetailBody').innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <img src="${product.image_url}" class="img-fluid rounded shadow-sm" alt="${product.name}">
            </div>
            <div class="col-md-6">
                <h5 class="mb-3">${product.name}</h5>
                ${product.short_description ? `<p class="text-muted mb-3"><i class="cil-info me-2"></i>${product.short_description}</p>` : ''}
                <div class="mb-3">
                    <span class="badge bg-info me-2">${getProductTypeLabel(product.product_type, product.type_name)}</span>
                    ${product.is_featured ? '<span class="badge bg-warning">Nổi bật</span>' : ''}
                </div>
                <h4 class="text-primary mb-3" data-price="${product.price}"></h4>
                <div class="mb-3">
                    <strong><i class="cil-layers me-2"></i>Tình trạng:</strong>
                    ${product.unlimited_stock ?
                        '<span class="text-success">Không giới hạn</span>' :
                        `<span class="${product.stock > 0 ? 'text-success' : 'text-danger'}">${product.stock > 0 ? product.stock + ' còn lại' : 'Hết hàng'}</span>`
                    }
                </div>
                ${product.description ? `
                <div class="mt-4">
                    <h6 class="text-primary mb-2"><i class="cil-description me-2"></i>Mô tả chi tiết</h6>
                    <div class="description-content" style="max-height: 300px; overflow-y: auto; padding-right: 10px;">
                        ${formatDescription(product.description)}
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
    `;

    document.getElementById('addToCartBtn').onclick = () => {
        addToCart(productId);
        coreui.Modal.getInstance(document.getElementById('productDetailModal')).hide();
    };

    new coreui.Modal(document.getElementById('productDetailModal')).show();
}

function formatDescription(description) {
    if (!description) return '';

    // Convert line breaks to HTML
    let formatted = description.replace(/\n/g, '<br>');

    // Format bullet points
    formatted = formatted.replace(/^[-*•]\s+(.+)$/gm, '<li class="mb-1">$1</li>');

    // Wrap consecutive list items in ul tags
    formatted = formatted.replace(/(<li[^>]*>.*?<\/li>(?:\s*<br>\s*<li[^>]*>.*?<\/li>)*)/gs, '<ul class="mb-3">$1</ul>');

    // Format numbered lists
    formatted = formatted.replace(/^(\d+)\.\s+(.+)$/gm, '<li class="mb-1">$2</li>');
    formatted = formatted.replace(/(<li[^>]*>.*?<\/li>(?:\s*<br>\s*<li[^>]*>.*?<\/li>)*)/gs, '<ol class="mb-3">$1</ol>');

    // Format bold text **text** or __text__
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    formatted = formatted.replace(/__(.*?)__/g, '<strong>$1</strong>');

    // Format italic text *text* or _text_
    formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');
    formatted = formatted.replace(/_(.*?)_/g, '<em>$1</em>');

    // Format headers ### Header
    formatted = formatted.replace(/^###\s+(.+)$/gm, '<h6 class="text-primary mt-3 mb-2">$1</h6>');
    formatted = formatted.replace(/^##\s+(.+)$/gm, '<h5 class="text-primary mt-3 mb-2">$1</h5>');
    formatted = formatted.replace(/^#\s+(.+)$/gm, '<h4 class="text-primary mt-3 mb-2">$1</h4>');

    // Format links [text](url)
    formatted = formatted.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" class="text-decoration-none">$1 <i class="cil-external-link"></i></a>');

    // Clean up extra br tags
    formatted = formatted.replace(/<br>\s*<br>/g, '<br>');

    return formatted;
}

function showCart() {
    displayCartItems();
    updateCartSummary();
    new coreui.Modal(document.getElementById('cartModal')).show();
}

function displayCartItems() {
    const container = document.getElementById('cartItems');

    if (cart.length === 0) {
        container.innerHTML = '<div class="text-center py-4"><p class="text-muted">Giỏ hàng trống</p></div>';
        return;
    }

    container.innerHTML = '';

    cart.forEach((item, index) => {
        // Check if increase button should be disabled
        const canIncrease = !item.max_quantity || item.quantity < item.max_quantity;
        const maxQuantityText = item.max_quantity ? ` (tối đa ${item.max_quantity})` : '';

        const itemDiv = document.createElement('div');
        itemDiv.className = 'card mb-3';
        itemDiv.innerHTML = `
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-2">
                        <img src="${item.image_url}" class="img-fluid rounded" alt="${item.name}">
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-1">${item.name}</h6>
                        <small class="text-muted">Giá: ${item.price.toLocaleString()} MP${maxQuantityText}</small>
                    </div>
                    <div class="col-md-2">
                        <div class="input-group input-group-sm">
                            <button class="btn btn-outline-secondary" onclick="updateCartQuantity(${index}, ${item.quantity - 1})">-</button>
                            <input type="text" class="form-control text-center" value="${item.quantity}" readonly>
                            <button class="btn btn-outline-secondary" onclick="updateCartQuantity(${index}, ${item.quantity + 1})"
                                    ${!canIncrease ? 'disabled title="Đã đạt giới hạn tối đa"' : ''}>+</button>
                        </div>
                        ${item.max_quantity === 1 ? '<small class="text-muted d-block text-center mt-1">Chỉ mua 1 lần</small>' : ''}
                    </div>
                    <div class="col-md-2 text-end">
                        <div class="fw-bold">${(item.price * item.quantity).toLocaleString()} MP</div>
                        <button class="btn btn-sm btn-outline-danger mt-1" onclick="removeFromCart(${index})">
                            <i class="cil-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        container.appendChild(itemDiv);
    });
}

function updateCartQuantity(index, newQuantity) {
    if (newQuantity <= 0) {
        removeFromCart(index);
        return;
    }

    const cartItem = cart[index];

    // Check max_quantity limit
    if (cartItem.max_quantity && newQuantity > cartItem.max_quantity) {
        showToast(`Sản phẩm "${cartItem.name}" chỉ có thể mua tối đa ${cartItem.max_quantity} sản phẩm!`, 'warning');
        return;
    }

    cart[index].quantity = newQuantity;
    localStorage.setItem('marketplace_cart', JSON.stringify(cart));
    displayCartItems();
    updateCartSummary();
    updateCartUI();
}

function removeFromCart(index) {
    cart.splice(index, 1);
    localStorage.setItem('marketplace_cart', JSON.stringify(cart));
    displayCartItems();
    updateCartSummary();
    updateCartUI();
}

function updateCartSummary() {
    const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);
    const discountAmount = parseFloat(document.getElementById('cartDiscount').textContent.replace(/[^0-9.-]/g, '')) || 0;
    const total = subtotal - discountAmount;

    document.getElementById('cartSubtotal').textContent = subtotal.toLocaleString() + ' MP';
    document.getElementById('cartTotal').textContent = total.toLocaleString() + ' MP';

    // Update current balance
    if (currentUser) {
        document.getElementById('currentBalance').textContent = (currentUser.mp_balance || 0).toLocaleString() + ' MP';

        // Check if user has enough balance
        const checkoutBtn = document.getElementById('checkoutBtn');
        if (currentUser.mp_balance < total) {
            checkoutBtn.disabled = true;
            checkoutBtn.innerHTML = '<i class="cil-warning"></i> Số dư không đủ';
        } else {
            checkoutBtn.disabled = false;
            checkoutBtn.innerHTML = '<i class="cil-credit-card"></i> Thanh toán';
        }
    }
}

function applyDiscountCode() {
    const code = document.getElementById('discountCodeInput').value.trim().toUpperCase();
    if (!code) {
        showToast('Vui lòng nhập mã giảm giá', 'warning');
        return;
    }

    const subtotal = cart.reduce((total, item) => total + (item.price * item.quantity), 0);

    fetch('/api/marketplace/validate-discount', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({
            code: code,
            order_total: subtotal
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            appliedDiscount = data.discount;
            document.getElementById('discountRow').style.display = 'flex';
            document.getElementById('cartDiscount').textContent = '-' + data.discount.discount_amount.toLocaleString() + ' MP';
            updateCartSummary();
            showToast(`Áp dụng mã "${code}" thành công! Tiết kiệm ${data.discount.discount_amount.toLocaleString()} MP`, 'success');

            // Disable input
            document.getElementById('discountCodeInput').disabled = true;
        } else {
            showToast('Lỗi: ' + data.error, 'danger');
            appliedDiscount = null;
            document.getElementById('discountRow').style.display = 'none';
        }
    })
    .catch(error => {
        console.error('Error validating discount:', error);
        showToast('Lỗi kết nối', 'danger');
    });
}

function proceedToCheckout() {
    console.log('Checkout initiated. Cart:', cart, 'Current user:', currentUser); // Debug log

    if (cart.length === 0) {
        showToast('Giỏ hàng trống', 'warning');
        return;
    }

    // Check if user is logged in - try multiple ways
    if (!currentUser) {
        // Try to reload user info first
        fetch('/api/user/profile')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentUser = data.user;
                    console.log('User reloaded for checkout:', currentUser);
                    proceedToCheckout(); // Retry checkout
                } else {
                    showToast('Vui lòng đăng nhập để thanh toán', 'warning');
                    // Optionally redirect to login
                    // window.location.href = '/login';
                }
            })
            .catch(error => {
                showToast('Vui lòng đăng nhập để thanh toán', 'warning');
            });
        return;
    }

    const checkoutBtn = document.getElementById('checkoutBtn');
    const originalText = checkoutBtn.innerHTML;
    checkoutBtn.innerHTML = '<i class="cil-reload fa-spin"></i> Đang xử lý...';
    checkoutBtn.disabled = true;

    const checkoutData = {
        cart_items: cart.map(item => ({
            product_id: item.product_id,
            quantity: item.quantity
        })),
        discount_code: document.getElementById('discountCodeInput').value.trim()
    };

    fetch('/api/marketplace/checkout', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(checkoutData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Clear cart
            cart = [];
            localStorage.removeItem('marketplace_cart');
            updateCartUI();

            // Hide cart modal
            coreui.Modal.getInstance(document.getElementById('cartModal')).hide();

            // Show success modal
            document.getElementById('successOrderNumber').textContent = `Đơn hàng #${data.order_number}`;
            new coreui.Modal(document.getElementById('checkoutSuccessModal')).show();

            // Reload user balance
            loadUserInfo();

        } else {
            showToast('Lỗi thanh toán: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showToast('Lỗi kết nối: ' + error, 'danger');
    })
    .finally(() => {
        checkoutBtn.innerHTML = originalText;
        checkoutBtn.disabled = false;
    });
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function setupEventListeners() {
    // Search and filter event listeners
    document.getElementById('searchInput').addEventListener('input', debounce(filterProducts, 300));
    document.getElementById('categoryFilter').addEventListener('change', function() {
        const categoryId = this.value;
        updateCategoryCardsState(categoryId);
        filterProducts();
    });
    document.getElementById('typeFilter').addEventListener('change', filterProducts);
    document.getElementById('priceFilter').addEventListener('change', filterProducts);
    document.getElementById('sortFilter').addEventListener('change', filterProducts);
}

function showToast(message, type = 'info') {
    // Create toast notification
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div class="toast" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Thông báo</strong>
                <button type="button" class="btn-close" data-coreui-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const toast = new coreui.Toast(toastElement);
    toast.show();
    
    setTimeout(() => {
        if (toastElement.parentElement) {
            toastElement.remove();
        }
    }, 5000);
}

function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}
</script>
{% endblock %}
