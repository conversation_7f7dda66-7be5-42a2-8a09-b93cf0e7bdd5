{% extends "base_coreui.html" %}

{% block title %}AFF Packages - Gói Thống Kê Doanh Thu{% endblock %}

{% block head_extra %}
<script src="/static/js/price-formatter.js"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h2 class="card-title mb-2">
                                <i class="cil-chart-line me-2"></i>AFF Packages
                            </h2>
                            <p class="card-text mb-0">
                                Gói subscription cho phép bạn xem thống kê doanh thu và analytics với giới hạn số account theo từng gói.
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <i class="cil-graph fa-3x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Subscription Status -->
    <div class="row mb-4" id="currentSubscriptionSection">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="cil-user me-2"></i>Subscription hiện tại
                    </h5>
                </div>
                <div class="card-body" id="currentSubscriptionContent">
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Đang tải...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Available Packages -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="cil-layers me-2"></i>Các gói có sẵn
            </h4>
        </div>
    </div>

    <div class="row" id="packagesContainer">
        <div class="col-12 text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Đang tải gói...</span>
            </div>
        </div>
    </div>
</div>

<!-- Purchase Modal -->
<div class="modal fade" id="purchaseModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="cil-credit-card me-2"></i>Mua gói subscription
                </h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="purchaseContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Upgrade Modal -->
<div class="modal fade" id="upgradeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="cil-arrow-circle-top me-2"></i>Nâng cấp gói subscription
                </h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="upgradeContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.package-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.package-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.package-card.recommended {
    border-color: #00C6AE;
    position: relative;
}

.package-card.recommended::before {
    content: "Khuyến nghị";
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: #00C6AE;
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: bold;
}

.package-card.current {
    border-color: #28a745;
    background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.price-display {
    font-size: 2rem;
    font-weight: bold;
    color: #00C6AE;
}

.price-period {
    font-size: 0.9rem;
    color: #6c757d;
}

.savings-badge {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 3px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: bold;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;
}

.feature-list li:last-child {
    border-bottom: none;
}

.feature-list li i {
    color: #28a745;
    margin-right: 8px;
}

.subscription-status {
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.subscription-status.active {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 1px solid #c3e6cb;
}

.subscription-status.expired {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 1px solid #f5c6cb;
}

.subscription-status.none {
    background: linear-gradient(135deg, #e2e3e5, #d6d8db);
    border: 1px solid #d6d8db;
}

.subscription-option {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #dee2e6;
}

.subscription-option:hover {
    border-color: #00C6AE;
    transform: translateY(-2px);
}

.subscription-option.border-primary {
    border-color: #00C6AE !important;
    background-color: rgba(0, 198, 174, 0.1);
}

.price-display {
    font-size: 1.5rem;
    font-weight: bold;
    color: #00C6AE;
}

.price-period {
    font-size: 0.9rem;
    color: #6c757d;
}

.savings-badge {
    background: #28a745;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 0.8rem;
    font-weight: bold;
    display: block;
    margin-top: 5px;
}
</style>

<script>
let currentSubscription = null;
let availablePackages = [];

document.addEventListener('DOMContentLoaded', function() {
    loadCurrentSubscription();
    loadAvailablePackages();
});

function loadCurrentSubscription() {
    fetch('/api/user/current-subscription')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentSubscription = data.subscription;
                displayCurrentSubscription();
            } else {
                displayNoSubscription();
            }
        })
        .catch(error => {
            console.error('Error loading subscription:', error);
            displayNoSubscription();
        });
}

function displayCurrentSubscription() {
    const container = document.getElementById('currentSubscriptionContent');
    
    if (!currentSubscription) {
        displayNoSubscription();
        return;
    }
    
    const status = currentSubscription.status;
    const statusClass = status === 'active' ? 'active' : (status === 'expired' ? 'expired' : 'none');
    
    container.innerHTML = `
        <div class="subscription-status ${statusClass}">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h6 class="mb-1">
                        <i class="cil-check-circle me-2"></i>${currentSubscription.package_name}
                    </h6>
                    <p class="mb-1">
                        <strong>Giới hạn:</strong> ${currentSubscription.account_limit} accounts |
                        <strong>Loại:</strong> ${currentSubscription.subscription_type === 'monthly' ? 'Tháng' : 'Năm'} |
                        <strong>Trạng thái:</strong> 
                        <span class="badge bg-${status === 'active' ? 'success' : 'danger'}">
                            ${status === 'active' ? 'Đang hoạt động' : 'Hết hạn'}
                        </span>
                    </p>
                    <p class="mb-0">
                        <small class="text-muted">
                            Từ ${formatDate(currentSubscription.start_date)} đến ${formatDate(currentSubscription.end_date)}
                        </small>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    ${status === 'active' ? 
                        `<button class="btn btn-outline-primary me-2" onclick="showUpgradeModal()">
                            <i class="cil-arrow-circle-top me-1"></i>Nâng cấp
                        </button>
                        <button class="btn btn-outline-secondary" onclick="manageSubscription()">
                            <i class="cil-settings me-1"></i>Quản lý
                        </button>` :
                        `<button class="btn btn-primary" onclick="renewSubscription()">
                            <i class="cil-reload me-1"></i>Gia hạn
                        </button>`
                    }
                </div>
            </div>
        </div>
    `;
}

function displayNoSubscription() {
    const container = document.getElementById('currentSubscriptionContent');
    container.innerHTML = `
        <div class="subscription-status none">
            <div class="text-center">
                <i class="cil-ban fa-2x text-muted mb-3"></i>
                <h6 class="text-muted">Bạn chưa có gói subscription nào</h6>
                <p class="text-muted mb-3">Chọn một gói bên dưới để bắt đầu xem thống kê doanh thu</p>
                <button class="btn btn-primary" onclick="scrollToPackages()">
                    <i class="cil-arrow-bottom me-1"></i>Chọn gói ngay
                </button>
            </div>
        </div>
    `;
}

function loadAvailablePackages() {
    fetch('/api/aff-packages')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                availablePackages = data.packages;
                displayPackages();
            } else {
                displayPackagesError();
            }
        })
        .catch(error => {
            console.error('Error loading packages:', error);
            displayPackagesError();
        });
}

function displayPackages() {
    const container = document.getElementById('packagesContainer');
    
    if (availablePackages.length === 0) {
        container.innerHTML = `
            <div class="col-12 text-center py-5">
                <i class="cil-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Chưa có gói nào</h5>
                <p class="text-muted">Vui lòng liên hệ admin để thêm gói subscription</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = availablePackages.map((pkg, index) => {
        const isRecommended = pkg.is_recommended; // From database
        const isCurrent = currentSubscription && currentSubscription.package_id === pkg.package_id;
        const monthlyPrice = pkg.monthly_price;
        const yearlyPrice = pkg.monthly_price * 12 * (100 - pkg.yearly_discount_percent) / 100;
        const yearlySavings = (pkg.monthly_price * 12) - yearlyPrice;
        
        return `
            <div class="col-lg-3 col-md-6 mb-4">
                <div class="card package-card h-100 ${isRecommended ? 'recommended' : ''} ${isCurrent ? 'current' : ''}">
                    <div class="card-body d-flex flex-column">
                        <div class="text-center mb-3">
                            <h5 class="card-title">${pkg.package_name}</h5>
                            <p class="text-muted small">${pkg.description}</p>
                        </div>
                        
                        <div class="text-center mb-3">
                            <div class="price-display" data-price="${monthlyPrice}"></div>
                            <div class="price-period">/ tháng</div>
                            ${pkg.yearly_discount_percent > 0 ?
                                `<div class="mt-2">
                                    <span class="savings-badge">Tiết kiệm <span data-price="${yearlySavings}"></span> khi mua năm</span>
                                </div>` : ''
                            }
                        </div>
                        
                        <ul class="feature-list mb-4 flex-grow-1">
                            <li><i class="cil-check"></i>Xem thống kê doanh thu</li>
                            <li><i class="cil-check"></i>Dashboard analytics</li>
                            <li><i class="cil-check"></i>Tối đa ${pkg.account_limit === 999999 ? 'Không giới hạn' : pkg.account_limit} accounts</li>
                            <li><i class="cil-check"></i>Upgrade/downgrade linh hoạt</li>
                            <li><i class="cil-check"></i>Hỗ trợ 24/7</li>
                        </ul>
                        
                        <div class="text-center">
                            ${isCurrent ? 
                                `<button class="btn btn-success w-100" disabled>
                                    <i class="cil-check me-1"></i>Gói hiện tại
                                </button>` :
                                `<button class="btn btn-primary w-100" onclick="showPurchaseModal(${pkg.package_id})">
                                    <i class="cil-credit-card me-1"></i>Chọn gói này
                                </button>`
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
    }).join('');

    // Format all prices after HTML is created
    setTimeout(() => {
        document.querySelectorAll('[data-price]').forEach(element => {
            const price = parseInt(element.getAttribute('data-price'));
            if (window.priceFormatter) {
                window.priceFormatter.formatProductPriceToElement(price, element);
            } else {
                element.textContent = new Intl.NumberFormat('vi-VN').format(price) + ' MP';
            }
        });
    }, 100);
}

function displayPackagesError() {
    const container = document.getElementById('packagesContainer');
    container.innerHTML = `
        <div class="col-12 text-center py-5">
            <i class="cil-warning fa-3x text-danger mb-3"></i>
            <h5 class="text-danger">Lỗi tải gói</h5>
            <p class="text-muted">Không thể tải danh sách gói. Vui lòng thử lại sau.</p>
            <button class="btn btn-outline-primary" onclick="loadAvailablePackages()">
                <i class="cil-reload me-1"></i>Thử lại
            </button>
        </div>
    `;
}

function showPurchaseModal(packageId) {
    const pkg = availablePackages.find(p => p.package_id === packageId);
    if (!pkg) return;
    
    const modal = new coreui.Modal(document.getElementById('purchaseModal'));
    const content = document.getElementById('purchaseContent');
    
    const monthlyPrice = pkg.monthly_price;
    const yearlyPrice = pkg.monthly_price * 12 * (100 - pkg.yearly_discount_percent) / 100;
    const yearlySavings = (pkg.monthly_price * 12) - yearlyPrice;
    
    content.innerHTML = `
        <div class="text-center mb-4">
            <h4>${pkg.package_name}</h4>
            <p class="text-muted">${pkg.description}</p>
        </div>
        
        <div class="row">
            <div class="col-md-6 mb-3">
                <div class="card subscription-option" data-type="monthly" onclick="selectSubscriptionType('monthly')">
                    <div class="card-body text-center">
                        <h5>Gói tháng</h5>
                        <div class="price-display" data-price="${monthlyPrice}"></div>
                        <div class="price-period">/ tháng</div>
                        <p class="text-muted mt-2">Thanh toán hàng tháng</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-3">
                <div class="card subscription-option" data-type="yearly" onclick="selectSubscriptionType('yearly')">
                    <div class="card-body text-center">
                        <h5>Gói năm</h5>
                        <div class="price-display" data-price="${yearlyPrice}"></div>
                        <div class="price-period">/ năm (<span data-price="${Math.round(yearlyPrice/12)}"></span>/tháng)</div>
                        <span class="savings-badge">Tiết kiệm <span data-price="${yearlySavings}"></span></span>
                        <p class="text-muted mt-2">Thanh toán 1 lần cho cả năm</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center">
            <button class="btn btn-primary btn-lg" id="confirmPurchaseBtn" onclick="confirmPurchase(${packageId})" disabled>
                <i class="cil-credit-card me-1"></i>Xác nhận mua gói
            </button>
        </div>
    `;

    // Format all prices in modal after HTML is created
    setTimeout(() => {
        document.querySelectorAll('#purchaseModal [data-price]').forEach(element => {
            const price = parseInt(element.getAttribute('data-price'));
            if (window.priceFormatter) {
                window.priceFormatter.formatProductPriceToElement(price, element);
            } else {
                element.textContent = new Intl.NumberFormat('vi-VN').format(price) + ' MP';
            }
        });
    }, 100);

    modal.show();
}

let selectedSubscriptionType = null;

function selectSubscriptionType(type) {
    selectedSubscriptionType = type;
    
    // Update UI
    document.querySelectorAll('.subscription-option').forEach(el => {
        el.classList.remove('border-primary');
    });
    document.querySelector(`[data-type="${type}"]`).classList.add('border-primary');
    
    // Enable purchase button
    document.getElementById('confirmPurchaseBtn').disabled = false;
}

function confirmPurchase(packageId) {
    if (!selectedSubscriptionType) return;
    
    const btn = document.getElementById('confirmPurchaseBtn');
    btn.disabled = true;
    btn.innerHTML = '<i class="cil-reload fa-spin me-1"></i>Đang xử lý...';
    
    fetch('/api/aff-packages/purchase', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            package_id: packageId,
            subscription_type: selectedSubscriptionType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal and reload page
            coreui.Modal.getInstance(document.getElementById('purchaseModal')).hide();
            location.reload();
        } else {
            alert('Lỗi: ' + data.error);
            btn.disabled = false;
            btn.innerHTML = '<i class="cil-credit-card me-1"></i>Xác nhận mua gói';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra. Vui lòng thử lại.');
        btn.disabled = false;
        btn.innerHTML = '<i class="cil-credit-card me-1"></i>Xác nhận mua gói';
    });
}

function formatPrice(amount) {
    // Always return MP format for string usage, use formatPriceToElement for DOM
    return new Intl.NumberFormat('vi-VN').format(amount) + ' MP';
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return 'N/A';
        return date.toLocaleDateString('vi-VN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
        });
    } catch (e) {
        console.error('Error formatting date:', dateString, e);
        return 'N/A';
    }
}

function scrollToPackages() {
    document.getElementById('packagesContainer').scrollIntoView({ behavior: 'smooth' });
}

function showUpgradeModal() {
    // TODO: Implement upgrade modal
    alert('Tính năng nâng cấp sẽ được triển khai sau');
}

function manageSubscription() {
    // TODO: Implement subscription management
    alert('Tính năng quản lý subscription sẽ được triển khai sau');
}

function renewSubscription() {
    // TODO: Implement renewal
    alert('Tính năng gia hạn sẽ được triển khai sau');
}
</script>
{% endblock %}
