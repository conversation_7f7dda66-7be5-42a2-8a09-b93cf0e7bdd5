{% extends "base_coreui.html" %}
{% block title %}Đ<PERSON><PERSON> hàng của tôi - Marketplace{% endblock %}

{% block head_extra %}
<style>
    .order-card {
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        margin-bottom: 20px;
        transition: all 0.3s ease;
    }
    .order-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
    .order-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 12px 12px 0 0;
        padding: 15px 20px;
        border-bottom: 1px solid #e0e0e0;
    }
    .order-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }
    .account-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 8px;
    }
    .file-item {
        transition: all 0.3s ease;
    }
    .file-item:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .file-icon {
        width: 50px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="cil-list text-primary"></i> Đơn hàng của tôi</h2>
                    <p class="text-muted">Xem lịch sử mua hàng và trạng thái đơn hàng</p>
                </div>
                <a href="/marketplace" class="btn btn-outline-primary">
                    <i class="cil-basket"></i> Tiếp tục mua sắm
                </a>
            </div>
        </div>
    </div>

    <!-- Filter & Search -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <input type="text" class="form-control" id="searchInput" placeholder="Tìm theo mã đơn hàng...">
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="statusFilter">
                        <option value="">Tất cả trạng thái</option>
                        <option value="completed">Hoàn thành</option>
                        <option value="pending">Đang xử lý</option>
                        <option value="cancelled">Đã hủy</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" id="timeFilter">
                        <option value="">Tất cả thời gian</option>
                        <option value="7">7 ngày qua</option>
                        <option value="30">30 ngày qua</option>
                        <option value="90">3 tháng qua</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-primary w-100" onclick="loadOrders()">
                        <i class="cil-reload"></i> Tải lại
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Template Version Check -->
    <div class="alert alert-warning mb-3">
        <strong>🔧 Template Debug v3.0 - Fixed:</strong> Nếu bạn thấy thông báo này, template đã được cập nhật.
        <br><strong>Nếu không thấy nút "Chi tiết đầy đủ":</strong>
        <ol class="mb-0 mt-2">
            <li>Hard refresh: Ctrl+F5 (Windows) hoặc Cmd+Shift+R (Mac)</li>
            <li>Mở F12 Console và click nút Debug bên dưới</li>
            <li>Kiểm tra có lỗi JavaScript không</li>
        </ol>
        <div class="mt-2">
            <button class="btn btn-sm btn-primary" onclick="debugOrders()">Debug Orders</button>
            <button class="btn btn-sm btn-success ms-1" onclick="displayOrders()">Force Refresh</button>
            <button class="btn btn-sm btn-warning ms-1" onclick="testRenderOrder()">Test Render</button>
            <a href="/marketplace/orders/1" class="btn btn-sm btn-info ms-1">Test Detail Link</a>
        </div>
    </div>

    <!-- Test Order Card (Static) -->
    <div class="order-card mb-3" style="border: 2px solid #00C6AE;">
        <div class="order-header">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h6 class="mb-1">🧪 TEST ORDER #12345</h6>
                    <small class="text-muted">Template đã được cập nhật - v2.0</small>
                </div>
                <div class="col-md-3 text-center">
                    <span class="order-status bg-success text-white">Hoàn thành</span>
                </div>
                <div class="col-md-3 text-end">
                    <div class="fw-bold text-primary">500,000 MP</div>
                    <small class="text-muted">1 sản phẩm</small>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-8">
                    <h6>Sản phẩm đã mua:</h6>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <strong>SP WIN T8</strong>
                            <span class="badge bg-info ms-2">Win Product</span>
                        </div>
                        <div class="text-end">
                            <span>1 x 500,000 MP</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex flex-column gap-2">
                        <a href="/marketplace/orders/1" class="btn btn-primary btn-sm">
                            <i class="cil-info"></i> Chi tiết đầy đủ
                        </a>
                        <button class="btn btn-outline-info btn-sm" onclick="alert('Xem nhanh clicked!')">
                            <i class="cil-list"></i> Xem nhanh
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders List -->
    <div id="ordersContainer">
        <div class="text-center py-5">
            <i class="cil-reload fa-spin fa-2x text-muted"></i>
            <p class="text-muted mt-2">Đang tải đơn hàng...</p>
        </div>
    </div>
</div>

<!-- Order Detail Modal -->
<div class="modal fade" id="orderDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderDetailTitle">Chi tiết đơn hàng</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="orderDetailBody">
                <!-- Order details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-outline-primary" id="warrantyBtn" style="display: none;">
                    <i class="cil-shield-alt"></i> Yêu cầu bảo hành
                </button>
            </div>
        </div>
    </div>
</div>

<script>
console.log('📜 Script loading...');

// Global variables
let orders = [];
let currentUser = null;

console.log('✅ Variables initialized:', {orders, currentUser});

// Make variables globally accessible
window.orders = orders;
window.currentUser = currentUser;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Page loaded - orders variable:', typeof orders);
    loadUserInfo();
    loadOrders();
    setupEventListeners();
});

function loadUserInfo() {
    fetch('/api/user/profile')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentUser = data.user;
            }
        })
        .catch(error => console.error('Error loading user info:', error));
}

function loadOrders() {
    console.log('📦 Loading orders...');
    try {
        const container = document.getElementById('ordersContainer');
        if (!container) {
            console.error('❌ Container not found!');
            return;
        }

        container.innerHTML = '<div class="text-center py-5"><i class="cil-reload fa-spin fa-2x text-muted"></i><p class="text-muted mt-2">Đang tải đơn hàng...</p></div>';

        fetch('/api/marketplace/orders')
        .then(response => response.json())
        .then(data => {
            console.log('API Response:', data); // Debug log
            if (data.success) {
                orders = data.orders;
                console.log('Orders loaded:', orders.length); // Debug log
                displayOrders();
            } else {
                container.innerHTML = '<div class="text-center py-5"><p class="text-danger">Lỗi: ' + data.error + '</p></div>';
            }
        })
        .catch(error => {
            console.error('❌ Fetch error:', error);
            container.innerHTML = '<div class="text-center py-5"><p class="text-danger">Lỗi kết nối: ' + error + '</p></div>';
        });
    } catch (error) {
        console.error('❌ LoadOrders error:', error);
    }
}

function displayOrders() {
    const container = document.getElementById('ordersContainer');
    console.log('Displaying orders:', orders); // Debug log

    if (orders.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="cil-basket fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Chưa có đơn hàng nào</h5>
                <p class="text-muted">Hãy khám phá marketplace và mua sắm ngay!</p>
                <a href="/marketplace" class="btn btn-primary">Bắt đầu mua sắm</a>
            </div>
        `;
        return;
    }
    
    container.innerHTML = '';
    
    orders.forEach(order => {
        const orderCard = document.createElement('div');
        orderCard.className = 'order-card';
        
        const statusClass = getStatusClass(order.status);
        const statusText = getStatusText(order.status);
        
        orderCard.innerHTML = `
            <div class="order-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h6 class="mb-1">Đơn hàng #${order.order_number}</h6>
                        <small class="text-muted">Đặt ngày: ${formatDate(order.created_at)}</small>
                    </div>
                    <div class="col-md-3 text-center">
                        <span class="order-status ${statusClass}">${statusText}</span>
                    </div>
                    <div class="col-md-3 text-end">
                        <div class="fw-bold text-primary">${order.final_amount.toLocaleString()} MP</div>
                        <small class="text-muted">${order.items.length} sản phẩm</small>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6>Sản phẩm đã mua:</h6>
                        ${order.items.map(item => `
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div>
                                    <strong>${item.product_name}</strong>
                                    <span class="badge bg-info ms-2">${getProductTypeLabel(item.product_type, item.type_name)}</span>
                                </div>
                                <div class="text-end">
                                    <span>${item.quantity} x ${item.unit_price.toLocaleString()} MP</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="d-flex flex-column gap-2">
                            <a href="/marketplace/orders/${order.order_id}" class="btn btn-primary btn-sm">
                                <i class="cil-info"></i> Chi tiết đầy đủ
                            </a>
                            <button class="btn btn-outline-info btn-sm" onclick="showOrderDetail(${order.order_id})">
                                <i class="cil-list"></i> Xem nhanh
                            </button>
                            ${order.status === 'completed' && hasAccountProducts(order.items) ?
                                `<button class="btn btn-outline-warning btn-sm" onclick="requestWarranty(${order.order_id})">
                                    <i class="cil-shield-alt"></i> Bảo hành
                                </button>` : ''
                            }
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.appendChild(orderCard);
    });
}

function getStatusClass(status) {
    const classes = {
        'completed': 'bg-success text-white',
        'pending': 'bg-warning text-dark',
        'cancelled': 'bg-danger text-white',
        'refunded': 'bg-secondary text-white'
    };
    return classes[status] || 'bg-secondary text-white';
}

function getStatusText(status) {
    const texts = {
        'completed': 'Hoàn thành',
        'pending': 'Đang xử lý',
        'cancelled': 'Đã hủy',
        'refunded': 'Đã hoàn tiền'
    };
    return texts[status] || status;
}

function getProductTypeLabel(type, typeName) {
    // Ưu tiên hiển thị tên ProductType gốc nếu có
    if (typeName) {
        return typeName;
    }

    // Fallback cho các type cũ
    const labels = {
        'account': 'Account',
        'win_product': 'Win Product',
        'course': 'Khóa học',
        'aff_package': 'AFF Package'
    };
    return labels[type] || type;
}

function hasAccountProducts(items) {
    return items.some(item => item.product_type === 'account');
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN') + ' ' + date.toLocaleTimeString('vi-VN', {hour: '2-digit', minute: '2-digit'});
}

function showOrderDetail(orderId) {
    const order = orders.find(o => o.order_id === orderId);
    if (!order) return;
    
    document.getElementById('orderDetailTitle').textContent = `Chi tiết đơn hàng #${order.order_number}`;
    
    const detailBody = document.getElementById('orderDetailBody');
    detailBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>Thông tin đơn hàng</h6>
                <table class="table table-sm">
                    <tr><td>Mã đơn hàng:</td><td><strong>#${order.order_number}</strong></td></tr>
                    <tr><td>Ngày đặt:</td><td>${formatDate(order.created_at)}</td></tr>
                    <tr><td>Trạng thái:</td><td><span class="order-status ${getStatusClass(order.status)}">${getStatusText(order.status)}</span></td></tr>
                    <tr><td>Tạm tính:</td><td>${order.total_amount.toLocaleString()} MP</td></tr>
                    ${order.discount_amount > 0 ? `<tr><td>Giảm giá:</td><td class="text-success">-${order.discount_amount.toLocaleString()} MP</td></tr>` : ''}
                    <tr><td><strong>Tổng cộng:</strong></td><td><strong class="text-primary">${order.final_amount.toLocaleString()} MP</strong></td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Chi tiết sản phẩm</h6>
                ${order.items.map(item => `
                    <div class="card mb-2">
                        <div class="card-body p-3">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>${item.product_name}</strong>
                                    <br><span class="badge bg-info">${getProductTypeLabel(item.product_type, item.type_name)}</span>
                                </div>
                                <div class="text-end">
                                    <div>${item.quantity} x ${item.unit_price.toLocaleString()} MP</div>
                                    <strong>${item.total_price.toLocaleString()} MP</strong>
                                </div>
                            </div>
                            ${item.product_type === 'account' && item.assigned_accounts ? `
                                <div class="mt-2">
                                    <small class="text-muted">Accounts đã gán:</small>
                                    ${item.assigned_accounts.map(acc => `
                                        <div class="account-item">
                                            <strong>${acc.account_name}</strong>
                                            <span class="badge bg-success ms-2">${acc.status}</span>
                                        </div>
                                    `).join('')}
                                </div>
                            ` : ''}
                            ${item.product_type !== 'account' ? `
                                <div class="mt-2">
                                    <button class="btn btn-sm btn-outline-primary" onclick="loadProductFiles(${item.product_id}, ${item.item_id})">
                                        <i class="cil-folder-open"></i> Xem Files
                                    </button>
                                </div>
                                <div id="productFiles_${item.item_id}" class="mt-2" style="display: none;">
                                    <!-- Files will be loaded here -->
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
    
    new coreui.Modal(document.getElementById('orderDetailModal')).show();
}

function requestWarranty(orderId) {
    // Redirect to warranty page with order ID
    window.location.href = `/marketplace/warranty?order_id=${orderId}`;
}

// Product Files Functions
function loadProductFiles(productId, itemId) {
    const container = document.getElementById(`productFiles_${itemId}`);
    const button = event.target;

    // Toggle visibility
    if (container.style.display === 'none') {
        container.style.display = 'block';
        button.innerHTML = '<i class="cil-folder-open"></i> Ẩn Files';

        // Load files if not loaded yet
        if (!container.hasAttribute('data-loaded')) {
            container.innerHTML = `
                <div class="text-center py-3">
                    <i class="cil-reload fa-spin"></i> Đang tải files...
                </div>
            `;

            fetch(`/api/products/${productId}/files`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayProductFiles(data.files, container);
                        container.setAttribute('data-loaded', 'true');
                    } else {
                        container.innerHTML = `
                            <div class="alert alert-warning">
                                <i class="cil-warning"></i> ${data.error}
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error loading files:', error);
                    container.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="cil-warning"></i> Lỗi kết nối: ${error.message}
                        </div>
                    `;
                });
        }
    } else {
        container.style.display = 'none';
        button.innerHTML = '<i class="cil-folder-open"></i> Xem Files';
    }
}

function displayProductFiles(files, container) {
    if (files.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="cil-info"></i> Sản phẩm này chưa có files đính kèm
            </div>
        `;
        return;
    }

    container.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="cil-folder-open"></i> Files sản phẩm (${files.length} files)</h6>
            </div>
            <div class="card-body">
                ${files.map(file => `
                    <div class="file-item d-flex align-items-center justify-content-between p-2 border rounded mb-2">
                        <div class="d-flex align-items-center">
                            <div class="file-icon me-3">
                                <i class="${getFileIcon(file.file_type)} fa-2x text-${getFileColor(file.file_type)}"></i>
                            </div>
                            <div>
                                <div class="fw-bold">${file.original_name}</div>
                                <small class="text-muted">
                                    ${formatFileSize(file.file_size)} • ${file.file_type.toUpperCase()}
                                    ${file.is_preview ? ' • <span class="badge bg-info">Preview</span>' : ''}
                                </small>
                                ${file.description ? `<div class="text-muted small">${file.description}</div>` : ''}
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="${file.download_url}" class="btn btn-primary btn-sm" target="_blank">
                                <i class="cil-cloud-download"></i> Tải về
                            </a>
                            <div class="small text-muted mt-1">
                                <i class="cil-chart-line"></i> ${file.download_count} lượt tải
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

function getFileIcon(type) {
    const icons = {
        'image': 'cil-image',
        'document': 'cil-description',
        'video': 'cil-video',
        'audio': 'cil-audio-spectrum',
        'other': 'cil-file'
    };
    return icons[type] || 'cil-file';
}

function getFileColor(type) {
    const colors = {
        'image': 'success',
        'document': 'danger',
        'video': 'primary',
        'audio': 'warning',
        'other': 'secondary'
    };
    return colors[type] || 'secondary';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function setupEventListeners() {
    document.getElementById('searchInput').addEventListener('input', filterOrders);
    document.getElementById('statusFilter').addEventListener('change', filterOrders);
    document.getElementById('timeFilter').addEventListener('change', filterOrders);
}

function filterOrders() {
    // Implementation for filtering orders
    // Will be enhanced based on needs
}

// Debug Functions
function debugOrders() {
    console.log('=== DEBUG ORDERS ===');
    console.log('Orders array:', orders);
    console.log('Orders count:', orders.length);
    console.log('Orders container:', document.getElementById('ordersContainer'));

    if (orders.length > 0) {
        console.log('First order:', orders[0]);
        console.log('First order items:', orders[0].items);
    }

    // Test if functions exist
    console.log('hasAccountProducts function:', typeof hasAccountProducts);
    console.log('getProductTypeLabel function:', typeof getProductTypeLabel);
    console.log('getStatusClass function:', typeof getStatusClass);
    console.log('getStatusText function:', typeof getStatusText);
    console.log('formatDate function:', typeof formatDate);
}

function testRenderOrder() {
    console.log('=== TEST RENDER ORDER ===');

    // Create a fake order for testing
    const testOrder = {
        order_id: 999,
        order_number: 'TEST123',
        final_amount: 500000,
        status: 'completed',
        created_at: new Date().toISOString(),
        items: [
            {
                product_name: 'SP WIN T8',
                product_type: 'win_product',
                quantity: 1,
                unit_price: 500000
            }
        ]
    };

    console.log('Test order:', testOrder);

    // Temporarily add test order to orders array
    const originalOrders = [...orders];
    orders = [testOrder];

    try {
        displayOrders();
        console.log('✅ Test render successful');
    } catch (error) {
        console.error('❌ Test render failed:', error);
    }

    // Restore original orders
    orders = originalOrders;
}
</script>
{% endblock %}
