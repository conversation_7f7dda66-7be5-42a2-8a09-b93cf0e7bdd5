{% extends "base_coreui.html" %}
{% block title %}T<PERSON>o sản phẩm mới - Marketplace{% endblock %}

{% block head_extra %}
<style>
    .upload-area {
        border: 2px dashed #ccc;
        border-radius: 8px;
        padding: 30px;
        text-align: center;
        cursor: pointer;
        transition: border-color 0.3s;
        min-height: 150px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .upload-area:hover {
        border-color: #00C6AE;
    }
    .upload-area.dragover {
        border-color: #00C6AE;
        background-color: #f0f9ff;
    }
    .account-item {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 8px;
        transition: all 0.3s ease;
    }
    .account-item:hover {
        border-color: #00C6AE;
        background-color: #f8f9fa;
    }
    .account-item.selected {
        border-color: #00C6AE;
        background-color: #e8f5f3;
    }
    .accounts-grid {
        max-height: 500px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
    }
    .form-section {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    .section-title {
        color: #00C6AE;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }
    .stats-badge {
        background: linear-gradient(135deg, #00C6AE, #00a693);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
    }
    .file-upload-area {
        border: 2px dashed #ccc;
        border-radius: 8px;
        padding: 40px 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #fafafa;
    }
    .file-upload-area:hover {
        border-color: #00C6AE;
        background: #f0f9ff;
    }
    .file-upload-area.dragover {
        border-color: #00C6AE;
        background: #e8f5f3;
        transform: scale(1.02);
    }
    .selected-files-container {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        background: white;
    }
    .file-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 10px;
        background: white;
        transition: all 0.3s ease;
    }
    .file-item:hover {
        border-color: #00C6AE;
        box-shadow: 0 2px 8px rgba(0,198,174,0.1);
    }
    .file-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 18px;
        color: white;
    }
    .file-icon.image { background: #28a745; }
    .file-icon.document { background: #dc3545; }
    .file-icon.video { background: #6f42c1; }
    .file-icon.audio { background: #fd7e14; }
    .file-icon.other { background: #6c757d; }
    .file-info {
        flex: 1;
    }
    .file-name {
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;
    }
    .file-meta {
        font-size: 12px;
        color: #6c757d;
    }
    .file-actions {
        display: flex;
        gap: 8px;
    }
    .file-preview {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 6px;
        margin-right: 12px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/admin/marketplace/products">Quản lý sản phẩm</a></li>
                            <li class="breadcrumb-item active">Tạo sản phẩm mới</li>
                        </ol>
                    </nav>
                    <h2><i class="cil-plus text-primary"></i> Tạo sản phẩm mới</h2>
                </div>
                <div>
                    <a href="/admin/marketplace/products" class="btn btn-outline-secondary me-2">
                        <i class="cil-arrow-left"></i> Quay lại
                    </a>
                    <button type="button" class="btn btn-primary" onclick="submitProduct()">
                        <i class="cil-check"></i> Tạo sản phẩm
                    </button>
                </div>
            </div>
        </div>
    </div>

    <form id="createProductForm" enctype="multipart/form-data">
        <div class="row">
            <!-- Left Column - Basic Info -->
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="form-section">
                    <h4 class="section-title"><i class="cil-info"></i> Thông tin cơ bản</h4>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Tên sản phẩm *</label>
                                <input type="text" class="form-control" name="name" required placeholder="Nhập tên sản phẩm...">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Danh mục *</label>
                                <select class="form-select" name="category_id" required onchange="loadProductTypesByCategory(this.value)">
                                    <option value="">Chọn danh mục</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Loại sản phẩm *</label>
                                <select class="form-select" name="product_type_id" required onchange="toggleProductTypeFields(this.value)">
                                    <option value="">Chọn danh mục trước</option>
                                </select>
                                <div class="form-text">Chọn danh mục để xem các loại sản phẩm có thể</div>
                            </div>
                        </div>
                        <div class="col-md-4" id="priceSection">
                            <div class="mb-3">
                                <label class="form-label">Giá bán (MP) *</label>
                                <input type="number" class="form-control" name="price" min="0" required placeholder="0" onchange="updatePreview()">
                                <div class="form-text">Giá cố định cho sản phẩm thường</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Trạng thái *</label>
                                <select class="form-select" name="status" required onchange="updatePreview()">
                                    <option value="active">🟢 Hoạt động</option>
                                    <option value="inactive">🔴 Tạm dừng</option>
                                </select>
                                <div class="form-text">Sản phẩm tạm dừng sẽ không hiển thị cho khách hàng</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Mô tả ngắn</label>
                        <input type="text" class="form-control" name="short_description" maxlength="500" placeholder="Mô tả ngắn hiển thị trên card sản phẩm...">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Mô tả chi tiết</label>
                        <textarea class="form-control" name="description" rows="5" placeholder="Mô tả chi tiết về sản phẩm, tính năng, lợi ích..."></textarea>
                    </div>

                    <div class="row" id="stockSection">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Số lượng kho</label>
                                <input type="number" class="form-control" name="stock" min="0" value="0" id="stockInput">
                                <small class="text-muted">Sẽ tự động cập nhật cho Account Package</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3 form-check mt-4">
                                <input type="checkbox" class="form-check-input" name="unlimited_stock" id="unlimitedStock">
                                <label class="form-check-label" for="unlimitedStock">
                                    Không giới hạn kho
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" name="is_featured" id="isFeatured">
                        <label class="form-check-label" for="isFeatured">
                            Sản phẩm nổi bật
                        </label>
                    </div>
                </div>

                <!-- Product Type Specific Settings -->
                
                <!-- AFF Package Settings -->
                <div id="affPackageSettings" class="form-section" style="display: none;">
                    <h4 class="section-title"><i class="cil-chart-line"></i> Cấu hình AFF Package Subscription</h4>
                    <div class="alert alert-info">
                        <i class="cil-info"></i> <strong>AFF Package</strong> là gói subscription theo tháng/năm cho phép user xem thống kê doanh thu với giới hạn số account. Không cần thiết lập giá bán cố định.
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Giới hạn số account *</label>
                                <input type="number" class="form-control" name="account_limit" min="1" value="10" required>
                                <div class="form-text">Số account tối đa được xem thống kê</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Giá tháng (MP) *</label>
                                <input type="number" class="form-control" name="monthly_price" min="1000" value="300000" required onchange="calculateYearlyPrice()">
                                <div class="form-text">Giá subscription theo tháng</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Giảm giá năm (%)</label>
                                <input type="number" class="form-control" name="yearly_discount_percent" min="0" max="50" value="20" onchange="calculateYearlyPrice()">
                                <div class="form-text">% giảm giá khi mua gói năm</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">💰 Tính toán giá</h6>
                                    <div id="priceCalculation">
                                        <div class="d-flex justify-content-between">
                                            <span>Giá tháng:</span>
                                            <strong id="monthlyPriceDisplay">300,000 MP</strong>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>Giá năm (gốc):</span>
                                            <span id="yearlyOriginalPrice">3,600,000 MP</span>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>Giá năm (sau giảm):</span>
                                            <strong class="text-success" id="yearlyDiscountedPrice">2,880,000 MP</strong>
                                        </div>
                                        <div class="d-flex justify-content-between">
                                            <span>Tiết kiệm:</span>
                                            <strong class="text-primary" id="yearlySavings">720,000 MP</strong>
                                        </div>
                                        <hr>
                                        <div class="d-flex justify-content-between">
                                            <span>Giá tháng hiệu quả:</span>
                                            <strong class="text-success" id="effectiveMonthlyPrice">240,000 MP</strong>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">📊 Tính năng gói</h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><i class="cil-check text-success"></i> Xem thống kê doanh thu</li>
                                        <li><i class="cil-check text-success"></i> Xem dashboard analytics</li>
                                        <li><i class="cil-check text-success"></i> Giới hạn <span id="accountLimitDisplay">10</span> accounts</li>
                                        <li><i class="cil-check text-success"></i> Upgrade/downgrade linh hoạt</li>
                                        <li><i class="cil-check text-success"></i> Tính toán bù trừ thông minh</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" name="auto_renew_default" id="autoRenewDefault">
                        <label class="form-check-label" for="autoRenewDefault">
                            Bật tự động gia hạn mặc định
                        </label>
                        <div class="form-text">User sẽ được bật auto-renew khi mua gói này</div>
                    </div>
                </div>

                <!-- Video Links Section (for videos products) -->
                <div id="videoLinksSection" class="form-section" style="display: none;">
                    <h4 class="section-title"><i class="cil-video"></i> Video Links</h4>
                    <p class="text-muted">Chọn các video links để bán cho sản phẩm này</p>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">Lọc theo loại video</label>
                            <select class="form-select" id="videoTypeFilter" onchange="loadAvailableVideoLinks()">
                                <option value="">Tất cả loại</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Lọc theo số lượng</label>
                            <select class="form-select" id="videoCountFilter" onchange="loadAvailableVideoLinks()">
                                <option value="">Tất cả</option>
                                <option value="1-50">1-50 videos</option>
                                <option value="51-100">51-100 videos</option>
                                <option value="101+">101+ videos</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">Tìm kiếm</label>
                            <input type="text" class="form-control" id="videoSearchInput" placeholder="Tên link..." onkeyup="loadAvailableVideoLinks()">
                        </div>
                    </div>

                    <!-- Available Video Links -->
                    <div class="card">
                        <div class="card-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">Video Links có sẵn</h6>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllVideoLinks()">
                                        <i class="cil-check"></i> Chọn tất cả
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearVideoLinksSelection()">
                                        <i class="cil-x"></i> Bỏ chọn
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="availableVideoLinksContainer">
                                <div class="text-center py-3">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="mt-2">Đang tải video links...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Selected Video Links Summary -->
                    <div class="mt-3">
                        <div class="alert alert-info">
                            <h6><i class="cil-info"></i> Thông tin tồn kho</h6>
                            <p class="mb-0">
                                Đã chọn: <strong id="selectedVideoLinksCount">0</strong> video links<br>
                                Tồn kho sản phẩm sẽ là: <strong id="calculatedStock">0</strong>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Product Files Upload -->
                <div id="productFilesSection" class="form-section" style="display: none;">
                    <h4 class="section-title"><i class="cil-folder-open"></i> Files đính kèm</h4>
                    <p class="text-muted mb-3">Upload files cho sản phẩm (hình ảnh, tài liệu, video). Khách hàng sẽ có thể tải về sau khi mua.</p>

                    <!-- File Upload Area -->
                    <div class="mb-3">
                        <label class="form-label">Chọn files để upload</label>
                        <div class="file-upload-area" onclick="document.getElementById('productFilesInput').click()">
                            <i class="cil-cloud-upload fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Click để chọn files hoặc kéo thả vào đây</h6>
                            <p class="text-muted mb-0">Hỗ trợ: Hình ảnh, PDF, DOC, Video, Audio</p>
                            <small class="text-muted">Tối đa 50MB mỗi file</small>
                        </div>
                        <input type="file" id="productFilesInput" multiple accept="image/*,.pdf,.doc,.docx,.txt,.mp4,.avi,.mov,.mp3,.wav" style="display: none;" onchange="handleFileSelection(this)">
                    </div>

                    <!-- Selected Files List -->
                    <div id="selectedFilesList" class="selected-files-container">
                        <!-- Files will be displayed here -->
                    </div>
                </div>

                <!-- Account Package Settings -->
                <div id="accountPackageSettings" class="form-section" style="display: none;">
                    <h4 class="section-title"><i class="cil-user"></i> Cấu hình Account Package</h4>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">Tiêu chí account</label>
                                <textarea class="form-control" name="account_criteria" rows="3" placeholder="VD: Follower > 1000, Status = Live, Cookie có sẵn..."></textarea>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Thời gian bảo hành (ngày)</label>
                                <input type="number" class="form-control" name="warranty_days" min="0" value="3">
                            </div>
                        </div>
                    </div>

                    <!-- Account Selection -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <label class="form-label mb-0">Chọn accounts cho package</label>
                            <div>
                                <span class="stats-badge me-2">Đã chọn: <span id="selectedAccountsCount">0</span> accounts</span>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadAvailableAccounts()">
                                    <i class="cil-reload"></i> Tải lại
                                </button>
                            </div>
                        </div>
                        
                        <!-- Filter accounts -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <input type="text" class="form-control" id="accountSearchInput" placeholder="Tìm theo tên account...">
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="accountStatusFilter">
                                    <option value="">Tất cả trạng thái</option>
                                    <option value="Live">Live</option>
                                    <option value="Có giỏ">Có giỏ</option>
                                    <option value="Đang nuôi">Đang nuôi</option>
                                    <option value="Đủ điều kiện">Đủ điều kiện</option>
                                    <option value="Bật hụt">Bật hụt</option>
                                    <option value="Thu giỏ">Thu giỏ</option>
                                    <option value="Die">Die</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select class="form-select" id="accountTeamFilter">
                                    <option value="">Tất cả team</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <div class="row">
                                    <div class="col-6">
                                        <input type="number" class="form-control" id="minFollowersFilter" placeholder="Min followers" min="0">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control" id="maxFollowersFilter" placeholder="Max followers" min="0">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="selectAllAccounts">
                                    <label class="form-check-label" for="selectAllAccounts">Chọn tất cả</label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Accounts list -->
                        <div id="availableAccountsList" class="accounts-grid">
                            <div class="text-center text-muted py-4">
                                <i class="cil-info"></i> Chọn loại sản phẩm "Account Package" để tải danh sách accounts
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column - Thumbnail & Preview -->
            <div class="col-lg-4">
                <div class="form-section">
                    <h4 class="section-title"><i class="cil-image"></i> Hình ảnh sản phẩm</h4>
                    
                    <div class="upload-area" onclick="document.getElementById('thumbnailInput').click()">
                        <i class="cil-cloud-upload fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">Click để chọn hình ảnh</h6>
                        <p class="text-muted mb-0">hoặc kéo thả vào đây</p>
                        <small class="text-muted">Hỗ trợ: JPG, PNG, GIF (tối đa 5MB)</small>
                    </div>
                    
                    <input type="file" id="thumbnailInput" name="thumbnail" accept="image/*" style="display: none;" onchange="previewThumbnail(this)">
                    
                    <div id="thumbnailPreview" class="mt-3" style="display: none;">
                        <img id="thumbnailImg" class="img-fluid rounded" style="width: 100%;">
                        <button type="button" class="btn btn-sm btn-outline-danger mt-2 w-100" onclick="removeThumbnail()">
                            <i class="cil-trash"></i> Xóa hình
                        </button>
                    </div>
                </div>

                <!-- Product Preview -->
                <div class="form-section">
                    <h4 class="section-title"><i class="cil-eye"></i> Preview sản phẩm</h4>
                    <div id="productPreview">
                        <div class="card">
                            <div class="position-relative">
                                <img id="previewImage" src="/static/marketplace/thumbnails/default.png" class="card-img-top" style="height: 200px; object-fit: cover;">
                                <span id="previewBadge" class="badge bg-info position-absolute" style="top: 10px; right: 10px;">Chưa chọn loại</span>
                            </div>
                            <div class="card-body">
                                <h6 id="previewTitle" class="card-title">Tên sản phẩm</h6>
                                <p id="previewDescription" class="card-text text-muted small">Mô tả ngắn sẽ hiển thị ở đây</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span id="previewPrice" class="text-primary fw-bold">0 MP</span>
                                    <span id="previewStock" class="badge bg-primary">0 còn lại</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// Global variables
let categories = [];
let availableAccounts = [];
let selectedAccountIds = [];
let selectedFiles = [];
let fileCounter = 0;
let availableVideoLinks = [];
let selectedVideoLinkIds = [];

// Initialize page
$(document).ready(function() {
    loadCategories();
    setupEventListeners();
    setupPreviewUpdates();
});

function loadCategories() {
    fetch('/api/admin/marketplace/categories')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                categories = data.categories;
                populateCategorySelect();
            } else {
                console.error('Error loading categories:', data.error);
            }
        })
        .catch(error => {
            console.error('Error loading categories:', error);
            // Fallback to public API
            fetch('/api/marketplace/categories')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        categories = data.categories;
                        populateCategorySelect();
                    }
                })
                .catch(err => console.error('Fallback error:', err));
        });
}

function populateCategorySelect() {
    const select = document.querySelector('select[name="category_id"]');
    select.innerHTML = '<option value="">Chọn danh mục</option>';

    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.category_id;
        option.textContent = category.name;
        select.appendChild(option);
    });
}

function loadProductTypesByCategory(categoryId) {
    const productTypeSelect = document.querySelector('select[name="product_type_id"]');

    if (!categoryId) {
        productTypeSelect.innerHTML = '<option value="">Chọn danh mục trước</option>';
        return;
    }

    // Show loading
    productTypeSelect.innerHTML = '<option value="">Đang tải...</option>';

    fetch(`/api/marketplace/admin/product-types-working/by-category/${categoryId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateProductTypeSelect(data.product_types || []);
            } else {
                console.error('Error loading product types:', data.error);
                productTypeSelect.innerHTML = '<option value="">Lỗi khi tải loại sản phẩm</option>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            productTypeSelect.innerHTML = '<option value="">Lỗi kết nối</option>';
        });
}

function populateProductTypeSelect(types) {
    const select = document.querySelector('select[name="product_type_id"]');
    select.innerHTML = '<option value="">Chọn loại sản phẩm</option>';

    types.forEach(type => {
        const option = document.createElement('option');
        option.value = type.type_id;
        option.textContent = type.name;
        option.setAttribute('data-metadata', JSON.stringify(type.metadata || {}));

        // Add description as title
        if (type.description) {
            option.title = type.description;
        }

        // Mark primary types
        if (type.is_primary) {
            option.textContent += ' (Chính)';
        }

        select.appendChild(option);
    });
}

function toggleProductTypeFields(typeId) {
    // Hide all type-specific fields
    document.getElementById('affPackageSettings').style.display = 'none';
    document.getElementById('accountPackageSettings').style.display = 'none';
    document.getElementById('videoLinksSection').style.display = 'none';
    document.getElementById('productFilesSection').style.display = 'none';

    // Show/hide sections based on product type
    const stockSection = document.getElementById('stockSection');
    const priceSection = document.getElementById('priceSection');
    const priceInput = document.querySelector('input[name="price"]');

    if (!typeId) {
        stockSection.style.display = 'block';
        priceSection.style.display = 'block';
        priceInput.required = true;
        return;
    }

    // Get selected option and its metadata
    const select = document.querySelector('select[name="product_type_id"]');
    const selectedOption = select.querySelector(`option[value="${typeId}"]`);

    if (selectedOption) {
        const metadata = JSON.parse(selectedOption.getAttribute('data-metadata') || '{}');

        // Show relevant fields based on metadata
        if (metadata.subscription_based || metadata.account_limit) {
            document.getElementById('affPackageSettings').style.display = 'block';
            // Hide stock and price sections for AFF Packages (subscription products)
            stockSection.style.display = 'none';
            priceSection.style.display = 'none';
            priceInput.required = false;
            priceInput.value = '0'; // Set default value for backend
        } else {
            // Show stock and price sections for non-subscription products
            stockSection.style.display = 'block';
            priceSection.style.display = 'block';
            priceInput.required = true;
        }

        if (metadata.requires_accounts) {
            document.getElementById('accountPackageSettings').style.display = 'block';
            loadAvailableAccounts();
        } else if (metadata.requires_video_links) {
            // For video products, show video links section
            document.getElementById('videoLinksSection').style.display = 'block';
            loadAvailableVideoLinks();
            loadVideoTypes();
        } else if (!metadata.subscription_based) {
            // For non-account, non-subscription products, show file upload section
            document.getElementById('productFilesSection').style.display = 'block';
        }
    } else {
        stockSection.style.display = 'block';
        priceSection.style.display = 'block';
        priceInput.required = true;
    }

    updatePreview();
}

function loadAvailableAccounts() {
    const container = document.getElementById('availableAccountsList');
    container.innerHTML = '<div class="text-center text-muted py-4"><i class="cil-reload fa-spin"></i> Đang tải accounts...</div>';

    // Build query parameters for server-side filtering
    const params = new URLSearchParams();

    const statusFilter = document.getElementById('accountStatusFilter').value;
    const teamFilter = document.getElementById('accountTeamFilter').value;
    const minFollowers = document.getElementById('minFollowersFilter').value;
    const maxFollowers = document.getElementById('maxFollowersFilter').value;
    const search = document.getElementById('accountSearchInput').value;

    if (statusFilter) params.append('status', statusFilter);
    if (teamFilter) params.append('team', teamFilter);
    if (minFollowers) params.append('min_followers', minFollowers);
    if (maxFollowers) params.append('max_followers', maxFollowers);
    if (search) params.append('search', search);

    fetch(`/api/admin/marketplace/available-accounts?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                availableAccounts = data.accounts;
                displayAvailableAccounts();
                populateTeamFilter();
            } else {
                container.innerHTML = '<div class="text-center text-danger py-4">Lỗi: ' + data.error + '</div>';
            }
        })
        .catch(error => {
            container.innerHTML = '<div class="text-center text-danger py-4">Lỗi kết nối: ' + error + '</div>';
        });
}

function populateTeamFilter() {
    const teamFilter = document.getElementById('accountTeamFilter');
    const teams = [...new Set(availableAccounts.map(acc => acc.team_name).filter(Boolean))];
    
    teamFilter.innerHTML = '<option value="">Tất cả team</option>';
    teams.forEach(team => {
        const option = document.createElement('option');
        option.value = team;
        option.textContent = team;
        teamFilter.appendChild(option);
    });
}

function displayAvailableAccounts() {
    const container = document.getElementById('availableAccountsList');

    if (availableAccounts.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-4">Không có account nào khả dụng với bộ lọc hiện tại</div>';
        // Reset select all checkbox when no accounts
        document.getElementById('selectAllAccounts').checked = false;
        return;
    }

    container.innerHTML = '';

    // Reset select all checkbox when accounts change
    document.getElementById('selectAllAccounts').checked = false;

    // Display accounts (filtering is now done server-side)
    availableAccounts.forEach(account => {
        const accountDiv = document.createElement('div');
        accountDiv.className = 'account-item';
        accountDiv.innerHTML = `
            <div class="form-check">
                <input class="form-check-input account-checkbox" type="checkbox"
                       value="${account.account_id}" id="account_${account.account_id}"
                       ${selectedAccountIds.includes(account.account_id) ? 'checked' : ''}
                       onchange="updateSelectedAccounts(); updateSelectAllState();">
                <label class="form-check-label w-100" for="account_${account.account_id}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${account.account_name}</strong>
                            <br><small class="text-muted">${account.team_name || 'No team'}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-${getStatusBadgeColor(account.status)}">${account.status}</span>
                            <br><small class="text-muted">${account.follower_count.toLocaleString()} followers</small>
                        </div>
                    </div>
                </label>
            </div>
        `;
        container.appendChild(accountDiv);
    });

    updateSelectedAccountsCount();
    updateSelectAllState();
}

function getStatusBadgeColor(status) {
    const colors = {
        'Live': 'success',
        'Có giỏ': 'primary',
        'Đang nuôi': 'warning',
        'Die': 'danger'
    };
    return colors[status] || 'secondary';
}

function updateSelectedAccounts() {
    const checkboxes = document.querySelectorAll('.account-checkbox:checked');
    selectedAccountIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
    updateSelectedAccountsCount();
    updatePreview();
}

function updateSelectedAccountsCount() {
    document.getElementById('selectedAccountsCount').textContent = selectedAccountIds.length;
    
    // Update stock field
    const stockInput = document.getElementById('stockInput');
    const productTypeSelect = document.querySelector('select[name="product_type_id"]');
    const selectedOption = productTypeSelect.querySelector(`option[value="${productTypeSelect.value}"]`);

    if (stockInput && selectedOption) {
        const metadata = JSON.parse(selectedOption.getAttribute('data-metadata') || '{}');
        if (metadata.requires_accounts) {
            stockInput.value = selectedAccountIds.length;
        }
    }
}

function previewThumbnail(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('thumbnailImg').src = e.target.result;
            document.getElementById('thumbnailPreview').style.display = 'block';
            document.getElementById('previewImage').src = e.target.result;
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function removeThumbnail() {
    document.getElementById('thumbnailInput').value = '';
    document.getElementById('thumbnailPreview').style.display = 'none';
    document.getElementById('previewImage').src = '/static/marketplace/thumbnails/default.png';
}

// Update select all checkbox state based on individual checkboxes
function updateSelectAllState() {
    const allCheckboxes = document.querySelectorAll('.account-checkbox');
    const checkedCheckboxes = document.querySelectorAll('.account-checkbox:checked');
    const selectAllCheckbox = document.getElementById('selectAllAccounts');

    if (allCheckboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCheckboxes.length === allCheckboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCheckboxes.length > 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }
}

// Debounce function to limit API calls
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function setupEventListeners() {
    // Account package filters - reload from server when filters change
    document.getElementById('accountSearchInput').addEventListener('input', debounce(loadAvailableAccounts, 500));
    document.getElementById('accountStatusFilter').addEventListener('change', loadAvailableAccounts);
    document.getElementById('accountTeamFilter').addEventListener('change', loadAvailableAccounts);
    document.getElementById('minFollowersFilter').addEventListener('input', debounce(loadAvailableAccounts, 500));
    document.getElementById('maxFollowersFilter').addEventListener('input', debounce(loadAvailableAccounts, 500));
    
    // Select all accounts checkbox
    document.getElementById('selectAllAccounts').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.account-checkbox');
        checkboxes.forEach(cb => {
            cb.checked = this.checked;
        });
        updateSelectedAccounts();
        // Remove indeterminate state when manually toggled
        this.indeterminate = false;
    });
}

function setupPreviewUpdates() {
    // Update preview when form fields change
    const fields = ['name', 'short_description', 'price', 'stock'];
    fields.forEach(field => {
        const element = document.querySelector(`[name="${field}"]`);
        if (element) {
            element.addEventListener('input', updatePreview);
        }
    });
    
    document.querySelector('select[name="product_type_id"]').addEventListener('change', updatePreview);
    document.querySelector('input[name="unlimited_stock"]').addEventListener('change', updatePreview);
}

function updatePreview() {
    const name = document.querySelector('[name="name"]').value || 'Tên sản phẩm';
    const description = document.querySelector('[name="short_description"]').value || 'Mô tả ngắn sẽ hiển thị ở đây';
    const price = document.querySelector('[name="price"]').value || '0';
    const stock = document.querySelector('[name="stock"]').value || '0';
    const productTypeSelect = document.querySelector('[name="product_type_id"]');
    const selectedOption = productTypeSelect.querySelector(`option[value="${productTypeSelect.value}"]`);
    const unlimitedStock = document.querySelector('[name="unlimited_stock"]').checked;
    
    document.getElementById('previewTitle').textContent = name;
    document.getElementById('previewDescription').textContent = description;
    document.getElementById('previewPrice').textContent = parseInt(price).toLocaleString() + ' MP';
    
    // Update type badge
    const typeName = selectedOption ? selectedOption.textContent : 'Chưa chọn loại';
    document.getElementById('previewBadge').textContent = typeName;
    
    // Update stock
    if (unlimitedStock) {
        document.getElementById('previewStock').textContent = 'Không giới hạn';
        document.getElementById('previewStock').className = 'badge bg-success';
    } else {
        document.getElementById('previewStock').textContent = stock + ' còn lại';
        document.getElementById('previewStock').className = `badge ${stock < 5 ? 'bg-warning' : 'bg-primary'}`;
    }
}

function submitProduct() {
    const form = document.getElementById('createProductForm');
    const formData = new FormData(form);
    const productTypeId = formData.get('product_type_id');

    // Get metadata for selected product type
    const productTypeSelect = document.querySelector('select[name="product_type_id"]');
    const selectedOption = productTypeSelect.querySelector(`option[value="${productTypeId}"]`);
    const metadata = selectedOption ? JSON.parse(selectedOption.getAttribute('data-metadata') || '{}') : {};

    // Add selected account IDs for account packages
    if (metadata.requires_accounts && selectedAccountIds.length > 0) {
        formData.append('selected_account_ids', JSON.stringify(selectedAccountIds));
    }

    // Add selected video link IDs for video products
    if (metadata.requires_video_links && selectedVideoLinkIds.length > 0) {
        formData.append('selected_video_link_ids', JSON.stringify(selectedVideoLinkIds));
    }

    // Add selected files for non-account products
    if (!metadata.requires_accounts && selectedFiles.length > 0) {
        selectedFiles.forEach((fileObj, index) => {
            formData.append(`product_files`, fileObj.file);
            formData.append(`file_descriptions`, fileObj.description);
            formData.append(`file_previews`, fileObj.isPreview);
            formData.append(`file_types`, fileObj.type);
            formData.append(`file_sort_orders`, fileObj.sortOrder);
        });
        formData.append('files_count', selectedFiles.length);
    }
    
    // Show loading
    const submitBtn = document.querySelector('button[onclick="submitProduct()"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="cil-reload fa-spin"></i> Đang tạo...';
    submitBtn.disabled = true;
    
    fetch('/api/admin/marketplace/products', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // If it's an account package, assign accounts to the package
            if (metadata.requires_accounts && selectedAccountIds.length > 0) {
                return assignAccountsToPackage(data.package_id, selectedAccountIds)
                    .then(() => {
                        showToast(`Tạo sản phẩm thành công với ${selectedAccountIds.length} accounts!`, 'success');
                        setTimeout(() => {
                            window.location.href = '/admin/marketplace/products';
                        }, 1500);
                    });
            } else {
                // For products with files
                if (selectedFiles.length > 0) {
                    showToast(`Tạo sản phẩm thành công với ${selectedFiles.length} files!`, 'success');
                } else {
                    showToast('Tạo sản phẩm thành công!', 'success');
                }
                setTimeout(() => {
                    window.location.href = '/admin/marketplace/products';
                }, 1500);
            }
        } else {
            throw new Error(data.error);
        }
    })
    .catch(error => {
        showToast('Lỗi: ' + error.message, 'danger');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function assignAccountsToPackage(packageId, accountIds) {
    return fetch('/api/admin/marketplace/assign-accounts-to-package', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            package_id: packageId,
            account_ids: accountIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            throw new Error(data.error);
        }
        return data;
    });
}

function showToast(message, type = 'info') {
    // Create toast notification
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div class="toast" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Thông báo</strong>
                <button type="button" class="btn-close" data-coreui-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const toast = new coreui.Toast(toastElement);
    toast.show();
    
    setTimeout(() => {
        if (toastElement.parentElement) {
            toastElement.remove();
        }
    }, 5000);
}

function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

// File Upload Functions
function handleFileSelection(input) {
    const files = Array.from(input.files);
    files.forEach(file => addFileToList(file));
    input.value = ''; // Reset input
}

function addFileToList(file) {
    // Validate file size (50MB max)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
        alert(`File "${file.name}" quá lớn. Tối đa 50MB.`);
        return;
    }

    // Create file object
    const fileObj = {
        id: ++fileCounter,
        file: file,
        name: file.name,
        size: file.size,
        type: getFileType(file),
        description: '',
        isPreview: false,
        sortOrder: selectedFiles.length
    };

    selectedFiles.push(fileObj);
    displaySelectedFiles();
}

function getFileType(file) {
    const extension = file.name.split('.').pop().toLowerCase();
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    const documentExts = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'xls', 'xlsx', 'ppt', 'pptx'];
    const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
    const audioExts = ['mp3', 'wav', 'ogg', 'aac', 'flac'];

    if (imageExts.includes(extension)) return 'image';
    if (documentExts.includes(extension)) return 'document';
    if (videoExts.includes(extension)) return 'video';
    if (audioExts.includes(extension)) return 'audio';
    return 'other';
}

function displaySelectedFiles() {
    const container = document.getElementById('selectedFilesList');

    if (selectedFiles.length === 0) {
        container.innerHTML = '<p class="text-muted text-center py-3">Chưa có file nào được chọn</p>';
        return;
    }

    container.innerHTML = selectedFiles.map(fileObj => `
        <div class="file-item" data-file-id="${fileObj.id}">
            ${fileObj.type === 'image' ?
                `<img src="${URL.createObjectURL(fileObj.file)}" class="file-preview" alt="Preview">` :
                `<div class="file-icon ${fileObj.type}">
                    <i class="${getFileIcon(fileObj.type)}"></i>
                </div>`
            }
            <div class="file-info">
                <div class="file-name">${fileObj.name}</div>
                <div class="file-meta">${formatFileSize(fileObj.size)} • ${fileObj.type}</div>
                <input type="text" class="form-control form-control-sm mt-1"
                       placeholder="Mô tả file (tùy chọn)"
                       value="${fileObj.description}"
                       onchange="updateFileDescription(${fileObj.id}, this.value)">
            </div>
            <div class="file-actions">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox"
                           ${fileObj.isPreview ? 'checked' : ''}
                           onchange="toggleFilePreview(${fileObj.id}, this.checked)">
                    <label class="form-check-label">Preview</label>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger"
                        onclick="removeFile(${fileObj.id})">
                    <i class="cil-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

function getFileIcon(type) {
    const icons = {
        'image': 'cil-image',
        'document': 'cil-description',
        'video': 'cil-video',
        'audio': 'cil-audio-spectrum',
        'other': 'cil-file'
    };
    return icons[type] || 'cil-file';
}

// AFF Package price calculation
function calculateYearlyPrice() {
    const monthlyPrice = parseInt(document.querySelector('input[name="monthly_price"]')?.value) || 0;
    const discountPercent = parseInt(document.querySelector('input[name="yearly_discount_percent"]')?.value) || 0;
    const accountLimit = parseInt(document.querySelector('input[name="account_limit"]')?.value) || 0;

    if (monthlyPrice <= 0) return;

    const yearlyOriginal = monthlyPrice * 12;
    const yearlyDiscounted = yearlyOriginal * (100 - discountPercent) / 100;
    const yearlySavings = yearlyOriginal - yearlyDiscounted;
    const effectiveMonthly = yearlyDiscounted / 12;

    // Update displays
    document.getElementById('monthlyPriceDisplay').textContent = formatPrice(monthlyPrice);
    document.getElementById('yearlyOriginalPrice').textContent = formatPrice(yearlyOriginal);
    document.getElementById('yearlyDiscountedPrice').textContent = formatPrice(yearlyDiscounted);
    document.getElementById('yearlySavings').textContent = formatPrice(yearlySavings);
    document.getElementById('effectiveMonthlyPrice').textContent = formatPrice(effectiveMonthly);
    document.getElementById('accountLimitDisplay').textContent = accountLimit;
}

function formatPrice(amount) {
    return new Intl.NumberFormat('vi-VN').format(Math.round(amount)) + ' MP';
}

// Initialize price calculation when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Set up event listeners for AFF Package fields
    const monthlyPriceInput = document.querySelector('input[name="monthly_price"]');
    const discountInput = document.querySelector('input[name="yearly_discount_percent"]');
    const accountLimitInput = document.querySelector('input[name="account_limit"]');

    if (monthlyPriceInput) {
        monthlyPriceInput.addEventListener('input', calculateYearlyPrice);
    }
    if (discountInput) {
        discountInput.addEventListener('input', calculateYearlyPrice);
    }
    if (accountLimitInput) {
        accountLimitInput.addEventListener('input', calculateYearlyPrice);
    }

    // Initial calculation
    calculateYearlyPrice();
});

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function updateFileDescription(fileId, description) {
    const fileObj = selectedFiles.find(f => f.id === fileId);
    if (fileObj) {
        fileObj.description = description;
    }
}

function toggleFilePreview(fileId, isPreview) {
    const fileObj = selectedFiles.find(f => f.id === fileId);
    if (fileObj) {
        fileObj.isPreview = isPreview;
    }
}

function removeFile(fileId) {
    selectedFiles = selectedFiles.filter(f => f.id !== fileId);
    displaySelectedFiles();
}

// Drag and Drop functionality
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.querySelector('.file-upload-area');

    if (uploadArea) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, unhighlight, false);
        });

        uploadArea.addEventListener('drop', handleDrop, false);
    }
});

function preventDefaults(e) {
    e.preventDefault();
    e.stopPropagation();
}

function highlight(e) {
    document.querySelector('.file-upload-area').classList.add('dragover');
}

function unhighlight(e) {
    document.querySelector('.file-upload-area').classList.remove('dragover');
}

function handleDrop(e) {
    const dt = e.dataTransfer;
    const files = dt.files;
    Array.from(files).forEach(file => addFileToList(file));
}

// ===== VIDEO LINKS FUNCTIONS =====

function loadAvailableVideoLinks() {
    const params = new URLSearchParams({
        status: 'available',
        video_type: document.getElementById('videoTypeFilter').value,
        video_count: document.getElementById('videoCountFilter').value,
        search: document.getElementById('videoSearchInput').value
    });

    fetch(`/api/admin/marketplace/video-links?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                availableVideoLinks = data.video_links;
                displayAvailableVideoLinks();
            } else {
                console.error('Error loading video links:', data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function displayAvailableVideoLinks() {
    const container = document.getElementById('availableVideoLinksContainer');

    if (availableVideoLinks.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="cil-video" style="font-size: 2rem; color: #ccc;"></i>
                <p class="text-muted mt-2">Không có video links nào phù hợp</p>
            </div>
        `;
        return;
    }

    container.innerHTML = '';

    availableVideoLinks.forEach(link => {
        const isSelected = selectedVideoLinkIds.includes(link.link_id);

        const linkCard = document.createElement('div');
        linkCard.className = `border rounded p-3 mb-2 ${isSelected ? 'border-primary bg-light' : ''}`;
        linkCard.style.cursor = 'pointer';

        linkCard.innerHTML = `
            <div class="form-check">
                <input class="form-check-input" type="checkbox"
                       id="videoLink_${link.link_id}"
                       ${isSelected ? 'checked' : ''}
                       onchange="toggleVideoLinkSelection(${link.link_id}, this.checked)">
                <label class="form-check-label w-100" for="videoLink_${link.link_id}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${link.name}</h6>
                            <p class="text-muted mb-1">${link.description || 'Không có mô tả'}</p>
                            <div>
                                <span class="badge bg-info me-2">${link.video_type || 'Chưa phân loại'}</span>
                                <span class="badge bg-primary">${link.video_count} videos</span>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="${link.drive_url}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="cil-external-link"></i>
                            </a>
                        </div>
                    </div>
                </label>
            </div>
        `;

        container.appendChild(linkCard);
    });

    updateVideoLinksStats();
}

function toggleVideoLinkSelection(linkId, isSelected) {
    if (isSelected) {
        if (!selectedVideoLinkIds.includes(linkId)) {
            selectedVideoLinkIds.push(linkId);
        }
    } else {
        selectedVideoLinkIds = selectedVideoLinkIds.filter(id => id !== linkId);
    }

    updateVideoLinksStats();
    displayAvailableVideoLinks(); // Refresh display
}

function selectAllVideoLinks() {
    selectedVideoLinkIds = availableVideoLinks.map(link => link.link_id);
    displayAvailableVideoLinks();
}

function clearVideoLinksSelection() {
    selectedVideoLinkIds = [];
    displayAvailableVideoLinks();
}

function updateVideoLinksStats() {
    document.getElementById('selectedVideoLinksCount').textContent = selectedVideoLinkIds.length;
    document.getElementById('calculatedStock').textContent = selectedVideoLinkIds.length;

    // Update stock input
    const stockInput = document.querySelector('input[name="stock"]');
    if (stockInput) {
        stockInput.value = selectedVideoLinkIds.length;
        stockInput.readOnly = true;
    }
}

function loadVideoTypes() {
    fetch('/api/admin/marketplace/video-types')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('videoTypeFilter');
                select.innerHTML = '<option value="">Tất cả loại</option>';

                data.video_types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading video types:', error));
}
</script>
{% endblock %}
