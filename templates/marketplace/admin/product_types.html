{% extends "base_coreui.html" %}

{% block title %}<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> phẩm{% endblock %}

{% block head_extra %}
<style>
    .type-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        background: white;
        transition: all 0.3s ease;
    }
    
    .type-card:hover {
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }
    
    .type-card.default {
        border-left: 4px solid #00C6AE;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }
    
    .type-icon {
        width: 60px;
        height: 60px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        background: linear-gradient(135deg, #00C6AE 0%, #00a693 100%);
    }
    
    .category-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-top: 10px;
    }
    
    .category-tag {
        background: #e3f2fd;
        color: #1976d2;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 12px;
        border: 1px solid #bbdefb;
    }
    
    .category-tag.primary {
        background: #00C6AE;
        color: white;
        border-color: #00a693;
    }
    
    .metadata-display {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 10px;
        margin-top: 10px;
        font-size: 12px;
        color: #6c757d;
    }
    
    .sort-handle {
        cursor: move;
        color: #6c757d;
        margin-right: 10px;
    }
    
    .sort-handle:hover {
        color: #00C6AE;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="cil-tags"></i> Quản lý Loại Sản phẩm</h2>
                    <p class="text-muted">Quản lý các loại sản phẩm và liên kết với danh mục</p>
                </div>
                <button class="btn btn-primary" onclick="showCreateTypeModal()">
                    <i class="cil-plus"></i> Tạo loại mới
                </button>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalTypesCount">0</h4>
                            <p class="mb-0">Tổng loại sản phẩm</p>
                        </div>
                        <div class="align-self-center">
                            <i class="cil-tags fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="activeTypesCount">0</h4>
                            <p class="mb-0">Đang hoạt động</p>
                        </div>
                        <div class="align-self-center">
                            <i class="cil-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="categoriesLinkedCount">0</h4>
                            <p class="mb-0">Liên kết danh mục</p>
                        </div>
                        <div class="align-self-center">
                            <i class="cil-link fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="productsUsingCount">0</h4>
                            <p class="mb-0">Sản phẩm sử dụng</p>
                        </div>
                        <div class="align-self-center">
                            <i class="cil-basket fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label>Trạng thái:</label>
                    <select class="form-select" id="statusFilter" onchange="loadProductTypes()">
                        <option value="">Tất cả</option>
                        <option value="active">Hoạt động</option>
                        <option value="inactive">Tạm dừng</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label>Loại:</label>
                    <select class="form-select" id="typeFilter" onchange="loadProductTypes()">
                        <option value="">Tất cả</option>
                        <option value="default">Mặc định</option>
                        <option value="custom">Tùy chỉnh</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label>Danh mục:</label>
                    <select class="form-select" id="categoryFilter" onchange="loadProductTypes()">
                        <option value="">Tất cả danh mục</option>
                        <!-- Will be populated by JS -->
                    </select>
                </div>
                <div class="col-md-3">
                    <label>Tìm kiếm:</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="Tên loại sản phẩm..." onkeyup="loadProductTypes()">
                </div>
            </div>
        </div>
    </div>

    <!-- Product Types List -->
    <div class="card">
        <div class="card-header">
            <h5><i class="cil-list"></i> Danh sách Loại Sản phẩm</h5>
        </div>
        <div class="card-body">
            <div id="productTypesContainer">
                <!-- Product types will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Product Type Modal -->
<div class="modal fade" id="productTypeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="typeModalTitle">Tạo loại sản phẩm mới</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="productTypeForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tên loại sản phẩm <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Icon</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" name="icon" value="cil-tag" onchange="updateIconPreview()">
                                    <span class="input-group-text">
                                        <i id="iconPreview" class="cil-tag"></i>
                                    </span>
                                </div>
                                <div class="form-text">Sử dụng CoreUI icons (vd: cil-tag, cil-star)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Mô tả</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_active" checked>
                                    <label class="form-check-label">Hoạt động</label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="is_default">
                                    <label class="form-check-label">Loại mặc định</label>
                                </div>
                                <div class="form-text">Chỉ nên có 1 loại mặc định</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Metadata Builder UI -->
                    <div class="mb-3">
                        <label class="form-label">Cấu hình sản phẩm</label>

                        <!-- Product Type Selection -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="cil-layers"></i> Loại sản phẩm</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="product_behavior" id="behavior_accounts" value="accounts" onchange="updateMetadataBuilder()">
                                            <label class="form-check-label" for="behavior_accounts">
                                                <i class="cil-user text-primary me-2"></i>
                                                <strong>Accounts</strong>
                                                <br><small class="text-muted">Gán tài khoản TikTok cho khách hàng</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="product_behavior" id="behavior_videos" value="videos" onchange="updateMetadataBuilder()">
                                            <label class="form-check-label" for="behavior_videos">
                                                <i class="cil-video text-success me-2"></i>
                                                <strong>Videos</strong>
                                                <br><small class="text-muted">Gán video links từ Google Drive</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="product_behavior" id="behavior_files" value="files" onchange="updateMetadataBuilder()">
                                            <label class="form-check-label" for="behavior_files">
                                                <i class="cil-file text-warning me-2"></i>
                                                <strong>Files</strong>
                                                <br><small class="text-muted">Upload files để khách hàng download</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Purchase Settings -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="cil-cart"></i> Cài đặt mua hàng</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="single_purchase_only" onchange="updateMetadataBuilder()">
                                            <label class="form-check-label" for="single_purchase_only">
                                                <strong>Chỉ mua 1 lần</strong>
                                                <br><small class="text-muted">Khách hàng không thể tăng số lượng trong giỏ hàng</small>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="warranty_enabled" onchange="updateMetadataBuilder()">
                                            <label class="form-check-label" for="warranty_enabled">
                                                <strong>Có bảo hành</strong>
                                                <br><small class="text-muted">Sản phẩm có chế độ bảo hành</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Advanced Settings (Optional) -->
                        <div class="card mb-3" id="advancedSettings" style="display: none;">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="cil-settings"></i> Cài đặt nâng cao
                                    <button type="button" class="btn btn-sm btn-outline-secondary float-end" onclick="toggleAdvancedSettings()">
                                        <i class="cil-chevron-bottom" id="advancedToggleIcon"></i>
                                    </button>
                                </h6>
                            </div>
                            <div class="card-body" id="advancedSettingsBody" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="subscription_based" onchange="updateMetadataBuilder()">
                                            <label class="form-check-label" for="subscription_based">
                                                Thanh toán theo tháng
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="downloadable" onchange="updateMetadataBuilder()">
                                            <label class="form-check-label" for="downloadable">
                                                Có thể download trực tiếp
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Custom properties (JSON)</label>
                                            <textarea class="form-control" id="custom_metadata" rows="3" placeholder='{"key": "value"}' onchange="updateMetadataBuilder()"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- JSON Preview -->
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="cil-code"></i> Metadata JSON (Auto-generated)</h6>
                            </div>
                            <div class="card-body">
                                <textarea class="form-control" name="metadata" id="metadataJson" rows="3" readonly style="font-family: monospace; font-size: 12px; background-color: #f8f9fa;"></textarea>
                                <div class="form-text">JSON được tạo tự động từ các cài đặt trên</div>
                            </div>
                        </div>

                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-info" onclick="toggleAdvancedSettings()">
                                <i class="cil-settings"></i> Cài đặt nâng cao
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Liên kết với danh mục</label>
                        <div id="categoryCheckboxes">
                            <!-- Will be populated by JS -->
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" id="typeSubmitBtn" onclick="saveProductType()">Tạo loại sản phẩm</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let productTypes = [];
let categories = [];
let editingTypeId = null;

// Load data on page load
document.addEventListener('DOMContentLoaded', function() {
    loadCategories();
    loadProductTypes();
});

function loadCategories() {
    fetch('/api/admin/marketplace/categories')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                categories = data.categories;
                populateCategoryFilter();
                populateCategoryCheckboxes();
            }
        })
        .catch(error => console.error('Error loading categories:', error));
}

function populateCategoryFilter() {
    const filter = document.getElementById('categoryFilter');
    filter.innerHTML = '<option value="">Tất cả danh mục</option>';
    
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.category_id;
        option.textContent = category.name;
        filter.appendChild(option);
    });
}

function populateCategoryCheckboxes() {
    const container = document.getElementById('categoryCheckboxes');
    container.innerHTML = '';
    
    categories.forEach(category => {
        const div = document.createElement('div');
        div.className = 'form-check';
        div.innerHTML = `
            <input class="form-check-input" type="checkbox" name="categories" value="${category.category_id}" id="cat_${category.category_id}">
            <label class="form-check-label" for="cat_${category.category_id}">
                <i class="${category.icon}"></i> ${category.name}
            </label>
            <div class="form-check ms-4">
                <input class="form-check-input" type="checkbox" name="primary_categories" value="${category.category_id}" id="primary_cat_${category.category_id}">
                <label class="form-check-label" for="primary_cat_${category.category_id}">
                    <small class="text-muted">Danh mục chính</small>
                </label>
            </div>
        `;
        container.appendChild(div);
    });
}

function loadProductTypes() {
    const params = new URLSearchParams({
        status: document.getElementById('statusFilter').value,
        type: document.getElementById('typeFilter').value,
        category: document.getElementById('categoryFilter').value,
        search: document.getElementById('searchInput').value
    });

    fetch(`/api/marketplace/admin/product-types-working?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                productTypes = data.product_types;
                displayProductTypes(data.product_types);
                updateStats(data.stats);
            } else {
                showToast('Lỗi: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading product types:', error);
            showToast('Lỗi kết nối', 'danger');
        });
}

function displayProductTypes(types) {
    const container = document.getElementById('productTypesContainer');
    container.innerHTML = '';

    if (types.length === 0) {
        container.innerHTML = `
            <div class="text-center py-5">
                <i class="cil-tags fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Chưa có loại sản phẩm nào</h5>
                <button class="btn btn-primary" onclick="showCreateTypeModal()">
                    <i class="cil-plus"></i> Tạo loại đầu tiên
                </button>
            </div>
        `;
        return;
    }

    types.forEach(type => {
        const typeCard = document.createElement('div');
        typeCard.className = `type-card ${type.is_default ? 'default' : ''}`;

        const categoryTags = (type.categories || []).map(cat =>
            `<span class="category-tag ${cat.is_primary ? 'primary' : ''}">${cat.name}</span>`
        ).join('');

        const metadata = type.metadata ?
            `<div class="metadata-display">
                <strong>Metadata:</strong> ${JSON.stringify(type.metadata, null, 2)}
            </div>` : '';

        typeCard.innerHTML = `
            <div class="row align-items-center">
                <div class="col-md-1">
                    <i class="sort-handle cil-menu fa-lg"></i>
                </div>
                <div class="col-md-2">
                    <div class="type-icon">
                        <i class="${type.icon}"></i>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5 class="mb-1">
                        ${type.name}
                        ${type.is_default ? '<span class="badge bg-primary ms-2">Mặc định</span>' : ''}
                    </h5>
                    <p class="text-muted mb-1">${type.description || 'Không có mô tả'}</p>
                    <div class="d-flex gap-2 mb-2">
                        <span class="badge ${type.is_active ? 'bg-success' : 'bg-secondary'}">
                            ${type.is_active ? 'Hoạt động' : 'Tạm dừng'}
                        </span>
                        <span class="badge bg-info">${type.product_count} sản phẩm</span>
                        <span class="badge bg-warning">${type.category_count || 0} danh mục</span>
                    </div>
                    <div class="category-tags">${categoryTags}</div>
                    ${metadata}
                </div>
                <div class="col-md-3 text-end">
                    <button class="btn btn-sm btn-outline-primary" onclick="editProductType(${type.type_id})">
                        <i class="cil-pencil"></i> Sửa
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="manageCategoryLinks(${type.type_id})">
                        <i class="cil-link"></i> Liên kết
                    </button>
                    ${!type.is_default ? `
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteProductType(${type.type_id})">
                            <i class="cil-trash"></i> Xóa
                        </button>
                    ` : ''}
                </div>
            </div>
        `;

        container.appendChild(typeCard);
    });
}

function updateStats(stats) {
    document.getElementById('totalTypesCount').textContent = stats.total || 0;
    document.getElementById('activeTypesCount').textContent = stats.active || 0;
    document.getElementById('categoriesLinkedCount').textContent = stats.categories_linked || 0;
    document.getElementById('productsUsingCount').textContent = stats.products_using || 0;
}

function showCreateTypeModal() {
    editingTypeId = null;
    document.getElementById('typeModalTitle').textContent = 'Tạo loại sản phẩm mới';
    document.getElementById('typeSubmitBtn').textContent = 'Tạo loại sản phẩm';
    document.getElementById('productTypeForm').reset();
    document.getElementById('iconPreview').className = 'cil-tag';

    // Uncheck all categories
    document.querySelectorAll('input[name="categories"]').forEach(cb => cb.checked = false);
    document.querySelectorAll('input[name="primary_categories"]').forEach(cb => cb.checked = false);

    new coreui.Modal(document.getElementById('productTypeModal')).show();
}

function editProductType(typeId) {
    const type = productTypes.find(t => t.type_id === typeId);
    if (!type) return;

    editingTypeId = typeId;
    document.getElementById('typeModalTitle').textContent = 'Sửa loại sản phẩm';
    document.getElementById('typeSubmitBtn').textContent = 'Cập nhật';

    // Fill form
    const form = document.getElementById('productTypeForm');
    form.name.value = type.name;
    form.description.value = type.description || '';
    form.icon.value = type.icon;
    form.is_active.checked = type.is_active;
    form.is_default.checked = type.is_default;

    // Load metadata into Metadata Builder
    const metadataJson = type.metadata ? JSON.stringify(type.metadata, null, 2) : '{}';
    loadExistingMetadata(metadataJson);

    // Update icon preview
    document.getElementById('iconPreview').className = type.icon;

    // Check linked categories
    console.log('Type categories:', type.categories); // Debug

    document.querySelectorAll('input[name="categories"]').forEach(cb => {
        const categoryId = parseInt(cb.value);
        cb.checked = (type.categories || []).some(cat => cat.category_id === categoryId);
    });

    document.querySelectorAll('input[name="primary_categories"]').forEach(cb => {
        const categoryId = parseInt(cb.value);
        cb.checked = (type.categories || []).some(cat => cat.category_id === categoryId && cat.is_primary);
    });

    new coreui.Modal(document.getElementById('productTypeModal')).show();
}

function updateIconPreview() {
    const iconInput = document.querySelector('input[name="icon"]');
    const preview = document.getElementById('iconPreview');
    preview.className = iconInput.value || 'cil-tag';
}

function saveProductType() {
    const formData = new FormData(document.getElementById('productTypeForm'));

    // Get selected categories
    const selectedCategories = [];
    const primaryCategories = [];

    document.querySelectorAll('input[name="categories"]:checked').forEach(cb => {
        selectedCategories.push(parseInt(cb.value));
    });

    document.querySelectorAll('input[name="primary_categories"]:checked').forEach(cb => {
        primaryCategories.push(parseInt(cb.value));
    });

    formData.append('selected_categories', JSON.stringify(selectedCategories));
    formData.append('primary_categories', JSON.stringify(primaryCategories));

    const url = editingTypeId ?
        `/api/marketplace/admin/product-types-working/${editingTypeId}` :
        '/api/marketplace/admin/product-types-working';

    const method = editingTypeId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            coreui.Modal.getInstance(document.getElementById('productTypeModal')).hide();
            loadProductTypes();
        } else {
            showToast('Lỗi: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showToast('Lỗi kết nối: ' + error, 'danger');
    });
}

function deleteProductType(typeId) {
    const type = productTypes.find(t => t.type_id === typeId);
    if (!type) return;

    if (type.product_count > 0) {
        showToast('Không thể xóa loại sản phẩm đang được sử dụng', 'warning');
        return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa loại sản phẩm "${type.name}"?`)) {
        fetch(`/api/marketplace/admin/product-types-working/${typeId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                loadProductTypes();
            } else {
                showToast('Lỗi: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            showToast('Lỗi kết nối: ' + error, 'danger');
        });
    }
}

function showToast(message, type) {
    // Simple toast implementation
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);

    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}

// Metadata Builder Functions
function updateMetadataBuilder() {
    const metadata = {};

    // Get selected product behavior
    const behaviorRadios = document.querySelectorAll('input[name="product_behavior"]');
    let selectedBehavior = null;
    behaviorRadios.forEach(radio => {
        if (radio.checked) {
            selectedBehavior = radio.value;
        }
    });

    // Set behavior-specific metadata
    if (selectedBehavior === 'accounts') {
        metadata.requires_accounts = true;
        metadata.product_type = 'account';
    } else if (selectedBehavior === 'videos') {
        metadata.requires_video_links = true;
        metadata.product_type = 'videos';
    } else if (selectedBehavior === 'files') {
        metadata.requires_files = true;
        metadata.downloadable = true;
        metadata.product_type = 'win_product';
    }

    // Purchase settings
    if (document.getElementById('single_purchase_only') && document.getElementById('single_purchase_only').checked) {
        metadata.single_purchase_only = true;
        metadata.max_quantity = 1;
    }

    if (document.getElementById('warranty_enabled') && document.getElementById('warranty_enabled').checked) {
        metadata.warranty_enabled = true;
    }

    // Advanced settings
    if (document.getElementById('subscription_based') && document.getElementById('subscription_based').checked) {
        metadata.subscription_based = true;
    }

    if (document.getElementById('downloadable') && document.getElementById('downloadable').checked) {
        metadata.downloadable = true;
    }

    // Custom metadata
    const customMetadataEl = document.getElementById('custom_metadata');
    if (customMetadataEl) {
        const customMetadata = customMetadataEl.value.trim();
        if (customMetadata) {
            try {
                const customObj = JSON.parse(customMetadata);
                Object.assign(metadata, customObj);
            } catch (e) {
                console.warn('Invalid custom metadata JSON:', e);
            }
        }
    }

    // Update JSON preview
    const metadataJsonEl = document.getElementById('metadataJson');
    if (metadataJsonEl) {
        metadataJsonEl.value = JSON.stringify(metadata, null, 2);
    }
}

function toggleAdvancedSettings() {
    const advancedBody = document.getElementById('advancedSettingsBody');
    const toggleIcon = document.getElementById('advancedToggleIcon');

    if (advancedBody && toggleIcon) {
        if (advancedBody.style.display === 'none') {
            advancedBody.style.display = 'block';
            toggleIcon.className = 'cil-chevron-top';
        } else {
            advancedBody.style.display = 'none';
            toggleIcon.className = 'cil-chevron-bottom';
        }
    }
}

function loadExistingMetadata(metadataJson) {
    if (!metadataJson) return;

    try {
        const metadata = JSON.parse(metadataJson);

        // Load product behavior
        if (metadata.requires_accounts || metadata.product_type === 'account') {
            const accountsRadio = document.getElementById('behavior_accounts');
            if (accountsRadio) accountsRadio.checked = true;
        } else if (metadata.requires_video_links || metadata.product_type === 'videos') {
            const videosRadio = document.getElementById('behavior_videos');
            if (videosRadio) videosRadio.checked = true;
        } else if (metadata.requires_files || metadata.product_type === 'win_product') {
            const filesRadio = document.getElementById('behavior_files');
            if (filesRadio) filesRadio.checked = true;
        }

        // Load purchase settings
        if (metadata.single_purchase_only || metadata.max_quantity === 1) {
            const singlePurchaseEl = document.getElementById('single_purchase_only');
            if (singlePurchaseEl) singlePurchaseEl.checked = true;
        }

        if (metadata.warranty_enabled) {
            const warrantyEl = document.getElementById('warranty_enabled');
            if (warrantyEl) warrantyEl.checked = true;
        }

        // Load advanced settings
        if (metadata.subscription_based) {
            const subscriptionEl = document.getElementById('subscription_based');
            if (subscriptionEl) subscriptionEl.checked = true;
        }

        if (metadata.downloadable) {
            const downloadableEl = document.getElementById('downloadable');
            if (downloadableEl) downloadableEl.checked = true;
        }

        // Load custom metadata (exclude known keys)
        const knownKeys = [
            'requires_accounts', 'requires_video_links', 'requires_files',
            'product_type', 'single_purchase_only', 'max_quantity',
            'warranty_enabled', 'subscription_based', 'downloadable'
        ];

        const customMetadata = {};
        Object.keys(metadata).forEach(key => {
            if (!knownKeys.includes(key)) {
                customMetadata[key] = metadata[key];
            }
        });

        if (Object.keys(customMetadata).length > 0) {
            const customMetadataEl = document.getElementById('custom_metadata');
            if (customMetadataEl) {
                customMetadataEl.value = JSON.stringify(customMetadata, null, 2);
            }
        }

        updateMetadataBuilder();
    } catch (e) {
        console.error('Error loading metadata:', e);
        // Fallback: show raw JSON in custom metadata
        const customMetadataEl = document.getElementById('custom_metadata');
        if (customMetadataEl) {
            customMetadataEl.value = metadataJson;
        }
        updateMetadataBuilder();
    }
}

// Initialize metadata builder when page loads
document.addEventListener('DOMContentLoaded', function() {
    updateMetadataBuilder();
});
</script>
{% endblock %}
