{% extends "base_coreui.html" %}
{% block title %}Chỉnh sửa sản phẩm - Marketplace{% endblock %}

{% block head_extra %}
<style>
    .upload-area {
        border: 2px dashed #ccc;
        border-radius: 8px;
        padding: 30px;
        text-align: center;
        cursor: pointer;
        transition: border-color 0.3s;
        min-height: 150px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .upload-area:hover {
        border-color: #00C6AE;
    }
    .upload-area.dragover {
        border-color: #00C6AE;
        background-color: #f0f9ff;
    }
    .account-item {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 8px;
        transition: all 0.3s ease;
    }
    .account-item:hover {
        border-color: #00C6AE;
        background-color: #f8f9fa;
    }
    .account-item.selected {
        border-color: #00C6AE;
        background-color: #e8f5f3;
    }
    .account-item.sold {
        background-color: #f8d7da;
        border-color: #f5c6cb;
    }
    .accounts-grid {
        max-height: 500px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
    }
    .form-section {
        background: white;
        border-radius: 12px;
        padding: 25px;
        margin-bottom: 25px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    .section-title {
        color: #00C6AE;
        font-weight: 600;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #e9ecef;
    }
    .stats-badge {
        background: linear-gradient(135deg, #00C6AE, #00a693);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
    }
    .file-upload-area {
        border: 2px dashed #ccc;
        border-radius: 8px;
        padding: 40px 20px;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #fafafa;
    }
    .file-upload-area:hover {
        border-color: #00C6AE;
        background: #f0f9ff;
    }
    .file-upload-area.dragover {
        border-color: #00C6AE;
        background: #e8f5f3;
        transform: scale(1.02);
    }
    .selected-files-container, .existing-files-container {
        max-height: 400px;
        overflow-y: auto;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        background: white;
    }
    .file-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 10px;
        background: white;
        transition: all 0.3s ease;
    }
    .file-item:hover {
        border-color: #00C6AE;
        box-shadow: 0 2px 8px rgba(0,198,174,0.1);
    }
    .file-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        font-size: 18px;
        color: white;
    }
    .file-icon.image { background: #28a745; }
    .file-icon.document { background: #dc3545; }
    .file-icon.video { background: #6f42c1; }
    .file-icon.audio { background: #fd7e14; }
    .file-icon.other { background: #6c757d; }
    .file-info {
        flex: 1;
    }
    .file-name {
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;
    }
    .file-meta {
        font-size: 12px;
        color: #6c757d;
    }
    .file-actions {
        display: flex;
        gap: 8px;
    }
    .file-preview {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 6px;
        margin-right: 12px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/admin/marketplace/products">Quản lý sản phẩm</a></li>
                            <li class="breadcrumb-item active">Chỉnh sửa sản phẩm</li>
                        </ol>
                    </nav>
                    <h2><i class="cil-pencil text-primary"></i> Chỉnh sửa sản phẩm</h2>
                </div>
                <div>
                    <a href="/admin/marketplace/products" class="btn btn-outline-secondary me-2">
                        <i class="cil-arrow-left"></i> Quay lại
                    </a>
                    <button type="button" class="btn btn-primary" onclick="updateProduct()">
                        <i class="cil-check"></i> Cập nhật sản phẩm
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loadingState" class="text-center py-5">
        <i class="cil-reload fa-spin fa-2x text-muted"></i>
        <p class="text-muted mt-2">Đang tải thông tin sản phẩm...</p>
    </div>

    <!-- Edit Form -->
    <div id="editForm" style="display: none;">
        <form id="updateProductForm" enctype="multipart/form-data">
            <input type="hidden" id="productId" name="product_id">
            
            <div class="row">
                <!-- Left Column - Basic Info -->
                <div class="col-lg-8">
                    <!-- Basic Information -->
                    <div class="form-section">
                        <h4 class="section-title"><i class="cil-info"></i> Thông tin cơ bản</h4>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Tên sản phẩm *</label>
                                    <input type="text" class="form-control" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Danh mục *</label>
                                    <select class="form-select" name="category_id" required onchange="loadProductTypesByCategory(this.value)">
                                        <option value="">Chọn danh mục</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Loại sản phẩm *</label>
                                    <select class="form-select" name="product_type_id" required onchange="toggleProductTypeFields(this.value)">
                                        <option value="">Chọn danh mục trước</option>
                                    </select>
                                    <div class="form-text">Chọn danh mục để xem các loại sản phẩm có thể</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Giá bán (MP) *</label>
                                    <input type="number" class="form-control" name="price" min="0" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Trạng thái *</label>
                                    <select class="form-select" name="status" required>
                                        <option value="active">🟢 Hoạt động</option>
                                        <option value="inactive">🔴 Tạm dừng</option>
                                    </select>
                                    <div class="form-text">Sản phẩm tạm dừng sẽ không hiển thị cho khách hàng</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Mô tả ngắn</label>
                            <input type="text" class="form-control" name="short_description" maxlength="500">
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Mô tả chi tiết</label>
                            <textarea class="form-control" name="description" rows="5"></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Số lượng kho</label>
                                    <input type="number" class="form-control" name="stock" min="0" id="stockInput">
                                    <small class="text-muted">Sẽ tự động cập nhật cho Account Package</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3 form-check mt-4">
                                    <input type="checkbox" class="form-check-input" name="unlimited_stock" id="unlimitedStock">
                                    <label class="form-check-label" for="unlimitedStock">
                                        Không giới hạn kho
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" name="is_featured" id="isFeatured">
                            <label class="form-check-label" for="isFeatured">
                                Sản phẩm nổi bật
                            </label>
                        </div>
                    </div>

                    <!-- Video Links Section (for videos products) -->
                    <div id="videoLinksSection" class="form-section" style="display: none;">
                        <h4 class="section-title"><i class="cil-video"></i> Video Links</h4>
                        <p class="text-muted">Chọn các video links để bán cho sản phẩm này</p>

                        <!-- Filters -->
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label class="form-label">Lọc theo loại video</label>
                                <select class="form-select" id="videoTypeFilter" onchange="loadAvailableVideoLinks()">
                                    <option value="">Tất cả loại</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Lọc theo số lượng</label>
                                <select class="form-select" id="videoCountFilter" onchange="loadAvailableVideoLinks()">
                                    <option value="">Tất cả</option>
                                    <option value="1-50">1-50 videos</option>
                                    <option value="51-100">51-100 videos</option>
                                    <option value="101+">101+ videos</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">Tìm kiếm</label>
                                <input type="text" class="form-control" id="videoSearchInput" placeholder="Tên link..." onkeyup="loadAvailableVideoLinks()">
                            </div>
                        </div>

                        <!-- Available Video Links -->
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Video Links có sẵn</h6>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllVideoLinks()">
                                            <i class="cil-check"></i> Chọn tất cả
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearVideoLinksSelection()">
                                            <i class="cil-x"></i> Bỏ chọn
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="availableVideoLinksContainer">
                                    <div class="text-center py-3">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2">Đang tải video links...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Selected Video Links Summary -->
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <h6><i class="cil-info"></i> Thông tin tồn kho</h6>
                                <p class="mb-0">
                                    Đã chọn: <strong id="selectedVideoLinksCount">0</strong> video links<br>
                                    Tồn kho sản phẩm sẽ là: <strong id="calculatedStock">0</strong>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Product Files Section -->
                    <div id="productFilesSection" class="form-section" style="display: none;">
                        <h4 class="section-title"><i class="cil-folder-open"></i> Files đính kèm</h4>
                        <p class="text-muted mb-3">Quản lý files cho sản phẩm (hình ảnh, tài liệu, video). Khách hàng sẽ có thể tải về sau khi mua.</p>

                        <!-- Existing Files -->
                        <div class="mb-4">
                            <h6>Files hiện tại:</h6>
                            <div id="existingFilesList" class="existing-files-container">
                                <!-- Existing files will be loaded here -->
                            </div>
                        </div>

                        <!-- Add New Files -->
                        <div class="mb-3">
                            <label class="form-label">Thêm files mới</label>
                            <div class="file-upload-area" onclick="document.getElementById('productFilesInput').click()">
                                <i class="cil-cloud-upload fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">Click để chọn files hoặc kéo thả vào đây</h6>
                                <p class="text-muted mb-0">Hỗ trợ: Hình ảnh, PDF, DOC, Video, Audio</p>
                                <small class="text-muted">Tối đa 50MB mỗi file</small>
                            </div>
                            <input type="file" id="productFilesInput" multiple accept="image/*,.pdf,.doc,.docx,.txt,.mp4,.avi,.mov,.mp3,.wav" style="display: none;" onchange="handleFileSelection(this)">
                        </div>

                        <!-- New Selected Files List -->
                        <div id="selectedFilesList" class="selected-files-container">
                            <!-- New files will be displayed here -->
                        </div>
                    </div>

                    <!-- Account Package Settings -->
                    <div id="accountPackageSettings" class="form-section" style="display: none;">
                        <h4 class="section-title"><i class="cil-user"></i> Cấu hình Account Package</h4>
                        
                        <div class="row">
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Tiêu chí account</label>
                                    <textarea class="form-control" name="account_criteria" rows="3"></textarea>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Thời gian bảo hành (ngày)</label>
                                    <input type="number" class="form-control" name="warranty_days" min="0" value="3">
                                </div>
                            </div>
                        </div>

                        <!-- Account Management -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <label class="form-label mb-0">Quản lý accounts trong package</label>
                                <div>
                                    <span class="stats-badge me-2">Đã gán: <span id="assignedAccountsCount">0</span> accounts</span>
                                    <span class="stats-badge me-2">Đã bán: <span id="soldAccountsCount">0</span> accounts</span>
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadPackageAccounts()">
                                        <i class="cil-reload"></i> Tải lại
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Current Package Accounts -->
                            <div class="mb-3">
                                <h6>Accounts hiện tại trong package:</h6>
                                <div id="currentPackageAccounts" class="accounts-grid">
                                    <div class="text-center text-muted py-3">
                                        <i class="cil-info"></i> Đang tải accounts...
                                    </div>
                                </div>
                            </div>

                            <!-- Add More Accounts -->
                            <div class="mb-3">
                                <h6>Thêm accounts mới:</h6>
                                
                                <!-- Filter accounts -->
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <input type="text" class="form-control" id="accountSearchInput" placeholder="Tìm theo tên account...">
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select" id="accountStatusFilter">
                                            <option value="">Tất cả trạng thái</option>
                                            <option value="Live">Live</option>
                                            <option value="Có giỏ">Có giỏ</option>
                                            <option value="Đang nuôi">Đang nuôi</option>
                                            <option value="Đủ điều kiện">Đủ điều kiện</option>
                                            <option value="Bật hụt">Bật hụt</option>
                                            <option value="Thu giỏ">Thu giỏ</option>
                                            <option value="Die">Die</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select" id="accountTeamFilter">
                                            <option value="">Tất cả team</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="row">
                                            <div class="col-6">
                                                <input type="number" class="form-control" id="minFollowersFilter" placeholder="Min followers" min="0">
                                            </div>
                                            <div class="col-6">
                                                <input type="number" class="form-control" id="maxFollowersFilter" placeholder="Max followers" min="0">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-outline-primary w-100" onclick="loadAvailableAccounts()">
                                            <i class="cil-reload"></i> Tải
                                        </button>
                                    </div>
                                </div>

                                <!-- Select all checkbox -->
                                <div class="row mb-2">
                                    <div class="col-md-12">
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="selectAllAccounts">
                                            <label class="form-check-label" for="selectAllAccounts">
                                                <strong>Chọn tất cả accounts hiển thị</strong>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Available accounts list -->
                                <div id="availableAccountsList" class="accounts-grid">
                                    <div class="text-center text-muted py-3">
                                        <i class="cil-info"></i> Click "Tải" để xem accounts có thể thêm
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Thumbnail & Stats -->
                <div class="col-lg-4">
                    <!-- Product Stats -->
                    <div class="form-section">
                        <h4 class="section-title"><i class="cil-chart-line"></i> Thống kê sản phẩm</h4>
                        
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <h4 id="soldCount">0</h4>
                                        <small>Đã bán</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <h4 id="currentStock">0</h4>
                                        <small>Còn lại</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Thumbnail -->
                    <div class="form-section">
                        <h4 class="section-title"><i class="cil-image"></i> Hình ảnh sản phẩm</h4>
                        
                        <div class="upload-area" onclick="document.getElementById('thumbnailInput').click()">
                            <i class="cil-cloud-upload fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Click để thay đổi hình ảnh</h6>
                            <small class="text-muted">JPG, PNG, GIF (tối đa 5MB)</small>
                        </div>
                        
                        <input type="file" id="thumbnailInput" name="thumbnail" accept="image/*" style="display: none;" onchange="previewThumbnail(this)">
                        <input type="hidden" name="current_thumbnail" id="currentThumbnail">
                        
                        <div id="thumbnailPreview" class="mt-3">
                            <img id="thumbnailImg" class="img-fluid rounded" style="width: 100%;">
                            <button type="button" class="btn btn-sm btn-outline-danger mt-2 w-100" onclick="removeThumbnail()">
                                <i class="cil-trash"></i> Xóa hình
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
// Global variables
let productId = {{ product_id }};
let categories = [];
let availableAccounts = [];
let packageAccounts = [];
let selectedAccountIds = [];
let selectedFiles = [];
let existingFiles = [];
let fileCounter = 0;
let availableVideoLinks = [];
let selectedVideoLinkIds = [];

// Initialize page
$(document).ready(function() {
    console.log('Initializing edit product page for product ID:', productId);
    loadCategories();
    loadProductData();
    setupEventListeners();
});

function loadCategories() {
    fetch('/api/admin/marketplace/categories')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                categories = data.categories;
                populateCategorySelect();
            } else {
                console.error('Error loading categories:', data.error);
            }
        })
        .catch(error => {
            console.error('Error loading categories:', error);
            // Fallback to public API
            fetch('/api/marketplace/categories')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        categories = data.categories;
                        populateCategorySelect();
                    }
                })
                .catch(err => console.error('Fallback error:', err));
        });
}

function populateCategorySelect() {
    const select = document.querySelector('select[name="category_id"]');
    select.innerHTML = '<option value="">Chọn danh mục</option>';

    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category.category_id;
        option.textContent = category.name;
        select.appendChild(option);
    });
}

function loadProductTypesByCategory(categoryId) {
    const productTypeSelect = document.querySelector('select[name="product_type_id"]');

    if (!categoryId) {
        productTypeSelect.innerHTML = '<option value="">Chọn danh mục trước</option>';
        return;
    }

    // Show loading
    productTypeSelect.innerHTML = '<option value="">Đang tải...</option>';

    fetch(`/api/marketplace/admin/product-types-working/by-category/${categoryId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateProductTypeSelect(data.product_types || []);
            } else {
                console.error('Error loading product types:', data.error);
                productTypeSelect.innerHTML = '<option value="">Lỗi khi tải loại sản phẩm</option>';
            }
        })
        .catch(error => {
            console.error('Error:', error);
            productTypeSelect.innerHTML = '<option value="">Lỗi kết nối</option>';
        });
}

function populateProductTypeSelect(types) {
    const select = document.querySelector('select[name="product_type_id"]');
    select.innerHTML = '<option value="">Chọn loại sản phẩm</option>';

    types.forEach(type => {
        const option = document.createElement('option');
        option.value = type.type_id;
        option.textContent = type.name;
        option.setAttribute('data-metadata', JSON.stringify(type.metadata || {}));

        // Add description as title
        if (type.description) {
            option.title = type.description;
        }

        // Mark primary types
        if (type.is_primary) {
            option.textContent += ' (Chính)';
        }

        select.appendChild(option);
    });
}

function loadProductData() {
    fetch(`/api/admin/marketplace/products/${productId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateForm(data.product);
                document.getElementById('loadingState').style.display = 'none';
                document.getElementById('editForm').style.display = 'block';
            } else {
                alert('Lỗi: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error loading product:', error);
            alert('Lỗi kết nối');
        });
}

function populateForm(product) {
    // Basic info
    document.getElementById('productId').value = product.product_id;
    document.querySelector('[name="name"]').value = product.name;
    document.querySelector('[name="category_id"]').value = product.category_id;

    // Load product types for selected category first
    if (product.category_id) {
        loadProductTypesByCategory(product.category_id);
        // Set product_type_id after types are loaded
        setTimeout(() => {
            if (product.product_type_id) {
                document.querySelector('[name="product_type_id"]').value = product.product_type_id;
            }
        }, 500);
    }
    document.querySelector('[name="price"]').value = product.price;
    document.querySelector('[name="status"]').value = product.status || 'active';
    document.querySelector('[name="short_description"]').value = product.short_description || '';
    document.querySelector('[name="description"]').value = product.description || '';
    document.querySelector('[name="stock"]').value = product.stock;
    document.querySelector('[name="unlimited_stock"]').checked = product.unlimited_stock;
    document.querySelector('[name="is_featured"]').checked = product.is_featured;
    
    // Stats
    document.getElementById('soldCount').textContent = product.sold_count || 0;
    document.getElementById('currentStock').textContent = product.unlimited_stock ? '∞' : product.stock;
    
    // Thumbnail
    if (product.image_url) {
        document.getElementById('currentThumbnail').value = product.image_url;
        document.getElementById('thumbnailImg').src = product.image_url;
        document.getElementById('thumbnailPreview').style.display = 'block';
    }
    
    // Product type specific - will be handled after product types are loaded
    setTimeout(() => {
        const productTypeId = document.querySelector('[name="product_type_id"]').value;
        if (productTypeId) {
            toggleProductTypeFields(productTypeId);

            // Load type-specific data based on metadata
            const selectedOption = document.querySelector(`select[name="product_type_id"] option[value="${productTypeId}"]`);
            if (selectedOption) {
                const metadata = JSON.parse(selectedOption.getAttribute('data-metadata') || '{}');

                if (metadata.requires_accounts) {
                    document.querySelector('[name="account_criteria"]').value = product.metadata?.criteria || '';
                    document.querySelector('[name="warranty_days"]').value = product.metadata?.warranty_days || 3;
                    loadPackageAccounts();
                } else if (metadata.requires_video_links) {
                    // Load existing video links for this product
                    loadExistingVideoLinks();
                }
            }
        }
    }, 600);
}

function toggleProductTypeFields(typeId) {
    // Hide all type-specific fields
    document.getElementById('accountPackageSettings').style.display = 'none';
    document.getElementById('videoLinksSection').style.display = 'none';
    document.getElementById('productFilesSection').style.display = 'none';

    if (!typeId) return;

    // Get selected option and its metadata
    const select = document.querySelector('select[name="product_type_id"]');
    const selectedOption = select.querySelector(`option[value="${typeId}"]`);

    if (selectedOption) {
        const metadata = JSON.parse(selectedOption.getAttribute('data-metadata') || '{}');

        // Show relevant fields based on metadata
        if (metadata.requires_accounts) {
            document.getElementById('accountPackageSettings').style.display = 'block';
        } else if (metadata.requires_video_links) {
            // For video products, show video links section
            document.getElementById('videoLinksSection').style.display = 'block';
            loadAvailableVideoLinks();
            loadVideoTypes();
            loadExistingVideoLinks(); // Load existing video links for this product
        } else {
            // For non-account, non-video products, show file upload section
            document.getElementById('productFilesSection').style.display = 'block';
            loadExistingFiles(); // Load existing files when showing the section
        }
    }
}

function loadPackageAccounts() {
    console.log('Loading package accounts for product:', productId);
    const url = `/api/admin/marketplace/package-accounts-backup/${productId}`;  // Use backup API that works
    console.log('🔍 API URL (backup):', url);

    fetch(url)
        .then(response => {
            console.log('🔍 Response status:', response.status);
            console.log('🔍 Response ok:', response.ok);
            console.log('🔍 Response url:', response.url);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return response.json();
        })
        .then(data => {
            console.log('🔍 Package accounts response:', data);
            if (data.success) {
                packageAccounts = data.accounts;
                displayPackageAccounts();
                updateAccountStats();
            } else {
                console.error('❌ Failed to load package accounts:', data.error);
            }
        })
        .catch(error => {
            console.error('❌ Error loading package accounts:', error);
        });
}

function displayPackageAccounts() {
    const container = document.getElementById('currentPackageAccounts');

    console.log('🔍 Displaying package accounts:', packageAccounts.length);
    console.log('🔍 Package accounts data:', packageAccounts);

    if (packageAccounts.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-3">Chưa có account nào trong package</div>';
        return;
    }

    container.innerHTML = '';

    packageAccounts.forEach((account, index) => {
        console.log(`🔍 Creating package account ${index}:`, account);

        const accountDiv = document.createElement('div');
        accountDiv.className = `account-item ${account.is_sold ? 'sold' : ''}`;
        accountDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${account.account_name}</strong>
                    <br><small class="text-muted">${account.follower_count.toLocaleString()} followers</small>
                    ${account.is_sold ? `<br><small class="text-danger">Đã bán cho: ${account.sold_to_username || 'Unknown'}</small>` : ''}
                </div>
                <div class="text-end">
                    <span class="badge bg-${account.is_sold ? 'danger' : 'success'}">${account.is_sold ? 'Đã bán' : 'Có sẵn'}</span>
                    ${!account.is_sold ? `<br><button class="btn btn-sm btn-outline-danger mt-1" onclick="removeAccountFromPackage(${account.package_account_id}, '${account.account_name}')" title="Xóa account khỏi package">
                        <i class="cil-trash"></i>
                    </button>` : ''}
                </div>
            </div>
        `;
        container.appendChild(accountDiv);
    });

    console.log('✅ Package accounts displayed successfully');
}

function updateAccountStats() {
    const assigned = packageAccounts.length;
    const sold = packageAccounts.filter(acc => acc.is_sold).length;
    
    document.getElementById('assignedAccountsCount').textContent = assigned;
    document.getElementById('soldAccountsCount').textContent = sold;
    
    // Update stock
    const available = assigned - sold;
    document.getElementById('stockInput').value = available;
    document.getElementById('currentStock').textContent = available;
}

function loadAvailableAccounts() {
    console.log('Loading available accounts...');

    // Build query parameters for server-side filtering
    const params = new URLSearchParams();

    const statusFilter = document.getElementById('accountStatusFilter').value;
    const teamFilter = document.getElementById('accountTeamFilter').value;
    const minFollowers = document.getElementById('minFollowersFilter').value;
    const maxFollowers = document.getElementById('maxFollowersFilter').value;
    const search = document.getElementById('accountSearchInput').value;

    if (statusFilter) params.append('status', statusFilter);
    if (teamFilter) params.append('team', teamFilter);
    if (minFollowers) params.append('min_followers', minFollowers);
    if (maxFollowers) params.append('max_followers', maxFollowers);
    if (search) params.append('search', search);

    fetch(`/api/admin/marketplace/available-accounts?${params}`)
        .then(response => response.json())
        .then(data => {
            console.log('Available accounts response:', data);
            if (data.success) {
                availableAccounts = data.accounts;
                console.log('Available accounts loaded:', availableAccounts.length);
                displayAvailableAccounts();
                populateTeamFilter();
            } else {
                console.error('Failed to load available accounts:', data.error);
            }
        })
        .catch(error => console.error('Error loading available accounts:', error));
}

function populateTeamFilter() {
    const teamFilter = document.getElementById('accountTeamFilter');
    const teams = [...new Set(availableAccounts.map(acc => acc.team_name).filter(Boolean))];

    teamFilter.innerHTML = '<option value="">Tất cả team</option>';
    teams.forEach(team => {
        const option = document.createElement('option');
        option.value = team;
        option.textContent = team;
        teamFilter.appendChild(option);
    });
}

function displayAvailableAccounts() {
    const container = document.getElementById('availableAccountsList');

    console.log('🔍 Displaying available accounts:', availableAccounts.length);

    if (availableAccounts.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-3">Không có account nào khả dụng với bộ lọc hiện tại</div>';
        // Reset select all checkbox when no accounts
        document.getElementById('selectAllAccounts').checked = false;
        return;
    }

    container.innerHTML = '';

    // Reset select all checkbox when accounts change
    document.getElementById('selectAllAccounts').checked = false;

    availableAccounts.forEach((account, index) => {
        console.log(`🔍 Creating account item ${index}:`, account);

        const accountDiv = document.createElement('div');
        accountDiv.className = 'account-item';
        accountDiv.innerHTML = `
            <div class="form-check">
                <input class="form-check-input account-checkbox" type="checkbox"
                       value="${account.account_id}" id="available_account_${account.account_id}"
                       onchange="updateSelectAllState()">
                <label class="form-check-label w-100" for="available_account_${account.account_id}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${account.account_name}</strong>
                            <br><small class="text-muted">${account.team_name || 'No team'}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-${getStatusBadgeColor(account.status)}">${account.status}</span>
                            <br><small class="text-muted">${account.follower_count.toLocaleString()} followers</small>
                        </div>
                    </div>
                </label>
            </div>
        `;
        container.appendChild(accountDiv);
    });

    // Debug: Check if checkboxes were created and update select all state
    setTimeout(() => {
        const checkboxes = document.querySelectorAll('.account-checkbox');
        console.log('🔍 Total checkboxes created:', checkboxes.length);
        updateSelectAllState();
    }, 100);
}

function getStatusBadgeColor(status) {
    const colors = {
        'Live': 'success',
        'Có giỏ': 'primary',
        'Đang nuôi': 'warning',
        'Die': 'danger'
    };
    return colors[status] || 'secondary';
}

function addSelectedAccountsToPackage() {
    console.log('🔍 addSelectedAccountsToPackage called');
    console.log('🔍 Current productId:', productId);

    const checkboxes = document.querySelectorAll('.account-checkbox:checked');
    console.log('🔍 Found checkboxes:', checkboxes);
    console.log('🔍 Selected account checkboxes count:', checkboxes.length);

    // Debug each checkbox
    checkboxes.forEach((cb, index) => {
        console.log(`🔍 Checkbox ${index}: value=${cb.value}, checked=${cb.checked}`);
    });

    const accountIds = Array.from(checkboxes).map(cb => {
        const id = parseInt(cb.value);
        console.log(`🔍 Converting ${cb.value} to ${id}`);
        return id;
    });

    console.log('🔍 Final account IDs to add:', accountIds);

    if (accountIds.length === 0) {
        alert('Vui lòng chọn ít nhất một account');
        return;
    }

    const requestData = {
        product_id: productId,
        account_ids: accountIds
    };

    console.log('🔍 Request data object:', requestData);
    console.log('🔍 JSON stringified:', JSON.stringify(requestData));

    fetch('/api/admin/marketplace/assign-accounts-backup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('🔍 Response status:', response.status);
        console.log('🔍 Response headers:', response.headers);
        console.log('🔍 Response ok:', response.ok);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.json();
    })
    .then(data => {
        console.log('🔍 Response data:', data);
        if (data.success) {
            alert(`Thêm accounts thành công! Đã thêm ${data.added_count} accounts.`);
            loadPackageAccounts();
            loadAvailableAccounts();
            // Clear selections
            document.querySelectorAll('.account-checkbox:checked').forEach(cb => cb.checked = false);
        } else {
            alert('Lỗi: ' + data.error);
        }
    })
    .catch(error => {
        console.error('🔍 Fetch error:', error);
        alert('Lỗi kết nối: ' + error.message);
    });
}

function removeAccountFromPackage(packageAccountId, accountName) {
    // Confirm before removing
    if (!confirm(`Bạn có chắc chắn muốn xóa account "${accountName || 'này'}" khỏi package?\n\nLưu ý: Account đã bán sẽ không thể xóa.`)) {
        return;
    }

    // Show loading state
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="cil-reload fa-spin"></i> Đang xóa...';
    button.disabled = true;

    fetch(`/api/admin/marketplace/package-accounts/${packageAccountId}`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showToast(data.message, 'success');

            // Remove the account row from UI
            const accountRow = button.closest('.account-item, .list-group-item, tr');
            if (accountRow) {
                accountRow.style.transition = 'opacity 0.3s ease';
                accountRow.style.opacity = '0';
                setTimeout(() => {
                    accountRow.remove();
                    // Update account count if exists
                    updateAccountCount();
                }, 300);
            }
        } else {
            // Show error message
            showToast(data.error, 'error');

            // Restore button state
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error removing account:', error);
        showToast('Lỗi kết nối: ' + error.message, 'error');

        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

function previewThumbnail(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('thumbnailImg').src = e.target.result;
            document.getElementById('thumbnailPreview').style.display = 'block';
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function removeThumbnail() {
    document.getElementById('thumbnailInput').value = '';
    document.getElementById('currentThumbnail').value = '';
    document.getElementById('thumbnailImg').src = '/static/marketplace/thumbnails/default.png';
}

function updateProduct() {
    const formData = new FormData(document.getElementById('updateProductForm'));

    // Add selected files for non-account products
    const productTypeSelect = document.querySelector('select[name="product_type_id"]');
    const selectedOption = productTypeSelect.querySelector(`option[value="${productTypeSelect.value}"]`);
    const metadata = selectedOption ? JSON.parse(selectedOption.getAttribute('data-metadata') || '{}') : {};

    if (metadata.requires_video_links) {
        // Add selected video links
        formData.append('video_link_ids', JSON.stringify(selectedVideoLinkIds));
        console.log('Adding video links to form:', selectedVideoLinkIds);
    } else if (!metadata.requires_accounts && selectedFiles.length > 0) {
        selectedFiles.forEach((fileObj, index) => {
            formData.append(`product_files`, fileObj.file);
            formData.append(`file_descriptions`, fileObj.description);
            formData.append(`file_previews`, fileObj.isPreview);
            formData.append(`file_types`, fileObj.type);
            formData.append(`file_sort_orders`, fileObj.sortOrder);
        });
        formData.append('files_count', selectedFiles.length);
    }

    console.log('Updating product with ID:', productId);

    fetch(`/api/admin/marketplace/products/${productId}`, {
        method: 'PUT',
        body: formData
    })
    .then(response => {
        console.log('Update response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('Update response data:', data);
        if (data.success) {
            if (selectedFiles.length > 0) {
                alert(`Cập nhật sản phẩm thành công với ${selectedFiles.length} files mới!`);
            } else {
                alert('Cập nhật sản phẩm thành công!');
            }
            window.location.href = '/admin/marketplace/products';
        } else {
            alert('Lỗi: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Update error:', error);
        alert('Lỗi kết nối: ' + error.message);
    });
}

function setupEventListeners() {
    // Add accounts button
    const addButton = document.createElement('button');
    addButton.type = 'button';
    addButton.className = 'btn btn-primary btn-sm mt-2';
    addButton.innerHTML = '<i class="cil-plus"></i> Thêm accounts đã chọn';
    addButton.onclick = addSelectedAccountsToPackage;
    
    // Add button after available accounts list
    document.getElementById('availableAccountsList').parentNode.appendChild(addButton);
}

// File Management Functions
function loadExistingFiles() {
    fetch(`/api/admin/marketplace/products/${productId}/files`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                existingFiles = data.files;
                displayExistingFiles();
            } else {
                console.error('Error loading files:', data.error);
            }
        })
        .catch(error => console.error('Error loading files:', error));
}

function displayExistingFiles() {
    const container = document.getElementById('existingFilesList');

    if (existingFiles.length === 0) {
        container.innerHTML = '<p class="text-muted text-center py-3">Chưa có file nào</p>';
        return;
    }

    container.innerHTML = existingFiles.map(file => `
        <div class="file-item" data-file-id="${file.file_id}">
            ${file.file_type === 'image' ?
                `<img src="/static/uploads/products/${file.file_path}" class="file-preview" alt="Preview">` :
                `<div class="file-icon ${file.file_type}">
                    <i class="${getFileIcon(file.file_type)}"></i>
                </div>`
            }
            <div class="file-info">
                <div class="file-name">${file.original_name}</div>
                <div class="file-meta">${formatFileSize(file.file_size)} • ${file.file_type} • Downloaded: ${file.download_count} times</div>
                ${file.description ? `<small class="text-muted">${file.description}</small>` : ''}
            </div>
            <div class="file-actions">
                <span class="badge ${file.is_preview ? 'bg-success' : 'bg-secondary'}">${file.is_preview ? 'Preview' : 'Private'}</span>
                <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadFile(${file.file_id})">
                    <i class="cil-cloud-download"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteExistingFile(${file.file_id})">
                    <i class="cil-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

function downloadFile(fileId) {
    window.open(`/api/products/${productId}/files/${fileId}/download`, '_blank');
}

function deleteExistingFile(fileId) {
    if (!confirm('Bạn có chắc muốn xóa file này?')) return;

    fetch(`/api/admin/marketplace/products/${productId}/files/${fileId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadExistingFiles(); // Reload list
            showToast('Xóa file thành công', 'success');
        } else {
            showToast('Lỗi: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showToast('Lỗi kết nối: ' + error, 'danger');
    });
}

// File Upload Functions (copy from create_product.html)
function handleFileSelection(input) {
    const files = Array.from(input.files);
    files.forEach(file => addFileToList(file));
    input.value = ''; // Reset input
}

function addFileToList(file) {
    // Validate file size (50MB max)
    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
        alert(`File "${file.name}" quá lớn. Tối đa 50MB.`);
        return;
    }

    // Create file object
    const fileObj = {
        id: ++fileCounter,
        file: file,
        name: file.name,
        size: file.size,
        type: getFileType(file),
        description: '',
        isPreview: false,
        sortOrder: selectedFiles.length
    };

    selectedFiles.push(fileObj);
    displaySelectedFiles();
}

function getFileType(file) {
    const extension = file.name.split('.').pop().toLowerCase();
    const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
    const documentExts = ['pdf', 'doc', 'docx', 'txt', 'rtf', 'xls', 'xlsx', 'ppt', 'pptx'];
    const videoExts = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
    const audioExts = ['mp3', 'wav', 'ogg', 'aac', 'flac'];

    if (imageExts.includes(extension)) return 'image';
    if (documentExts.includes(extension)) return 'document';
    if (videoExts.includes(extension)) return 'video';
    if (audioExts.includes(extension)) return 'audio';
    return 'other';
}

function displaySelectedFiles() {
    const container = document.getElementById('selectedFilesList');

    if (selectedFiles.length === 0) {
        container.innerHTML = '<p class="text-muted text-center py-3">Chưa có file mới nào được chọn</p>';
        return;
    }

    container.innerHTML = selectedFiles.map(fileObj => `
        <div class="file-item" data-file-id="${fileObj.id}">
            ${fileObj.type === 'image' ?
                `<img src="${URL.createObjectURL(fileObj.file)}" class="file-preview" alt="Preview">` :
                `<div class="file-icon ${fileObj.type}">
                    <i class="${getFileIcon(fileObj.type)}"></i>
                </div>`
            }
            <div class="file-info">
                <div class="file-name">${fileObj.name}</div>
                <div class="file-meta">${formatFileSize(fileObj.size)} • ${fileObj.type}</div>
                <input type="text" class="form-control form-control-sm mt-1"
                       placeholder="Mô tả file (tùy chọn)"
                       value="${fileObj.description}"
                       onchange="updateFileDescription(${fileObj.id}, this.value)">
            </div>
            <div class="file-actions">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox"
                           ${fileObj.isPreview ? 'checked' : ''}
                           onchange="toggleFilePreview(${fileObj.id}, this.checked)">
                    <label class="form-check-label">Preview</label>
                </div>
                <button type="button" class="btn btn-sm btn-outline-danger"
                        onclick="removeFile(${fileObj.id})">
                    <i class="cil-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

function getFileIcon(type) {
    const icons = {
        'image': 'cil-image',
        'document': 'cil-description',
        'video': 'cil-video',
        'audio': 'cil-audio-spectrum',
        'other': 'cil-file'
    };
    return icons[type] || 'cil-file';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function updateFileDescription(fileId, description) {
    const fileObj = selectedFiles.find(f => f.id === fileId);
    if (fileObj) {
        fileObj.description = description;
    }
}

function toggleFilePreview(fileId, isPreview) {
    const fileObj = selectedFiles.find(f => f.id === fileId);
    if (fileObj) {
        fileObj.isPreview = isPreview;
    }
}

function removeFile(fileId) {
    selectedFiles = selectedFiles.filter(f => f.id !== fileId);
    displaySelectedFiles();
}

function showToast(message, type = 'info') {
    // Simple alert for now - can be enhanced with proper toast
    if (type === 'success') {
        alert('✅ ' + message);
    } else if (type === 'error') {
        alert('❌ ' + message);
    } else {
        alert(message);
    }
}

function updateAccountCount() {
    // Update account count display if exists
    const accountItems = document.querySelectorAll('.account-item, .list-group-item[data-account-id]');
    const countElement = document.querySelector('.account-count, .accounts-count');

    if (countElement) {
        countElement.textContent = accountItems.length;
    }

    // Update package info if no accounts left
    if (accountItems.length === 0) {
        const accountsList = document.getElementById('currentAccountsList');
        if (accountsList) {
            accountsList.innerHTML = '<div class="text-center text-muted py-3">Chưa có account nào trong package này</div>';
        }
    }
}

// Update select all checkbox state based on individual checkboxes
function updateSelectAllState() {
    const allCheckboxes = document.querySelectorAll('.account-checkbox');
    const checkedCheckboxes = document.querySelectorAll('.account-checkbox:checked');
    const selectAllCheckbox = document.getElementById('selectAllAccounts');

    if (!selectAllCheckbox) return; // Exit if checkbox doesn't exist

    if (allCheckboxes.length === 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCheckboxes.length === allCheckboxes.length) {
        selectAllCheckbox.checked = true;
        selectAllCheckbox.indeterminate = false;
    } else if (checkedCheckboxes.length > 0) {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = true;
    } else {
        selectAllCheckbox.checked = false;
        selectAllCheckbox.indeterminate = false;
    }
}

// Debounce function to limit API calls
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Setup event listeners for account filters
document.addEventListener('DOMContentLoaded', function() {
    // Account package filters - reload from server when filters change
    const accountSearchInput = document.getElementById('accountSearchInput');
    const accountStatusFilter = document.getElementById('accountStatusFilter');
    const accountTeamFilter = document.getElementById('accountTeamFilter');
    const minFollowersFilter = document.getElementById('minFollowersFilter');
    const maxFollowersFilter = document.getElementById('maxFollowersFilter');

    if (accountSearchInput) {
        accountSearchInput.addEventListener('input', debounce(loadAvailableAccounts, 500));
    }
    if (accountStatusFilter) {
        accountStatusFilter.addEventListener('change', loadAvailableAccounts);
    }
    if (accountTeamFilter) {
        accountTeamFilter.addEventListener('change', loadAvailableAccounts);
    }
    if (minFollowersFilter) {
        minFollowersFilter.addEventListener('input', debounce(loadAvailableAccounts, 500));
    }
    if (maxFollowersFilter) {
        maxFollowersFilter.addEventListener('input', debounce(loadAvailableAccounts, 500));
    }

    // Select all accounts checkbox
    const selectAllAccounts = document.getElementById('selectAllAccounts');
    if (selectAllAccounts) {
        selectAllAccounts.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.account-checkbox');
            checkboxes.forEach(cb => {
                cb.checked = this.checked;
            });
            // Remove indeterminate state when manually toggled
            this.indeterminate = false;
            console.log('Select all toggled:', this.checked, 'Affected checkboxes:', checkboxes.length);
        });
    }
});

// ===== VIDEO LINKS FUNCTIONS =====

function loadAvailableVideoLinks() {
    const params = new URLSearchParams({
        status: 'available',
        video_type: document.getElementById('videoTypeFilter').value,
        video_count: document.getElementById('videoCountFilter').value,
        search: document.getElementById('videoSearchInput').value
    });

    fetch(`/api/admin/marketplace/video-links?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                availableVideoLinks = data.video_links;
                displayAvailableVideoLinks();
            } else {
                console.error('Error loading video links:', data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
        });
}

function displayAvailableVideoLinks() {
    const container = document.getElementById('availableVideoLinksContainer');

    if (availableVideoLinks.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="cil-video" style="font-size: 2rem; color: #ccc;"></i>
                <p class="text-muted mt-2">Không có video links nào phù hợp</p>
            </div>
        `;
        return;
    }

    container.innerHTML = '';

    availableVideoLinks.forEach(link => {
        const isSelected = selectedVideoLinkIds.includes(link.link_id);

        const linkCard = document.createElement('div');
        linkCard.className = `border rounded p-3 mb-2 ${isSelected ? 'border-primary bg-light' : ''}`;
        linkCard.style.cursor = 'pointer';

        linkCard.innerHTML = `
            <div class="form-check">
                <input class="form-check-input" type="checkbox"
                       id="videoLink_${link.link_id}"
                       ${isSelected ? 'checked' : ''}
                       onchange="toggleVideoLinkSelection(${link.link_id}, this.checked)">
                <label class="form-check-label w-100" for="videoLink_${link.link_id}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">${link.name}</h6>
                            <p class="text-muted mb-1">${link.description || 'Không có mô tả'}</p>
                            <div>
                                <span class="badge bg-info me-2">${link.video_type || 'Chưa phân loại'}</span>
                                <span class="badge bg-primary">${link.video_count} videos</span>
                            </div>
                        </div>
                        <div class="text-end">
                            <a href="${link.drive_url}" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="cil-external-link"></i>
                            </a>
                        </div>
                    </div>
                </label>
            </div>
        `;

        container.appendChild(linkCard);
    });

    updateVideoLinksStats();
}

function toggleVideoLinkSelection(linkId, isSelected) {
    if (isSelected) {
        if (!selectedVideoLinkIds.includes(linkId)) {
            selectedVideoLinkIds.push(linkId);
        }
    } else {
        selectedVideoLinkIds = selectedVideoLinkIds.filter(id => id !== linkId);
    }

    updateVideoLinksStats();
    displayAvailableVideoLinks(); // Refresh display
}

function selectAllVideoLinks() {
    selectedVideoLinkIds = availableVideoLinks.map(link => link.link_id);
    displayAvailableVideoLinks();
}

function clearVideoLinksSelection() {
    selectedVideoLinkIds = [];
    displayAvailableVideoLinks();
}

function updateVideoLinksStats() {
    document.getElementById('selectedVideoLinksCount').textContent = selectedVideoLinkIds.length;
    document.getElementById('calculatedStock').textContent = selectedVideoLinkIds.length;

    // Update stock input
    const stockInput = document.querySelector('input[name="stock"]');
    if (stockInput) {
        stockInput.value = selectedVideoLinkIds.length;
        stockInput.readOnly = true;
    }
}

function loadVideoTypes() {
    fetch('/api/admin/marketplace/video-types')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('videoTypeFilter');
                select.innerHTML = '<option value="">Tất cả loại</option>';

                data.video_types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading video types:', error));
}

function loadExistingVideoLinks() {
    // Load video links that are already assigned to this product
    fetch(`/api/admin/marketplace/products/${productId}/video-links`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                selectedVideoLinkIds = data.video_links.map(link => link.link_id);
                updateVideoLinksStats();
                // Refresh display to show selected state
                if (availableVideoLinks.length > 0) {
                    displayAvailableVideoLinks();
                }
            } else {
                console.error('Error loading existing video links:', data.error);
            }
        })
        .catch(error => console.error('Error loading existing video links:', error));
}

</script>
{% endblock %}
