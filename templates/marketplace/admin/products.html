{% extends "base_coreui.html" %}
{% block title %}Quản lý sản phẩm - Marketplace{% endblock %}

{% block head_extra %}
<style>
    .product-thumbnail {
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 8px;
    }
    .product-card {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
    }
    .product-type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    .upload-area {
        border: 2px dashed #ccc;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        cursor: pointer;
        transition: border-color 0.3s;
    }
    .upload-area:hover {
        border-color: #00C6AE;
    }
    .upload-area.dragover {
        border-color: #00C6AE;
        background-color: #f0f9ff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="cil-basket text-primary"></i> Quản lý sản phẩm Marketplace</h2>
                    <p class="text-muted">Tạo và quản lý sản phẩm bán trên marketplace</p>
                </div>
                <a href="/admin/marketplace/products/create" class="btn btn-primary">
                    <i class="cil-plus"></i> Tạo sản phẩm mới
                </a>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalProducts">0</h4>
                            <p class="mb-0">Tổng sản phẩm</p>
                        </div>
                        <i class="cil-basket fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="activeProducts">0</h4>
                            <p class="mb-0">Đang bán</p>
                        </div>
                        <i class="cil-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="lowStockProducts">0</h4>
                            <p class="mb-0">Sắp hết hàng</p>
                        </div>
                        <i class="cil-warning fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalRevenue">0 MP</h4>
                            <p class="mb-0">Doanh thu tháng</p>
                        </div>
                        <i class="cil-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <label>Danh mục:</label>
                    <select class="form-select" id="categoryFilter">
                        <option value="">Tất cả danh mục</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label>Loại sản phẩm:</label>
                    <select class="form-select" id="typeFilter">
                        <option value="">Tất cả loại</option>
                        <option value="account">Account Package</option>
                        <option value="win_product">Win Product</option>
                        <option value="course">Khóa học</option>
                        <option value="aff_package">AFF Package</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label>Trạng thái:</label>
                    <select class="form-select" id="statusFilter">
                        <option value="">Tất cả</option>
                        <option value="active">Đang bán</option>
                        <option value="inactive">Tạm dừng</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label>Tìm kiếm:</label>
                    <input type="text" class="form-control" id="searchInput" placeholder="Tên sản phẩm...">
                </div>
            </div>
        </div>
    </div>

    <!-- Products Table -->
    <div class="card">
        <div class="card-header">
            <h5><i class="cil-list"></i> Danh sách sản phẩm</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Hình ảnh</th>
                            <th>Tên sản phẩm</th>
                            <th>Danh mục</th>
                            <th>Loại</th>
                            <th>Giá</th>
                            <th>Kho</th>
                            <th>Giới hạn mua</th>
                            <th>Trạng thái</th>
                            <th>Thao tác</th>
                        </tr>
                    </thead>
                    <tbody id="productsTableBody">
                        <!-- Products will be loaded here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Create Product Modal -->
<div class="modal fade" id="createProductModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Tạo sản phẩm mới</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal"></button>
            </div>
            <form id="createProductForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tên sản phẩm *</label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Danh mục *</label>
                                <select class="form-select" name="category_id" required>
                                    <option value="">Chọn danh mục</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Loại sản phẩm *</label>
                                <select class="form-select" name="product_type" required onchange="toggleProductTypeFields(this.value)">
                                    <option value="">Chọn loại</option>
                                    <option value="account">Account Package</option>
                                    <option value="win_product">Win Product</option>
                                    <option value="course">Khóa học</option>
                                    <option value="aff_package">AFF Package</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Giá (MP) *</label>
                                <input type="number" class="form-control" name="price" min="0" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Mô tả ngắn</label>
                        <input type="text" class="form-control" name="short_description" maxlength="500">
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Mô tả chi tiết</label>
                        <textarea class="form-control" name="description" rows="4"></textarea>
                    </div>

                    <!-- Thumbnail Upload -->
                    <div class="mb-3">
                        <label class="form-label">Hình ảnh sản phẩm</label>
                        <div class="upload-area" onclick="document.getElementById('thumbnailInput').click()">
                            <i class="cil-cloud-upload fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Click để chọn hình ảnh hoặc kéo thả vào đây</p>
                            <small class="text-muted">Hỗ trợ: JPG, PNG, GIF (tối đa 5MB)</small>
                        </div>
                        <input type="file" id="thumbnailInput" name="thumbnail" accept="image/*" style="display: none;" onchange="previewThumbnail(this)">
                        <div id="thumbnailPreview" class="mt-2" style="display: none;">
                            <img id="thumbnailImg" class="img-thumbnail" style="max-width: 200px;">
                        </div>
                    </div>

                    <!-- Stock Settings -->
                    <div class="row" id="stockSettings">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Số lượng kho</label>
                                <input type="number" class="form-control" name="stock" min="0" value="0">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3 form-check mt-4">
                                <input type="checkbox" class="form-check-input" name="unlimited_stock" id="unlimitedStock">
                                <label class="form-check-label" for="unlimitedStock">
                                    Không giới hạn kho
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- AFF Package Settings -->
                    <div id="affPackageSettings" style="display: none;">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Giới hạn account</label>
                                    <input type="number" class="form-control" name="account_limit" min="1" value="10">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Thời hạn (tháng)</label>
                                    <input type="number" class="form-control" name="subscription_duration_months" min="1" value="1">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Package Settings -->
                    <div id="accountPackageSettings" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">Tiêu chí account</label>
                            <textarea class="form-control" name="account_criteria" rows="3" placeholder="VD: Follower > 1000, Status = Live, Cookie có sẵn..."></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Thời gian bảo hành (ngày)</label>
                            <input type="number" class="form-control" name="warranty_days" min="0" value="3">
                        </div>

                        <!-- Account Selection -->
                        <div class="mb-3">
                            <label class="form-label">Chọn accounts cho package này</label>
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <small class="text-muted">Chọn accounts có sẵn để bán trong package này</small>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="loadAvailableAccounts()">
                                    <i class="cil-reload"></i> Tải lại danh sách
                                </button>
                            </div>

                            <!-- Filter accounts -->
                            <div class="row mb-3">
                                <div class="col-md-4">
                                    <input type="text" class="form-control form-control-sm" id="accountSearchInput" placeholder="Tìm theo tên account...">
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select form-select-sm" id="accountStatusFilter">
                                        <option value="">Tất cả trạng thái</option>
                                        <option value="Live">Live</option>
                                        <option value="Có giỏ">Có giỏ</option>
                                        <option value="Đang nuôi">Đang nuôi</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-select form-select-sm" id="accountTeamFilter">
                                        <option value="">Tất cả team</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="selectAllAccounts">
                                        <label class="form-check-label small" for="selectAllAccounts">Chọn tất cả</label>
                                    </div>
                                </div>
                            </div>

                            <!-- Accounts list -->
                            <div id="availableAccountsList" style="max-height: 300px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 8px; padding: 10px;">
                                <div class="text-center text-muted py-3">
                                    <i class="cil-reload fa-spin"></i> Đang tải accounts...
                                </div>
                            </div>

                            <div class="mt-2">
                                <small class="text-muted">Đã chọn: <span id="selectedAccountsCount">0</span> accounts</small>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" name="is_featured" id="isFeatured">
                        <label class="form-check-label" for="isFeatured">
                            Sản phẩm nổi bật
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">Tạo sản phẩm</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Global variables
let products = [];
let categories = [];
let availableAccounts = [];
let selectedAccountIds = [];

// Initialize page
$(document).ready(function() {
    loadCategories();
    loadProducts();
    setupEventListeners();
});

function loadCategories() {
    fetch('/api/marketplace/categories')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                categories = data.categories;
                populateCategorySelects();
            }
        })
        .catch(error => console.error('Error loading categories:', error));
}

function populateCategorySelects() {
    const selects = ['categoryFilter', 'createProductForm select[name="category_id"]'];
    
    selects.forEach(selector => {
        const select = document.querySelector('#' + selector) || document.querySelector(selector);
        if (select) {
            // Clear existing options (except first one for filters)
            const isFilter = selector.includes('Filter');
            if (!isFilter) {
                select.innerHTML = '<option value="">Chọn danh mục</option>';
            }
            
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.category_id;
                option.textContent = category.name;
                select.appendChild(option);
            });
        }
    });
}

function loadProducts() {
    // Build query parameters for admin view
    const params = new URLSearchParams({
        show_all: 'true',  // Admin can see all products
        status: document.getElementById('statusFilter').value || 'all'
    });

    fetch(`/api/marketplace/products?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                products = data.products;
                displayProducts();
                updateStats();
            }
        })
        .catch(error => console.error('Error loading products:', error));
}

function displayProducts() {
    const tbody = document.getElementById('productsTableBody');
    tbody.innerHTML = '';

    if (products.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" class="text-center">Chưa có sản phẩm nào</td></tr>';
        return;
    }

    products.forEach(product => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <img src="${product.image_url}" alt="${product.name}" class="product-thumbnail">
            </td>
            <td>
                <strong>${product.name}</strong>
                <br><small class="text-muted">${product.short_description || ''}</small>
            </td>
            <td>${product.category_name}</td>
            <td>
                <span class="badge bg-info product-type-badge">${getProductTypeLabel(product.product_type, product.type_name)}</span>
            </td>
            <td><strong>${product.price.toLocaleString()} MP</strong></td>
            <td>
                ${product.unlimited_stock ?
                    '<span class="badge bg-success">Không giới hạn</span>' :
                    `<span class="badge ${product.stock < 5 ? 'bg-warning' : 'bg-primary'}">${product.stock}</span>`
                }
                <br><small class="text-muted">Đã bán: ${product.sold_count || 0}</small>
            </td>
            <td>
                ${product.max_quantity ?
                    `<span class="badge bg-warning">Tối đa ${product.max_quantity}</span>` :
                    '<span class="badge bg-success">Không giới hạn</span>'
                }
                ${product.max_quantity === 1 ? '<br><small class="text-muted">Chỉ mua 1 lần</small>' : ''}
            </td>
            <td>
                ${getStatusBadge(product.status, product.is_deleted)}
            </td>
            <td>
                <a href="/admin/marketplace/products/edit/${product.product_id}" class="btn btn-sm btn-outline-primary" title="Chỉnh sửa">
                    <i class="cil-pencil"></i>
                </a>
                ${!product.is_deleted ? `
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct(${product.product_id}, '${product.name}')" title="Xóa sản phẩm">
                        <i class="cil-trash"></i>
                    </button>
                ` : `
                    <span class="badge bg-secondary">Đã xóa</span>
                `}
            </td>
        `;
        tbody.appendChild(row);
    });
}

function getProductTypeLabel(type, typeName) {
    // Ưu tiên hiển thị tên ProductType gốc nếu có
    if (typeName) {
        return typeName;
    }

    // Fallback cho các type cũ
    const labels = {
        'account': 'Account',
        'win_product': 'Win Product',
        'course': 'Khóa học',
        'aff_package': 'AFF Package'
    };
    return labels[type] || type;
}

function getStatusBadge(status, isDeleted) {
    if (isDeleted) {
        return '<span class="badge bg-dark">🗑️ Đã xóa</span>';
    }

    const badges = {
        'active': '<span class="badge bg-success">🟢 Hoạt động</span>',
        'inactive': '<span class="badge bg-warning">🔴 Tạm dừng</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Không xác định</span>';
}

function deleteProduct(productId, productName) {
    if (!confirm(`Bạn có chắc chắn muốn xóa sản phẩm "${productName}"?\n\nLưu ý: Nếu sản phẩm đã có đơn hàng, hệ thống sẽ thực hiện xóa mềm để bảo vệ dữ liệu.`)) {
        return;
    }

    fetch(`/api/admin/marketplace/products/${productId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            loadProducts(); // Reload the list
        } else {
            alert('Lỗi: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi xóa sản phẩm');
    });
}

function updateStats() {
    document.getElementById('totalProducts').textContent = products.length;
    document.getElementById('activeProducts').textContent = products.filter(p => p.is_active).length;
    document.getElementById('lowStockProducts').textContent = products.filter(p => !p.unlimited_stock && p.stock < 5).length;
}

function showCreateProductModal() {
    new coreui.Modal(document.getElementById('createProductModal')).show();
}

function toggleProductTypeFields(type) {
    // Hide all type-specific fields
    document.getElementById('affPackageSettings').style.display = 'none';
    document.getElementById('accountPackageSettings').style.display = 'none';

    // Show relevant fields
    if (type === 'aff_package') {
        document.getElementById('affPackageSettings').style.display = 'block';
    } else if (type === 'account') {
        document.getElementById('accountPackageSettings').style.display = 'block';
        loadAvailableAccounts(); // Load accounts when account package is selected
    }
}

function loadAvailableAccounts() {
    const container = document.getElementById('availableAccountsList');
    container.innerHTML = '<div class="text-center text-muted py-3"><i class="cil-reload fa-spin"></i> Đang tải accounts...</div>';

    fetch('/api/admin/marketplace/available-accounts')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                availableAccounts = data.accounts;
                displayAvailableAccounts();
                populateTeamFilter();
            } else {
                container.innerHTML = '<div class="text-center text-danger py-3">Lỗi: ' + data.error + '</div>';
            }
        })
        .catch(error => {
            container.innerHTML = '<div class="text-center text-danger py-3">Lỗi kết nối: ' + error + '</div>';
        });
}

function populateTeamFilter() {
    const teamFilter = document.getElementById('accountTeamFilter');
    const teams = [...new Set(availableAccounts.map(acc => acc.team_name).filter(Boolean))];

    teamFilter.innerHTML = '<option value="">Tất cả team</option>';
    teams.forEach(team => {
        const option = document.createElement('option');
        option.value = team;
        option.textContent = team;
        teamFilter.appendChild(option);
    });
}

function displayAvailableAccounts() {
    const container = document.getElementById('availableAccountsList');

    if (availableAccounts.length === 0) {
        container.innerHTML = '<div class="text-center text-muted py-3">Không có account nào khả dụng</div>';
        return;
    }

    // Apply filters
    let filteredAccounts = availableAccounts;

    const searchTerm = document.getElementById('accountSearchInput').value.toLowerCase();
    const statusFilter = document.getElementById('accountStatusFilter').value;
    const teamFilter = document.getElementById('accountTeamFilter').value;

    if (searchTerm) {
        filteredAccounts = filteredAccounts.filter(acc =>
            acc.account_name.toLowerCase().includes(searchTerm)
        );
    }

    if (statusFilter) {
        filteredAccounts = filteredAccounts.filter(acc => acc.status === statusFilter);
    }

    if (teamFilter) {
        filteredAccounts = filteredAccounts.filter(acc => acc.team_name === teamFilter);
    }

    container.innerHTML = '';

    filteredAccounts.forEach(account => {
        const accountDiv = document.createElement('div');
        accountDiv.className = 'form-check mb-2 p-2 border rounded';
        accountDiv.innerHTML = `
            <input class="form-check-input account-checkbox" type="checkbox"
                   value="${account.account_id}" id="account_${account.account_id}"
                   ${selectedAccountIds.includes(account.account_id) ? 'checked' : ''}
                   onchange="updateSelectedAccounts()">
            <label class="form-check-label w-100" for="account_${account.account_id}">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${account.account_name}</strong>
                        <br><small class="text-muted">${account.team_name || 'No team'}</small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-${getStatusBadgeColor(account.status)}">${account.status}</span>
                        <br><small class="text-muted">${account.follower_count.toLocaleString()} followers</small>
                    </div>
                </div>
            </label>
        `;
        container.appendChild(accountDiv);
    });

    updateSelectedAccountsCount();
}

function getStatusBadgeColor(status) {
    const colors = {
        'Live': 'success',
        'Có giỏ': 'primary',
        'Đang nuôi': 'warning',
        'Die': 'danger'
    };
    return colors[status] || 'secondary';
}

function updateSelectedAccounts() {
    const checkboxes = document.querySelectorAll('.account-checkbox:checked');
    selectedAccountIds = Array.from(checkboxes).map(cb => parseInt(cb.value));
    updateSelectedAccountsCount();
}

function updateSelectedAccountsCount() {
    document.getElementById('selectedAccountsCount').textContent = selectedAccountIds.length;

    // Update stock field
    const stockInput = document.querySelector('input[name="stock"]');
    if (stockInput) {
        stockInput.value = selectedAccountIds.length;
    }
}

function previewThumbnail(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('thumbnailImg').src = e.target.result;
            document.getElementById('thumbnailPreview').style.display = 'block';
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function setupEventListeners() {
    // Form submission
    document.getElementById('createProductForm').addEventListener('submit', function(e) {
        e.preventDefault();
        createProduct();
    });

    // Filters
    ['categoryFilter', 'typeFilter', 'statusFilter', 'searchInput'].forEach(id => {
        document.getElementById(id).addEventListener('change', filterProducts);
        if (id === 'searchInput') {
            document.getElementById(id).addEventListener('input', filterProducts);
        }
    });

    // Account package filters
    document.getElementById('accountSearchInput').addEventListener('input', displayAvailableAccounts);
    document.getElementById('accountStatusFilter').addEventListener('change', displayAvailableAccounts);
    document.getElementById('accountTeamFilter').addEventListener('change', displayAvailableAccounts);

    // Select all accounts checkbox
    document.getElementById('selectAllAccounts').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.account-checkbox');
        checkboxes.forEach(cb => {
            cb.checked = this.checked;
        });
        updateSelectedAccounts();
    });
}

function createProduct() {
    const formData = new FormData(document.getElementById('createProductForm'));
    const productType = formData.get('product_type');

    // Add selected account IDs for account packages
    if (productType === 'account' && selectedAccountIds.length > 0) {
        formData.append('selected_account_ids', JSON.stringify(selectedAccountIds));
    }

    fetch('/api/admin/marketplace/products', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // If it's an account package, assign accounts to the package
            if (productType === 'account' && selectedAccountIds.length > 0) {
                assignAccountsToPackage(data.package_id, selectedAccountIds)
                    .then(() => {
                        showToast(`Tạo sản phẩm thành công với ${selectedAccountIds.length} accounts!`, 'success');
                        coreui.Modal.getInstance(document.getElementById('createProductModal')).hide();
                        loadProducts();
                        resetForm();
                    })
                    .catch(error => {
                        showToast('Sản phẩm đã tạo nhưng lỗi khi gán accounts: ' + error, 'warning');
                        loadProducts();
                    });
            } else {
                showToast('Tạo sản phẩm thành công!', 'success');
                coreui.Modal.getInstance(document.getElementById('createProductModal')).hide();
                loadProducts();
                resetForm();
            }
        } else {
            showToast('Lỗi: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        showToast('Lỗi kết nối: ' + error, 'danger');
    });
}

function assignAccountsToPackage(packageId, accountIds) {
    return fetch('/api/admin/marketplace/assign-accounts-to-package', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            package_id: packageId,
            account_ids: accountIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (!data.success) {
            throw new Error(data.error);
        }
        return data;
    });
}

function resetForm() {
    selectedAccountIds = [];
    availableAccounts = [];
    document.getElementById('availableAccountsList').innerHTML = '';
    document.getElementById('selectedAccountsCount').textContent = '0';
}

function filterProducts() {
    // Implementation for filtering products
    // Will be added in next iteration
}

function editProduct(productId) {
    // Implementation for editing products
    // Will be added in next iteration
}

// Event listeners
document.addEventListener('DOMContentLoaded', function() {
    loadProducts();

    // Add event listeners for filters
    document.getElementById('statusFilter').addEventListener('change', loadProducts);
    document.getElementById('categoryFilter').addEventListener('change', loadProducts);
    document.getElementById('typeFilter').addEventListener('change', loadProducts);
});

function showToast(message, type = 'info') {
    // Create toast notification
    const toastContainer = document.querySelector('.toast-container') || createToastContainer();
    
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div class="toast" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <strong class="me-auto">Thông báo</strong>
                <button type="button" class="btn-close" data-coreui-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    toastContainer.insertAdjacentHTML('beforeend', toastHtml);
    
    const toastElement = document.getElementById(toastId);
    const toast = new coreui.Toast(toastElement);
    toast.show();
    
    setTimeout(() => {
        if (toastElement.parentElement) {
            toastElement.remove();
        }
    }, 5000);
}

function createToastContainer() {
    const container = document.createElement('div');
    container.className = 'toast-container position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}
</script>
{% endblock %}
