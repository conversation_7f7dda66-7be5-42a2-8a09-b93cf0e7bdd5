<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>Đăng nhập - SAPMMO System</title>
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='images/logo.png') }}">

    <!-- CoreUI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/css/coreui.min.css">
    <!-- CoreUI Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/icons@2.1.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #FFDDEE 0%, #FFB6C1 50%, #FFC0CB 100%);
            background-attachment: fixed;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 192, 203, 0.2) 0%, transparent 50%);
            animation: float 6s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .login-container {
            width: 100%;
            max-width: 420px;
            padding: 20px;
            margin: auto;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .login-header {
            background: white;
            padding: 15px;
            text-align: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(0, 198, 174, 0.05) 0%, transparent 70%);
            animation: shimmer 4s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: rotate(0deg) scale(1); }
            50% { transform: rotate(180deg) scale(1.1); }
        }

        .logo-container {
            position: relative;
            z-index: 2;
            padding: 10px;
        }

        .logo-container img {
            height: 120px;
            width: auto;
            filter: drop-shadow(0 4px 12px rgba(0, 198, 174, 0.3));
            transition: all 0.3s ease;
        }

        .logo-container img:hover {
            transform: scale(1.08);
            filter: drop-shadow(0 6px 16px rgba(0, 198, 174, 0.4));
        }

        .login-body {
            padding: 40px 30px;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-label {
            color: #4a5568;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(0, 198, 174, 0.1);
            border-color: #00C6AE;
            background: white;
        }

        .btn-login {
            background: linear-gradient(135deg, #00C6AE 0%, #00A693 100%);
            border: none;
            border-radius: 12px;
            width: 100%;
            padding: 14px;
            font-weight: 600;
            font-size: 16px;
            margin-top: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 198, 174, 0.3);
        }

        .btn-login:active {
            transform: translateY(0);
        }

        .btn-register {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            border: none;
            border-radius: 12px;
            width: 100%;
            padding: 14px;
            font-weight: 600;
            font-size: 16px;
            margin-top: 10px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-register::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-register:hover::before {
            left: 100%;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }

        .btn-register:active {
            transform: translateY(0);
        }

        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e2e8f0;
        }

        .divider span {
            background: rgba(255, 255, 255, 0.9);
            padding: 0 15px;
            color: #6c757d;
            font-size: 14px;
        }

        .alert {
            margin-bottom: 20px;
            border-radius: 12px;
            border: none;
            backdrop-filter: blur(10px);
            padding: 15px;
            font-weight: 500;
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.1);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .alert-success {
            background: rgba(25, 135, 84, 0.1);
            color: #0f5132;
            border-left: 4px solid #198754;
        }

        .mb-3 {
            margin-bottom: 20px;
        }

        /* Responsive */
        @media (max-width: 480px) {
            body {
                padding: 10px 0;
            }

            .login-container {
                padding: 10px;
                max-width: 95%;
            }

            .login-body {
                padding: 20px 15px;
            }

            .logo-container img {
                height: 80px;
            }

            .login-header {
                padding: 15px 10px;
            }
        }

        /* Ensure content is always visible */
        @media (max-height: 600px) {
            body {
                align-items: flex-start;
                padding: 10px 0;
            }

            .login-header {
                padding: 15px 20px;
            }

            .login-body {
                padding: 20px 30px;
            }

            .logo-container img {
                height: 70px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="logo-container">
                    <img src="{{ url_for('static', filename='images/logo.png') }}" alt="MIP System">
                </div>
            </div>
            <div class="login-body">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    {{ error }}
                </div>
                {% endif %}

                {% if success %}
                <div class="alert alert-success" role="alert">
                    {{ success }}
                </div>
                {% endif %}

                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">Tên đăng nhập</label>
                        <input type="text" class="form-control" id="username" name="username"
                               value="{{ username if username else '' }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Mật khẩu</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary btn-login">Đăng nhập</button>
                </form>

                <div class="text-center mt-3 mb-3">
                    <a href="/forgot-password" class="text-decoration-none">Quên mật khẩu?</a>
                </div>

                <div class="divider">
                    <span>hoặc</span>
                </div>

                <a href="/register" class="btn btn-secondary btn-register">
                    <i class="cil-user-plus me-2"></i>Đăng ký tài khoản mới
                </a>
            </div>
        </div>
    </div>

    <!-- CoreUI JS -->
    <script src="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/js/coreui.bundle.min.js"></script>
</body>
</html>