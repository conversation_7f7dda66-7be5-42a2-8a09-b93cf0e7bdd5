<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>Đăng nhập - SAPMMO System</title>

    <!-- CoreUI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/css/coreui.min.css">
    <!-- CoreUI Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/icons@2.1.0/css/all.min.css">

    <style>
        body {
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .login-container {
            width: 100%;
            max-width: 400px;
            padding: 15px;
        }

        .login-card {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .login-header {
            background-color: #ff69b4; /* Giữ nguyên màu hồng như template cũ */
            padding: 20px;
            text-align: center;
            border-radius: 10px 10px 0 0;
        }

        .login-header h1 {
            color: #000000; /* Giữ nguyên màu đen như template cũ */
            font-size: 3rem; /* Giữ nguyên kích thước như template cũ */
            font-weight: bold;
            margin: 0;
        }

        .login-body {
            padding: 2rem;
            background-color: white;
            border-radius: 0 0 10px 10px;
        }

        .form-control:focus {
            box-shadow: none;
            border-color: #ff69b4;
        }

        .btn-login {
            background-color: #0d6efd; /* Giữ nguyên màu nút như template cũ */
            border-color: #0d6efd;
            width: 100%;
            padding: 0.5rem;
            font-weight: 500;
            margin-top: 1rem;
        }

        .btn-login:hover, .btn-login:focus {
            background-color: #0b5ed7;
            border-color: #0b5ed7;
        }

        .alert {
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1>MIP SYSTEM</h1>
            </div>
            <div class="login-body">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    {{ error }}
                </div>
                {% endif %}

                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">Tên đăng nhập</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Mật khẩu</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <button type="submit" class="btn btn-primary btn-login">Đăng nhập</button>
                </form>

                <div class="text-center mt-3">
                    <p class="mb-2">
                        <a href="/forgot-password" class="text-decoration-none">Quên mật khẩu?</a>
                    </p>
                    <p class="mb-0">
                        Chưa có tài khoản?
                        <a href="/register" class="text-decoration-none fw-bold">Đăng ký ngay</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- CoreUI JS -->
    <script src="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/js/coreui.bundle.min.js"></script>
</body>
</html>
