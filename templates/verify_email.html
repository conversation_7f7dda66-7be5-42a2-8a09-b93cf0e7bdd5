<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title><PERSON><PERSON><PERSON> thực Email - MIP System</title>
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='images/logo.png') }}">

    <!-- CoreUI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/css/coreui.min.css">
    <!-- CoreUI Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/icons@2.1.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .verify-container {
            width: 100%;
            max-width: 450px;
            padding: 20px;
        }

        .verify-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .verify-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .verify-header {
            background: linear-gradient(135deg, #00C6AE 0%, #00A693 100%);
            padding: 30px 20px;
            text-align: center;
            color: white;
        }

        .verify-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .verify-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .verify-body {
            padding: 40px 30px;
        }

        .email-display {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            margin-bottom: 25px;
        }

        .email-display strong {
            color: #00C6AE;
            font-size: 1.1rem;
        }

        .otp-input-group {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 25px 0;
        }

        .otp-input {
            width: 50px;
            height: 50px;
            text-align: center;
            font-size: 1.5rem;
            font-weight: bold;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
        }

        .otp-input:focus {
            outline: none;
            border-color: #00C6AE;
            box-shadow: 0 0 0 3px rgba(0, 198, 174, 0.1);
            background: white;
        }

        .btn-verify {
            background: linear-gradient(135deg, #00C6AE 0%, #00A693 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 12px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-verify:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 198, 174, 0.3);
        }

        .btn-verify:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }

        .resend-section {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .countdown {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .btn-resend {
            background: none;
            border: none;
            color: #00C6AE;
            text-decoration: underline;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .btn-resend:hover {
            color: #00A693;
        }

        .btn-resend:disabled {
            color: #6c757d;
            cursor: not-allowed;
            text-decoration: none;
        }

        .alert {
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #6c757d;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .back-link a:hover {
            color: #00C6AE;
        }
    </style>
</head>
<body>
    <div class="verify-container">
        <div class="verify-card">
            <div class="verify-header">
                <h1><i class="cil-envelope-closed me-2"></i>Xác thực Email</h1>
                <p>Nhập mã xác thực đã gửi đến email của bạn</p>
            </div>
            <div class="verify-body">
                <div id="alertContainer"></div>

                <div class="email-display">
                    <p class="mb-1">Mã xác thực đã được gửi đến:</p>
                    <strong>{{ email }}</strong>
                </div>

                <form id="verifyForm">
                    <div class="otp-input-group">
                        <input type="text" class="otp-input" maxlength="1" data-index="0">
                        <input type="text" class="otp-input" maxlength="1" data-index="1">
                        <input type="text" class="otp-input" maxlength="1" data-index="2">
                        <input type="text" class="otp-input" maxlength="1" data-index="3">
                        <input type="text" class="otp-input" maxlength="1" data-index="4">
                        <input type="text" class="otp-input" maxlength="1" data-index="5">
                    </div>

                    <button type="submit" class="btn btn-verify" id="verifyBtn">
                        <span id="verifySpinner" class="spinner-border spinner-border-sm d-none me-2"></span>
                        <i id="verifyIcon" class="cil-check me-2"></i>
                        Xác thực
                    </button>
                </form>

                <div class="resend-section">
                    <p class="countdown" id="countdown">Gửi lại mã sau <span id="timer">60</span> giây</p>
                    <button class="btn-resend" id="resendBtn" onclick="resendOTP()" disabled>
                        Gửi lại mã xác thực
                    </button>
                </div>

                <div class="back-link">
                    {% if type == 'registration' %}
                    <a href="/register">← Quay lại đăng ký</a>
                    {% else %}
                    <a href="/login">← Quay lại đăng nhập</a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- CoreUI JS -->
    <script src="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/js/coreui.bundle.min.js"></script>

    <script>
        const email = '{{ email }}';
        const verificationType = '{{ type }}';
        let countdownTimer = 60;
        let countdownInterval;

        // OTP input handling
        const otpInputs = document.querySelectorAll('.otp-input');
        
        otpInputs.forEach((input, index) => {
            input.addEventListener('input', function(e) {
                const value = e.target.value;
                
                // Only allow numbers
                if (!/^\d$/.test(value)) {
                    e.target.value = '';
                    return;
                }
                
                // Move to next input
                if (value && index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }
            });
            
            input.addEventListener('keydown', function(e) {
                // Handle backspace
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    otpInputs[index - 1].focus();
                }
            });
            
            input.addEventListener('paste', function(e) {
                e.preventDefault();
                const paste = e.clipboardData.getData('text');
                const digits = paste.replace(/\D/g, '').slice(0, 6);
                
                digits.split('').forEach((digit, i) => {
                    if (otpInputs[i]) {
                        otpInputs[i].value = digit;
                    }
                });
                
                if (digits.length > 0) {
                    otpInputs[Math.min(digits.length - 1, 5)].focus();
                }
            });
        });

        // Form submission
        document.getElementById('verifyForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const otp = Array.from(otpInputs).map(input => input.value).join('');
            
            if (otp.length !== 6) {
                showAlert('warning', 'Vui lòng nhập đầy đủ 6 số');
                return;
            }
            
            verifyOTP(otp);
        });

        function verifyOTP(otp) {
            const verifyBtn = document.getElementById('verifyBtn');
            const verifySpinner = document.getElementById('verifySpinner');
            const verifyIcon = document.getElementById('verifyIcon');
            
            // Disable button and show spinner
            verifyBtn.disabled = true;
            verifySpinner.classList.remove('d-none');
            verifyIcon.classList.add('d-none');
            
            fetch('/api/verify-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    code: otp,
                    type: verificationType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message || 'Xác thực thành công!');
                    setTimeout(() => {
                        window.location.href = data.redirect || '/login';
                    }, 1500);
                } else {
                    showAlert('danger', data.error || 'Mã xác thực không đúng');
                    // Clear inputs
                    otpInputs.forEach(input => input.value = '');
                    otpInputs[0].focus();
                }
            })
            .catch(error => {
                showAlert('danger', 'Có lỗi xảy ra, vui lòng thử lại');
                console.error('Verify error:', error);
            })
            .finally(() => {
                // Re-enable button and hide spinner
                verifyBtn.disabled = false;
                verifySpinner.classList.add('d-none');
                verifyIcon.classList.remove('d-none');
            });
        }

        function showAlert(type, message) {
            const alertContainer = document.getElementById('alertContainer');
            alertContainer.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        }

        function startCountdown() {
            countdownTimer = 60;
            const timerElement = document.getElementById('timer');
            const countdownElement = document.getElementById('countdown');
            const resendBtn = document.getElementById('resendBtn');
            
            resendBtn.disabled = true;
            countdownElement.style.display = 'block';
            
            countdownInterval = setInterval(() => {
                countdownTimer--;
                timerElement.textContent = countdownTimer;
                
                if (countdownTimer <= 0) {
                    clearInterval(countdownInterval);
                    countdownElement.style.display = 'none';
                    resendBtn.disabled = false;
                }
            }, 1000);
        }

        function resendOTP() {
            const resendBtn = document.getElementById('resendBtn');

            // Disable button
            resendBtn.disabled = true;
            resendBtn.textContent = 'Đang gửi...';

            showAlert('info', 'Đang gửi lại mã xác thực...');

            fetch('/api/resend-otp', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    email: email,
                    type: verificationType
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', data.message || 'Mã xác thực đã được gửi lại!');
                    // Clear current inputs
                    otpInputs.forEach(input => input.value = '');
                    otpInputs[0].focus();
                    // Start countdown
                    startCountdown();
                } else {
                    showAlert('danger', data.error || 'Không thể gửi lại mã xác thực');
                    // Re-enable button
                    resendBtn.disabled = false;
                    resendBtn.textContent = 'Gửi lại mã xác thực';
                }
            })
            .catch(error => {
                showAlert('danger', 'Có lỗi xảy ra, vui lòng thử lại');
                console.error('Resend error:', error);
                // Re-enable button
                resendBtn.disabled = false;
                resendBtn.textContent = 'Gửi lại mã xác thực';
            });
        }

        // Start countdown on page load
        document.addEventListener('DOMContentLoaded', function() {
            startCountdown();
            otpInputs[0].focus();
        });
    </script>
</body>
</html>
