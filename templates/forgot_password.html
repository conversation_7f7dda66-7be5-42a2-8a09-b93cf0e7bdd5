<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title><PERSON>u<PERSON><PERSON> mật khẩu - SAPMMO System</title>
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='images/logo.png') }}">

    <!-- CoreUI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/css/coreui.min.css">
    <!-- CoreUI Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/icons@2.1.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .forgot-container {
            width: 100%;
            max-width: 450px;
            padding: 20px;
        }

        .forgot-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .forgot-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .forgot-header {
            background: linear-gradient(135deg, #00C6AE 0%, #00A693 100%);
            padding: 30px 20px;
            text-align: center;
            color: white;
        }

        .forgot-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .forgot-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .forgot-body {
            padding: 40px 30px;
        }

        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            outline: none;
            border-color: #00C6AE;
            box-shadow: 0 0 0 3px rgba(0, 198, 174, 0.1);
            background: white;
        }

        .form-label {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .btn-forgot {
            background: linear-gradient(135deg, #00C6AE 0%, #00A693 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 12px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-forgot:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 198, 174, 0.3);
        }

        .btn-forgot:disabled {
            opacity: 0.6;
            transform: none;
            box-shadow: none;
        }

        .alert {
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .back-link {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .back-link a {
            color: #6c757d;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .back-link a:hover {
            color: #00C6AE;
        }

        .info-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
        }

        .info-box h6 {
            color: #00C6AE;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .info-box ul {
            margin: 0;
            padding-left: 20px;
        }

        .info-box li {
            margin-bottom: 5px;
            font-size: 0.9rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="forgot-container">
        <div class="forgot-card">
            <div class="forgot-header">
                <h1><i class="cil-lock-locked me-2"></i>Quên mật khẩu</h1>
                <p>Nhập email để nhận mã xác thực</p>
            </div>
            <div class="forgot-body">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    <i class="cil-warning me-2"></i>{{ error }}
                </div>
                {% endif %}

                <div class="info-box">
                    <h6><i class="cil-info me-2"></i>Hướng dẫn:</h6>
                    <ul>
                        <li>Nhập email đã đăng ký tài khoản</li>
                        <li>Chúng tôi sẽ gửi mã xác thực 6 số</li>
                        <li>Sử dụng mã để đặt lại mật khẩu mới</li>
                    </ul>
                </div>

                <form method="POST" id="forgotForm">
                    <div class="mb-4">
                        <label for="email" class="form-label">
                            <i class="cil-envelope-closed me-2"></i>Email đã đăng ký
                        </label>
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="<EMAIL>" required>
                        <div class="form-text">
                            Nhập email bạn đã sử dụng khi đăng ký tài khoản
                        </div>
                    </div>

                    <button type="submit" class="btn btn-forgot" id="submitBtn">
                        <span id="submitSpinner" class="spinner-border spinner-border-sm d-none me-2"></span>
                        <i id="submitIcon" class="cil-paper-plane me-2"></i>
                        Gửi mã xác thực
                    </button>
                </form>

                <div class="back-link">
                    <a href="/login">
                        <i class="cil-arrow-left me-1"></i>Quay lại đăng nhập
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- CoreUI JS -->
    <script src="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/js/coreui.bundle.min.js"></script>

    <script>
        document.getElementById('forgotForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            const submitSpinner = document.getElementById('submitSpinner');
            const submitIcon = document.getElementById('submitIcon');
            const email = document.getElementById('email').value;

            // Basic email validation
            if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                e.preventDefault();
                alert('Vui lòng nhập email hợp lệ');
                return;
            }

            // Show loading state
            submitBtn.disabled = true;
            submitSpinner.classList.remove('d-none');
            submitIcon.classList.add('d-none');
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Đang gửi...';
        });

        // Auto focus email input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('email').focus();
        });
    </script>
</body>
</html>
