<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>Đ<PERSON>ng ký - SAPMMO System</title>
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='images/logo.png') }}">

    <!-- CoreUI CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/css/coreui.min.css">
    <!-- CoreUI Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@coreui/icons@2.1.0/css/all.min.css">

    <style>
        body {
            background: linear-gradient(135deg, #FFDDEE 0%, #FFB6C1 50%, #FFC0CB 100%);
            background-attachment: fixed;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            position: relative;
            overflow-x: hidden;
            overflow-y: auto;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 192, 203, 0.2) 0%, transparent 50%);
            animation: float 6s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .register-container {
            width: 100%;
            max-width: 500px;
            padding: 20px;
            z-index: 1;
            margin: auto;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .register-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .register-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.15);
        }

        .register-header {
            background: linear-gradient(135deg, #00C6AE 0%, #00A693 100%);
            padding: 30px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .register-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 10px,
                rgba(255, 255, 255, 0.05) 10px,
                rgba(255, 255, 255, 0.05) 20px
            );
            animation: slide 20s linear infinite;
        }

        @keyframes slide {
            0% { transform: translateX(-50px) translateY(-50px); }
            100% { transform: translateX(50px) translateY(50px); }
        }

        .logo-container {
            position: relative;
            z-index: 2;
        }

        .logo-container img {
            height: 100px;
            width: auto;
            transition: all 0.3s ease;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
        }

        .logo-container img:hover {
            transform: scale(1.08);
            filter: drop-shadow(0 6px 16px rgba(0, 198, 174, 0.4));
        }

        .register-body {
            padding: 40px 30px;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-label {
            color: #4a5568;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 12px 16px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
        }

        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(0, 198, 174, 0.1);
            border-color: #00C6AE;
            background: white;
        }

        .form-control.is-invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
        }

        .form-control.is-valid {
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
        }

        .invalid-feedback {
            display: block;
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }

        .valid-feedback {
            display: block;
            color: #28a745;
            font-size: 14px;
            margin-top: 5px;
        }

        .btn-register {
            background: linear-gradient(135deg, #00C6AE 0%, #00A693 100%);
            border: none;
            border-radius: 12px;
            width: 100%;
            padding: 14px;
            font-weight: 600;
            font-size: 16px;
            margin-top: 20px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-register::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-register:hover::before {
            left: 100%;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 198, 174, 0.3);
        }

        .btn-register:active {
            transform: translateY(0);
        }

        .btn-back {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            border: none;
            border-radius: 12px;
            width: 100%;
            padding: 14px;
            font-weight: 600;
            font-size: 16px;
            margin-top: 10px;
            transition: all 0.3s ease;
        }

        .btn-back:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }

        .alert {
            margin-bottom: 20px;
            border-radius: 12px;
            border: none;
            backdrop-filter: blur(10px);
        }

        .alert-danger {
            background: rgba(220, 53, 69, 0.1);
            color: #721c24;
        }

        .alert-success {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
        }

        .mb-3 {
            margin-bottom: 20px;
        }

        .phone-input-group {
            display: flex;
            align-items: center;
        }

        .phone-prefix {
            background: #f8f9fa;
            border: 2px solid #e2e8f0;
            border-right: none;
            border-radius: 12px 0 0 12px;
            padding: 12px 16px;
            font-weight: 600;
            color: #6c757d;
        }

        .phone-input {
            border-radius: 0 12px 12px 0 !important;
            border-left: none !important;
        }

        .phone-input:focus {
            border-left: 2px solid #00C6AE !important;
        }

        /* Responsive */
        @media (max-width: 480px) {
            body {
                padding: 10px 0;
            }

            .register-container {
                padding: 10px;
                max-width: 95%;
            }

            .register-body {
                padding: 20px 15px;
            }

            .logo-container img {
                height: 80px;
            }

            .register-header {
                padding: 15px 10px;
            }
        }

        /* Ensure content is always visible */
        @media (max-height: 700px) {
            body {
                align-items: flex-start;
                padding: 10px 0;
            }

            .register-header {
                padding: 15px 20px;
            }

            .register-body {
                padding: 20px 30px;
            }

            .logo-container img {
                height: 70px;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-card">
            <div class="register-header">
                <div class="logo-container">
                    <img src="{{ url_for('static', filename='images/logo.png') }}" alt="MIP System">
                </div>
            </div>
            <div class="register-body">
                {% if error %}
                <div class="alert alert-danger" role="alert">
                    {{ error }}
                </div>
                {% endif %}

                {% if success %}
                <div class="alert alert-success" role="alert">
                    {{ success }}
                </div>
                {% endif %}

                <form method="POST" id="registerForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">Tên đăng nhập *</label>
                        <input type="text" class="form-control" id="username" name="username" required 
                               value="{{ request.form.username if request.form.username else '' }}"
                               pattern="^[a-zA-Z0-9_]{3,20}$" 
                               title="Tên đăng nhập phải từ 3-20 ký tự, chỉ chứa chữ cái, số và dấu gạch dưới">
                        <div class="invalid-feedback" id="username-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="email" name="email"
                               value="{{ request.form.email if request.form.email else '' }}" required>
                        <div class="invalid-feedback" id="email-feedback"></div>
                        <div class="form-text">Email sẽ được sử dụng để xác thực tài khoản</div>
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">Số điện thoại *</label>
                        <div class="phone-input-group">
                            <span class="phone-prefix">+84</span>
                            <input type="tel" class="form-control phone-input" id="phone" name="phone" required 
                                   value="{{ request.form.phone if request.form.phone else '' }}"
                                   pattern="^[0-9]{9,10}$" 
                                   placeholder="123456789"
                                   title="Số điện thoại phải có 9-10 chữ số">
                        </div>
                        <div class="invalid-feedback" id="phone-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Mật khẩu *</label>
                        <input type="password" class="form-control" id="password" name="password" required 
                               minlength="6" title="Mật khẩu phải có ít nhất 6 ký tự">
                        <div class="invalid-feedback" id="password-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Xác nhận mật khẩu *</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        <div class="invalid-feedback" id="confirm-password-feedback"></div>
                    </div>

                    <button type="submit" class="btn btn-primary btn-register">
                        <i class="cil-user-plus me-2"></i>Đăng ký
                    </button>
                    
                    <a href="/login" class="btn btn-secondary btn-back">
                        <i class="cil-arrow-left me-2"></i>Quay lại đăng nhập
                    </a>
                </form>
            </div>
        </div>
    </div>

    <!-- CoreUI JS -->
    <script src="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/js/coreui.bundle.min.js"></script>
    
    <script>
        // Form validation
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            let isValid = true;
            
            // Clear previous validation
            document.querySelectorAll('.form-control').forEach(input => {
                input.classList.remove('is-invalid', 'is-valid');
            });
            
            // Username validation
            const username = document.getElementById('username');
            if (username.value.length < 3 || username.value.length > 20 || !/^[a-zA-Z0-9_]+$/.test(username.value)) {
                username.classList.add('is-invalid');
                document.getElementById('username-feedback').textContent = 'Tên đăng nhập phải từ 3-20 ký tự, chỉ chứa chữ cái, số và dấu gạch dưới';
                isValid = false;
            } else {
                username.classList.add('is-valid');
            }
            
            // Email validation (required)
            const email = document.getElementById('email');
            if (!email.value) {
                email.classList.add('is-invalid');
                document.getElementById('email-feedback').textContent = 'Email là bắt buộc';
                isValid = false;
            } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.value)) {
                email.classList.add('is-invalid');
                document.getElementById('email-feedback').textContent = 'Email không hợp lệ';
                isValid = false;
            } else {
                email.classList.add('is-valid');
            }
            
            // Phone validation
            const phone = document.getElementById('phone');
            if (!/^[0-9]{9,10}$/.test(phone.value)) {
                phone.classList.add('is-invalid');
                document.getElementById('phone-feedback').textContent = 'Số điện thoại phải có 9-10 chữ số';
                isValid = false;
            } else {
                phone.classList.add('is-valid');
            }
            
            // Password validation
            const password = document.getElementById('password');
            if (password.value.length < 6) {
                password.classList.add('is-invalid');
                document.getElementById('password-feedback').textContent = 'Mật khẩu phải có ít nhất 6 ký tự';
                isValid = false;
            } else {
                password.classList.add('is-valid');
            }
            
            // Confirm password validation
            const confirmPassword = document.getElementById('confirm_password');
            if (confirmPassword.value !== password.value) {
                confirmPassword.classList.add('is-invalid');
                document.getElementById('confirm-password-feedback').textContent = 'Mật khẩu xác nhận không khớp';
                isValid = false;
            } else if (confirmPassword.value) {
                confirmPassword.classList.add('is-valid');
            }
            
            if (!isValid) {
                e.preventDefault();
            }
        });
        
        // Real-time validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (confirmPassword && confirmPassword !== password) {
                this.classList.add('is-invalid');
                this.classList.remove('is-valid');
                document.getElementById('confirm-password-feedback').textContent = 'Mật khẩu xác nhận không khớp';
            } else if (confirmPassword && confirmPassword === password) {
                this.classList.add('is-valid');
                this.classList.remove('is-invalid');
                document.getElementById('confirm-password-feedback').textContent = '';
            }
        });
    </script>
</body>
</html>
