{% extends "base_coreui.html" %}
{% block title %}<PERSON><PERSON><PERSON> <PERSON> h<PERSON> thống{% endblock %}

{% block head_extra %}
<style>
    .card {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        border-radius: 0.25rem;
        margin-bottom: 1.5rem;
    }

    .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        padding: 0.75rem 1.25rem;
    }

    .btn-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .btn-icon i {
        margin-right: 0.25rem;
    }

    .form-label {
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .spinner-border-sm {
        width: 1rem;
        height: 1rem;
        border-width: 0.2em;
    }

    .nav-link.disabled {
        color: #6c757d !important;
        pointer-events: none;
        cursor: default;
        opacity: 0.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <strong><PERSON><PERSON><PERSON> <PERSON><PERSON> hệ thống</strong>
    </div>
    <div class="card-body">
        <form id="configForm">
            <div class="mb-3">
                <label for="mpPerVideo" class="form-label">Số MP cần để tạo 1 video</label>
                <input type="number" class="form-control" id="mpPerVideo" name="mp_per_video" min="1" required>
                <div class="form-text">Số MP sẽ bị trừ khi người dùng tạo một video mới.</div>
            </div>

            <div class="mb-3">
                <label for="minMpBalance" class="form-label">Số MP tối thiểu cần có để tạo video</label>
                <input type="number" class="form-control" id="minMpBalance" name="min_mp_balance" min="0" required>
                <div class="form-text">Người dùng phải có ít nhất số MP này sau khi trừ MP để tạo video.</div>
            </div>

            <hr class="my-4">

            <h6 class="mb-3">Cấu hình GoLogin</h6>

            <div class="mb-3">
                <label for="gologinApiToken" class="form-label">GoLogin API Token</label>
                <input type="password" class="form-control" id="gologinApiToken" name="gologin_api_token" placeholder="Nhập GoLogin API Token">
                <div class="form-text">
                    API Token để kết nối với GoLogin. Lấy từ <a href="https://app.gologin.com/personalArea/TokenApi" target="_blank">GoLogin Dashboard</a>
                </div>
            </div>

            <div class="mb-3">
                <label for="gologinBaseUrl" class="form-label">GoLogin Base URL</label>
                <input type="text" class="form-control" id="gologinBaseUrl" name="gologin_base_url" value="https://api.gologin.com" readonly>
                <div class="form-text">URL API của GoLogin (mặc định)</div>
            </div>

            <div class="mb-3">
                <button type="button" class="btn btn-outline-info btn-icon" onclick="testGoLoginConnection()">
                    <i class="cil-wifi"></i> Test Kết Nối GoLogin
                </button>
                <span id="gologinStatus" class="ms-2"></span>
            </div>

            <hr class="my-4">

            <h6 class="mb-3">Cấu hình Email & Xác thực</h6>

            <div class="mb-3">
                <label for="mailtrapApiToken" class="form-label">Mailtrap API Token</label>
                <input type="password" class="form-control" id="mailtrapApiToken" name="mailtrap_api_token" placeholder="Nhập Mailtrap API Token">
                <div class="form-text">
                    API Token để gửi email qua Mailtrap. Lấy từ <a href="https://mailtrap.io/api-tokens" target="_blank">Mailtrap Dashboard</a>
                </div>
            </div>

            <div class="mb-3">
                <label for="mailtrapSenderEmail" class="form-label">Email gửi</label>
                <input type="email" class="form-control" id="mailtrapSenderEmail" name="mailtrap_sender_email" placeholder="<EMAIL>">
                <div class="form-text">Địa chỉ email sẽ hiển thị là người gửi</div>
            </div>

            <div class="mb-3">
                <label for="mailtrapSenderName" class="form-label">Tên người gửi</label>
                <input type="text" class="form-control" id="mailtrapSenderName" name="mailtrap_sender_name" placeholder="MIP System">
                <div class="form-text">Tên hiển thị của người gửi email</div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="otpExpiryMinutes" class="form-label">Thời gian hết hạn OTP (phút)</label>
                        <input type="number" class="form-control" id="otpExpiryMinutes" name="otp_expiry_minutes" min="5" max="60" value="15">
                        <div class="form-text">Mã OTP sẽ hết hạn sau thời gian này</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="maxOtpAttempts" class="form-label">Số lần thử OTP tối đa</label>
                        <input type="number" class="form-control" id="maxOtpAttempts" name="max_otp_attempts" min="1" max="10" value="3">
                        <div class="form-text">Số lần nhập sai OTP tối đa</div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="emailVerificationEnabled" name="email_verification_enabled">
                    <label class="form-check-label" for="emailVerificationEnabled">
                        Bật xác thực email khi đăng ký
                    </label>
                </div>
                <div class="form-text">Yêu cầu người dùng xác thực email trước khi kích hoạt tài khoản</div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="testEmailAddress" class="form-label">Email nhận test</label>
                        <input type="email" class="form-control" id="testEmailAddress" placeholder="<EMAIL>">
                        <div class="form-text">Email sẽ nhận thư test</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="button" class="btn btn-outline-info btn-icon" onclick="testEmailConnection()">
                                <i class="cil-envelope-closed"></i> Test Gửi Email
                            </button>
                            <span id="emailStatus" class="ms-2"></span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <button type="button" class="btn btn-success btn-icon" onclick="saveEmailConfig()">
                    <span id="emailSaveSpinner" class="spinner-border spinner-border-sm d-none me-2"></span>
                    <i id="emailSaveIcon" class="cil-save me-2"></i>
                    Lưu Cấu hình Email
                </button>
            </div>

            <hr class="my-4">

            <h6 class="mb-3">Cấu hình Hiển thị Giá (Coffee Cup)</h6>

            <div class="mb-3">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="enableCoffeeDisplay" name="enable_coffee_display">
                    <label class="form-check-label" for="enableCoffeeDisplay">
                        Bật hiển thị giá bằng ly cafe
                    </label>
                </div>
                <div class="form-text">Khi bật, giá sẽ hiển thị dưới dạng số ly cafe thay vì MP</div>
            </div>

            <div class="mb-3">
                <label for="coffeeCupValue" class="form-label">Giá trị 1 ly cafe (MP)</label>
                <input type="number" class="form-control" id="coffeeCupValue" name="coffee_cup_value" min="1000" step="1000" required>
                <div class="form-text">Ví dụ: 50000 = 1 ly cafe = 50,000 MP</div>
            </div>

            <!-- Icon Size Settings -->
            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="coffeeIconWidth" class="form-label">Chiều rộng icon (px)</label>
                    <input type="number" class="form-control" id="coffeeIconWidth" min="10" max="50" value="20" onchange="previewCoffeeIcon()">
                    <div class="form-text">Chiều rộng của icon ly cafe (10-50px)</div>
                </div>
                <div class="col-md-6">
                    <label for="coffeeIconHeight" class="form-label">Chiều cao icon (px)</label>
                    <input type="number" class="form-control" id="coffeeIconHeight" min="10" max="50" value="20" onchange="previewCoffeeIcon()">
                    <div class="form-text">Chiều cao của icon ly cafe (10-50px)</div>
                </div>
            </div>

            <div class="mb-3">
                <label class="form-label">Icon ly cafe</label>

                <!-- Icon type selection -->
                <div class="mb-3">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="coffeeIconType" id="iconTypeEmoji" value="emoji" checked onchange="toggleIconType()">
                        <label class="form-check-label" for="iconTypeEmoji">
                            <i class="cil-smile"></i> Sử dụng Emoji
                        </label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="coffeeIconType" id="iconTypeImage" value="image" onchange="toggleIconType()">
                        <label class="form-check-label" for="iconTypeImage">
                            <i class="cil-image"></i> Sử dụng Hình Ảnh
                        </label>
                    </div>
                </div>

                <!-- Tab navigation -->
                <ul class="nav nav-tabs" id="iconTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="emoji-tab" data-coreui-toggle="tab" data-coreui-target="#emoji-pane" type="button" role="tab">
                            <i class="cil-smile"></i> Emoji
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="image-tab" data-coreui-toggle="tab" data-coreui-target="#image-pane" type="button" role="tab">
                            <i class="cil-image"></i> Upload Hình Ảnh
                        </button>
                    </li>
                </ul>

                <!-- Tab content -->
                <div class="tab-content border border-top-0 p-3" id="iconTabContent">
                    <!-- Emoji tab -->
                    <div class="tab-pane fade show active" id="emoji-pane" role="tabpanel">
                        <div class="input-group">
                            <input type="text" class="form-control" id="coffeeCupIcon" name="coffee_cup_icon" placeholder="☕" value="☕">
                            <button type="button" class="btn btn-outline-secondary" onclick="previewCoffeeIcon()">
                                <i class="cil-magnifying-glass"></i> Preview
                            </button>
                        </div>
                        <div class="form-text">
                            Có thể sử dụng emoji: ☕ 🍵 ☕️ 🥤 🧋 🍺 🍷 🥃
                        </div>
                    </div>

                    <!-- Image upload tab -->
                    <div class="tab-pane fade" id="image-pane" role="tabpanel">
                        <div class="mb-3">
                            <input type="file" class="form-control" id="coffeeIconFile" accept="image/png,image/jpg,image/jpeg,image/gif" onchange="handleIconUpload(this)">
                            <div class="form-text">
                                Upload hình ảnh PNG, JPG, GIF (tối đa 2MB). Kích thước khuyến nghị: 32x32px hoặc 64x64px
                            </div>
                        </div>
                        <div id="uploadProgress" class="progress d-none mb-3">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="uploadedImagePreview" class="text-center d-none">
                            <img id="uploadedImage" src="" alt="Coffee Icon" style="max-width: 64px; max-height: 64px;">
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeUploadedIcon()">
                                    <i class="cil-trash"></i> Xóa
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-text mt-2">
                    <strong>Preview:</strong> <span id="iconPreview" class="ms-2"></span>
                </div>

                <!-- Hidden field to store the final icon value (emoji or image path) -->
                <input type="hidden" id="finalCoffeeIcon" name="coffee_cup_icon_final">
            </div>

            <div class="mb-3">
                <div class="alert alert-info">
                    <strong>Ví dụ:</strong> Nếu cấu hình 1 ly = 50,000 MP, thì giá 500,000 MP sẽ hiển thị là "10 ☕"
                </div>
            </div>

            <button type="submit" class="btn btn-primary btn-icon" id="saveBtn">
                <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="saveSpinner"></span>
                <i class="cil-save" id="saveIcon"></i>
                <span>Lưu cấu hình</span>
            </button>
        </form>
    </div>
</div>

<!-- Export Database Card -->
<div class="card">
    <div class="card-header">
        <strong>Export Database</strong>
    </div>
    <div class="card-body">
        <p class="text-muted">Export toàn bộ database hiện tại thành file SQL để import lên server khác.</p>
        <div class="alert alert-warning" role="alert">
            <i class="cil-warning"></i>
            <strong>Lưu ý:</strong> File export sẽ chứa tất cả dữ liệu bao gồm cấu trúc bảng và dữ liệu. Chỉ admin mới có quyền thực hiện chức năng này.
        </div>

        <button type="button" class="btn btn-success btn-icon" id="exportBtn" onclick="exportDatabase()">
            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true" id="exportSpinner"></span>
            <i class="cil-cloud-download" id="exportIcon"></i>
            <span>Export Database</span>
        </button>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load current configuration
    fetch('/api/config')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                document.getElementById('mpPerVideo').value = data.config.mp_per_video;
                document.getElementById('minMpBalance').value = data.config.min_mp_balance;
                document.getElementById('gologinApiToken').value = data.config.gologin_api_token || '';
                document.getElementById('gologinBaseUrl').value = data.config.gologin_base_url || 'https://api.gologin.com';

                // Load email configs
                document.getElementById('mailtrapApiToken').value = data.config.mailtrap_api_token || '';
                document.getElementById('mailtrapSenderEmail').value = data.config.mailtrap_sender_email || '';
                document.getElementById('mailtrapSenderName').value = data.config.mailtrap_sender_name || 'MIP System';
                document.getElementById('otpExpiryMinutes').value = data.config.otp_expiry_minutes || '15';
                document.getElementById('maxOtpAttempts').value = data.config.max_otp_attempts || '3';
                document.getElementById('emailVerificationEnabled').checked = data.config.email_verification_enabled === 'true';

                // Load coffee cup configs
                document.getElementById('enableCoffeeDisplay').checked = data.config.enable_coffee_display === 'true';
                document.getElementById('coffeeCupValue').value = data.config.coffee_cup_value || '50000';
                document.getElementById('coffeeIconWidth').value = data.config.coffee_icon_width || '20';
                document.getElementById('coffeeIconHeight').value = data.config.coffee_icon_height || '20';

                const coffeeIcon = data.config.coffee_cup_icon || '☕';
                const iconType = data.config.coffee_icon_type || 'emoji';

                // Set icon type radio button
                if (iconType === 'image') {
                    document.getElementById('iconTypeImage').checked = true;
                    document.getElementById('image-tab').click();

                    if (coffeeIcon.startsWith('/static/')) {
                        document.getElementById('uploadedImage').src = coffeeIcon;
                        document.getElementById('uploadedImagePreview').classList.remove('d-none');
                    }
                } else {
                    document.getElementById('iconTypeEmoji').checked = true;
                    document.getElementById('coffeeCupIcon').value = coffeeIcon;
                }

                toggleIconType();

                // Preview icon
                setTimeout(previewCoffeeIcon, 200);
            } else {
                showAlert('danger', 'Không thể tải cấu hình: ' + (data.message || 'Lỗi không xác định'));
            }
        })
        .catch(error => {
            showAlert('danger', 'Lỗi khi tải cấu hình: ' + error.message);
        });

    // Handle form submission
    document.getElementById('configForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const saveBtn = document.getElementById('saveBtn');
        const saveSpinner = document.getElementById('saveSpinner');
        const saveIcon = document.getElementById('saveIcon');

        // Disable button and show spinner
        saveBtn.disabled = true;
        saveSpinner.classList.remove('d-none');
        saveIcon.classList.add('d-none');

        const formData = {
            mp_per_video: document.getElementById('mpPerVideo').value,
            min_mp_balance: document.getElementById('minMpBalance').value,
            gologin_api_token: document.getElementById('gologinApiToken').value,
            gologin_base_url: document.getElementById('gologinBaseUrl').value,
            // Coffee configs
            enable_coffee_display: document.getElementById('enableCoffeeDisplay').checked ? 'true' : 'false',
            coffee_cup_value: document.getElementById('coffeeCupValue').value,
            coffee_icon_type: document.querySelector('input[name="coffeeIconType"]:checked').value,
            coffee_icon_width: document.getElementById('coffeeIconWidth').value,
            coffee_icon_height: document.getElementById('coffeeIconHeight').value,
            coffee_cup_icon: getFinalCoffeeIconValue()
        };

        fetch('/api/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            // Enable button and hide spinner
            saveBtn.disabled = false;
            saveSpinner.classList.add('d-none');
            saveIcon.classList.remove('d-none');

            if (data.status === 'success') {
                showAlert('success', 'Đã cập nhật cấu hình thành công!');
            } else {
                showAlert('danger', 'Có lỗi xảy ra: ' + (data.message || 'Lỗi không xác định'));
            }
        })
        .catch(error => {
            // Enable button and hide spinner
            saveBtn.disabled = false;
            saveSpinner.classList.add('d-none');
            saveIcon.classList.remove('d-none');

            showAlert('danger', 'Lỗi khi lưu cấu hình: ' + error.message);
        });
    });

    // Function to show alert
    function showAlert(type, message) {
        // Remove any existing alerts
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // Create new alert
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show mt-3`;
        alertDiv.role = 'alert';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-coreui-dismiss="alert" aria-label="Close"></button>
        `;

        // Insert alert before the form
        const form = document.getElementById('configForm');
        form.parentNode.insertBefore(alertDiv, form);

        // Auto dismiss after 5 seconds
        setTimeout(() => {
            alertDiv.classList.remove('show');
            setTimeout(() => alertDiv.remove(), 150);
        }, 5000);
    }
});

// Function to export database
function exportDatabase() {
    const exportBtn = document.getElementById('exportBtn');
    const exportSpinner = document.getElementById('exportSpinner');
    const exportIcon = document.getElementById('exportIcon');

    // Confirm action
    if (!confirm('Bạn có chắc chắn muốn export toàn bộ database? Quá trình này có thể mất vài phút.')) {
        return;
    }

    // Disable button and show spinner
    exportBtn.disabled = true;
    exportSpinner.classList.remove('d-none');
    exportIcon.classList.add('d-none');

    // Create a form to submit POST request
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/api/export_database';
    form.style.display = 'none';

    // Add CSRF token if needed
    const csrfToken = document.querySelector('meta[name="csrf-token"]');
    if (csrfToken) {
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = csrfToken.getAttribute('content');
        form.appendChild(csrfInput);
    }

    document.body.appendChild(form);

    // Submit form
    form.submit();

    // Re-enable button after a delay (since we're downloading a file)
    setTimeout(() => {
        exportBtn.disabled = false;
        exportSpinner.classList.add('d-none');
        exportIcon.classList.remove('d-none');

        // Remove the form
        document.body.removeChild(form);

        // Show success message
        showAlert('success', 'Database export đã được tạo và tải xuống thành công!');
    }, 3000);
}

// Function to show alert (reuse from above)
function showAlert(type, message) {
    // Remove any existing alerts
    const existingAlerts = document.querySelectorAll('.alert:not(.alert-warning)');
    existingAlerts.forEach(alert => alert.remove());

    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show mt-3`;
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-coreui-dismiss="alert" aria-label="Close"></button>
    `;

    // Insert alert at the top of the page
    const firstCard = document.querySelector('.card');
    firstCard.parentNode.insertBefore(alertDiv, firstCard);

    // Auto dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => alertDiv.remove(), 150);
    }, 5000);
}

// Function to save email configuration
function saveEmailConfig() {
    const saveBtn = document.querySelector('button[onclick="saveEmailConfig()"]');
    const saveSpinner = document.getElementById('emailSaveSpinner');
    const saveIcon = document.getElementById('emailSaveIcon');

    // Disable button and show spinner
    saveBtn.disabled = true;
    saveSpinner.classList.remove('d-none');
    saveIcon.classList.add('d-none');

    const emailFormData = {
        mailtrap_api_token: document.getElementById('mailtrapApiToken').value,
        mailtrap_sender_email: document.getElementById('mailtrapSenderEmail').value,
        mailtrap_sender_name: document.getElementById('mailtrapSenderName').value,
        otp_expiry_minutes: document.getElementById('otpExpiryMinutes').value,
        max_otp_attempts: document.getElementById('maxOtpAttempts').value,
        email_verification_enabled: document.getElementById('emailVerificationEnabled').checked ? 'true' : 'false'
    };

    fetch('/api/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(emailFormData)
    })
    .then(response => response.json())
    .then(data => {
        // Re-enable button and hide spinner
        saveBtn.disabled = false;
        saveSpinner.classList.add('d-none');
        saveIcon.classList.remove('d-none');

        if (data.status === 'success') {
            showAlert('success', 'Đã lưu cấu hình email thành công!');
        } else {
            showAlert('danger', 'Có lỗi xảy ra: ' + data.message);
        }
    })
    .catch(error => {
        // Re-enable button and hide spinner
        saveBtn.disabled = false;
        saveSpinner.classList.add('d-none');
        saveIcon.classList.remove('d-none');

        showAlert('danger', 'Có lỗi xảy ra khi lưu cấu hình email');
        console.error('Save email config error:', error);
    });
}

// Function to test email connection
function testEmailConnection() {
    const apiToken = document.getElementById('mailtrapApiToken').value;
    const senderEmail = document.getElementById('mailtrapSenderEmail').value;
    const testEmail = document.getElementById('testEmailAddress').value;
    const statusSpan = document.getElementById('emailStatus');

    if (!apiToken || !senderEmail || !testEmail) {
        statusSpan.innerHTML = '<span class="badge bg-warning">Vui lòng nhập đầy đủ thông tin</span>';
        return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(testEmail)) {
        statusSpan.innerHTML = '<span class="badge bg-warning">Email nhận không hợp lệ</span>';
        return;
    }

    statusSpan.innerHTML = '<span class="badge bg-info"><i class="spinner-border spinner-border-sm me-1"></i>Đang gửi test email...</span>';

    // Test email by sending a test email
    fetch('/api/test-email', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            api_token: apiToken,
            sender_email: senderEmail,
            sender_name: document.getElementById('mailtrapSenderName').value || 'MIP System',
            test_email: testEmail
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusSpan.innerHTML = '<span class="badge bg-success"><i class="cil-check me-1"></i>Email test đã gửi thành công!</span>';
        } else {
            statusSpan.innerHTML = '<span class="badge bg-danger"><i class="cil-x me-1"></i>Lỗi: ' + (data.error || 'Không thể gửi email') + '</span>';
        }
    })
    .catch(error => {
        statusSpan.innerHTML = '<span class="badge bg-danger"><i class="cil-x me-1"></i>Lỗi kết nối</span>';
        console.error('Email test error:', error);
    });
}

// Function to test GoLogin connection
function testGoLoginConnection() {
    const apiToken = document.getElementById('gologinApiToken').value;
    const statusSpan = document.getElementById('gologinStatus');

    if (!apiToken) {
        statusSpan.innerHTML = '<span class="badge bg-warning">Vui lòng nhập API Token</span>';
        return;
    }

    statusSpan.innerHTML = '<span class="badge bg-info"><i class="spinner-border spinner-border-sm me-1"></i>Đang kiểm tra...</span>';

    fetch('/api/gologin/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            api_token: apiToken
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusSpan.innerHTML = '<span class="badge bg-success"><i class="cil-check me-1"></i>Kết nối thành công</span>';
        } else {
            statusSpan.innerHTML = '<span class="badge bg-danger"><i class="cil-x me-1"></i>Kết nối thất bại: ' + (data.error || 'Unknown error') + '</span>';
        }
    })
    .catch(error => {
        statusSpan.innerHTML = '<span class="badge bg-danger"><i class="cil-x me-1"></i>Lỗi: ' + error.message + '</span>';
    });
}

function toggleIconType() {
    const iconType = document.querySelector('input[name="coffeeIconType"]:checked').value;
    const emojiTab = document.getElementById('emoji-tab');
    const imageTab = document.getElementById('image-tab');
    const emojiPane = document.getElementById('emoji-pane');
    const imagePane = document.getElementById('image-pane');

    if (iconType === 'emoji') {
        // Enable emoji tab, disable image tab
        emojiTab.classList.remove('disabled');
        imageTab.classList.add('disabled');
        emojiTab.click();
    } else {
        // Enable image tab, disable emoji tab
        imageTab.classList.remove('disabled');
        emojiTab.classList.add('disabled');
        imageTab.click();
    }

    previewCoffeeIcon();
}

function getFinalCoffeeIconValue() {
    const iconType = document.querySelector('input[name="coffeeIconType"]:checked').value;

    if (iconType === 'emoji') {
        return document.getElementById('coffeeCupIcon').value || '☕';
    } else {
        const uploadedImage = document.getElementById('uploadedImage');
        return uploadedImage.src || '';
    }
}

function previewCoffeeIcon() {
    const iconType = document.querySelector('input[name="coffeeIconType"]:checked').value;
    const preview = document.getElementById('iconPreview');

    // Clear preview first
    preview.innerHTML = '';

    if (iconType === 'emoji') {
        const icon = document.getElementById('coffeeCupIcon').value;
        if (icon) {
            const span = document.createElement('span');
            span.style.fontSize = '1.5em';
            span.textContent = icon;
            preview.appendChild(span);
        }
    } else {
        const uploadedImage = document.getElementById('uploadedImage');
        if (uploadedImage.src && uploadedImage.src !== window.location.href) {
            const img = document.createElement('img');
            img.src = uploadedImage.src;

            // Use width/height from config
            const width = document.getElementById('coffeeIconWidth').value || '20';
            const height = document.getElementById('coffeeIconHeight').value || '20';
            img.style.width = width + 'px';
            img.style.height = height + 'px';
            img.alt = 'coffee preview';
            preview.appendChild(img);
        }
    }
}

function handleIconUpload(input) {
    const file = input.files[0];
    if (!file) return;

    // Validate file size (2MB max)
    if (file.size > 2 * 1024 * 1024) {
        alert('File quá lớn! Vui lòng chọn file dưới 2MB.');
        input.value = '';
        return;
    }

    // Validate file type
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
        alert('Chỉ hỗ trợ file PNG, JPG, JPEG, GIF!');
        input.value = '';
        return;
    }

    // Show progress
    const progressContainer = document.getElementById('uploadProgress');
    const progressBar = progressContainer.querySelector('.progress-bar');
    progressContainer.classList.remove('d-none');

    // Create FormData
    const formData = new FormData();
    formData.append('coffee_icon', file);

    // Upload file
    fetch('/api/upload/coffee-icon', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        progressContainer.classList.add('d-none');

        if (data.success) {
            // Show uploaded image
            const uploadedImage = document.getElementById('uploadedImage');
            const previewContainer = document.getElementById('uploadedImagePreview');

            uploadedImage.src = data.file_path;
            previewContainer.classList.remove('d-none');

            // Update preview
            previewCoffeeIcon();

            alert('Upload thành công!');
        } else {
            alert('Lỗi upload: ' + data.error);
        }
    })
    .catch(error => {
        progressContainer.classList.add('d-none');
        alert('Lỗi upload: ' + error.message);
    });
}

function removeUploadedIcon() {
    const uploadedImage = document.getElementById('uploadedImage');
    const previewContainer = document.getElementById('uploadedImagePreview');
    const fileInput = document.getElementById('coffeeIconFile');

    uploadedImage.src = '';
    previewContainer.classList.add('d-none');
    fileInput.value = '';

    // Clear preview
    document.getElementById('iconPreview').innerHTML = '';
    document.getElementById('finalCoffeeIcon').value = '';
}

// Auto preview when typing or changing tabs
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('coffeeCupIcon').addEventListener('input', previewCoffeeIcon);

    // Tab change event
    document.querySelectorAll('#iconTabs .nav-link').forEach(tab => {
        tab.addEventListener('click', function() {
            setTimeout(previewCoffeeIcon, 100); // Small delay to ensure tab is active
        });
    });
});
</script>
{% endblock %}
