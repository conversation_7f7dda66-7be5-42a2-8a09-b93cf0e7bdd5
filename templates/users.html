{% extends "base.html" %}
{% block title %}Quản lý người dùng{% endblock %}
{% block content %}
<div class="card mb-4">
    <div class="card-body">
        <h5 class="card-title">Thêm người dùng</h5>
        <form method="POST" action="/add_user">
            <div class="mb-3"><input type="text" class="form-control" name="username" placeholder="Tên đăng nhập" required
                               pattern="^[a-zA-Z0-9_]{3,20}$"
                               title="Tên đăng nhập phải từ 3-20 ký tự, chỉ chứa chữ cái, số và dấu gạch dưới"></div>
            <div class="mb-3"><input type="password" class="form-control" name="password" placeholder="Mật khẩu" required></div>
            <div class="mb-3">
                <select class="form-select" name="role" required>
                    <option value="admin">Admin</option>
                    <option value="leader"><PERSON><PERSON><PERSON> trưởng</option>
                    <option value="member">Nhân viên</option>
                </select>
            </div>
            <div class="mb-3">
                <select class="form-select" name="team_id">
                    <option value="">Chọn đội (không bắt buộc)</option>
                    {% for team in teams %}
                    <option value="{{ team[0] }}">{{ team[1] }}</option>
                    {% endfor %}
                </select>
            </div>
            <button type="submit" class="btn btn-primary">Thêm</button>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <h5 class="card-title">Danh sách người dùng</h5>
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Tên đăng nhập</th>
                    <th>Vai trò</th>
                    <th>Đội</th>
                    <th>Mã Unit</th>
                    <th>Số MP</th>
                    <th>Số video</th>
                    <th>Hành động</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td>{{ user[0] }}</td>
                    <td>{{ user[1] }}</td>
                    <td>{{ user[3] }}</td>
                    <td>{{ teams|selectattr('0', 'equalto', user[4] or 0)|map(attribute='1')|first or '' }}</td>
                    <td>{{ user[5] or 'Chưa có' }}</td>
                    <td>{{ user[6] }}</td>
                    <td>{{ user[7] }}</td>
                    <td>
                        <button class="btn btn-warning btn-sm" onclick="editUser('{{ user[0] }}')">Edit</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteUser('{{ user[0] }}')">Delete</button>
                        {% if user_role == 'admin' %}
                        <button class="btn btn-info btn-sm" onclick="showAddMPModal('{{ user[0] }}')">Nạp MP</button>
                        <button class="btn btn-secondary btn-sm" onclick="showHistoryModal('{{ user[0] }}')">Lịch sử MP</button>
                        <button class="btn btn-warning btn-sm" onclick="showVideosModal('{{ user[0] }}')">Videos</button>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chỉnh sửa người dùng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm" method="POST" action="/edit_user">
                    <input type="hidden" name="user_id" id="edit_user_id">
                    <div class="mb-3">
                        <label class="form-label">Mã Unit</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="edit_unit_code" readonly>
                            <button type="button" class="btn btn-outline-secondary" onclick="generateUnitCode()">Generate</button>
                        </div>
                    </div>
                    <div class="mb-3"><input type="text" class="form-control" name="username" id="edit_username" required
                               pattern="^[a-zA-Z0-9_]{3,20}$"
                               title="Tên đăng nhập phải từ 3-20 ký tự, chỉ chứa chữ cái, số và dấu gạch dưới"></div>
                    <div class="mb-3"><input type="password" class="form-control" name="password" id="edit_password" placeholder="Để trống nếu không đổi"></div>
                    <div class="mb-3">
                        <select class="form-select" name="role" id="edit_role" required>
                            <option value="admin">Admin</option>
                            <option value="leader">Đội trưởng</option>
                            <option value="member">Nhân viên</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <select class="form-select" name="team_id" id="edit_team_id">
                            <option value="">Chọn đội (không bắt buộc)</option>
                            {% for team in teams %}
                            <option value="{{ team[0] }}">{{ team[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Lưu</button>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="addMPModal" tabindex="-1" aria-labelledby="addMPModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMPModalLabel">Nạp MP</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addMPForm">
                    <input type="hidden" id="mp_user_id">
                    <div class="mb-3">
                        <label for="mp_amount" class="form-label">Số MP</label>
                        <input type="number" class="form-control" id="mp_amount" required min="1">
                    </div>
                    <div class="mb-3">
                        <label for="mp_description" class="form-label">Ghi chú</label>
                        <textarea class="form-control" id="mp_description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="submitAddMP()">Nạp MP</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="mpHistoryModal" tabindex="-1" aria-labelledby="mpHistoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mpHistoryModalLabel">Lịch sử MP</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Thời gian</th>
                                <th>Số MP</th>
                                <th>Loại</th>
                                <th>Ghi chú</th>
                                <th>Người thực hiện</th>
                            </tr>
                        </thead>
                        <tbody id="mpHistoryBody">
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // ... existing code ...
});

function showAddMPModal(userId) {
    $('#mp_user_id').val(userId);
    $('#mp_amount').val('');
    $('#mp_description').val('');
    $('#addMPModal').modal('show');
}

function submitAddMP() {
    const userId = $('#mp_user_id').val();
    const amount = $('#mp_amount').val();
    const description = $('#mp_description').val();
    
    if (!amount) {
        alert('Vui lòng nhập số MP');
        return;
    }
    
    // Disable nút submit và hiển thị loading
    const submitBtn = $('.modal-footer .btn-primary');
    const originalText = submitBtn.text();
    submitBtn.prop('disabled', true);
    submitBtn.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...');
    
    fetch('/api/user/mp/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            user_id: userId,
            amount: parseInt(amount),
            description: description
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }
        alert('Nạp MP thành công');
        $('#addMPModal').modal('hide');
        location.reload();
    })
    .catch(error => {
        alert('Có lỗi xảy ra: ' + error.message);
    })
    .finally(() => {
        // Restore nút submit
        submitBtn.prop('disabled', false);
        submitBtn.html(originalText);
    });
}

function showHistoryModal(userId) {
    $.ajax({
        url: '/api/user/mp/history/' + userId,
        method: 'GET',
        success: function(response) {
            const tbody = $('#mpHistoryBody');
            tbody.empty();
            
            if (response.length === 0) {
                tbody.append('<tr><td colspan="5" class="text-center">Không có lịch sử giao dịch</td></tr>');
            } else {
                response.forEach(function(t) {
                    tbody.append(`
                        <tr>
                            <td>${new Date(t.created_at).toLocaleString()}</td>
                            <td>${t.amount}</td>
                            <td>${t.type === 'ADD' ? 'Nạp' : 'Tiêu'}</td>
                            <td>${t.description || ''}</td>
                            <td>${t.created_by_name}</td>
                        </tr>
                    `);
                });
            }
            
            $('#mpHistoryModal').modal('show');
        },
        error: function(xhr) {
            alert('Có lỗi xảy ra: ' + (xhr.responseJSON?.error || 'Không thể tải lịch sử'));
        }
    });
}

function showVideosModal(userId) {
    $.ajax({
        url: '/api/user/videos/' + userId,
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token')
        },
        success: function(response) {
            const tbody = $('#videosTableBody');
            tbody.empty();
            
            if (response.length === 0) {
                tbody.append('<tr><td colspan="3" class="text-center">Không có video nào</td></tr>');
            } else {
                response.forEach(function(v) {
                    tbody.append(`
                        <tr>
                            <td>${new Date(v.created_at).toLocaleString()}</td>
                            <td>${v.video_type}</td>
                            <td>${v.mp_cost}</td>
                        </tr>
                    `);
                });
            }
            
            $('#videosModal').modal('show');
        },
        error: function(xhr) {
            alert('Có lỗi xảy ra: ' + (xhr.responseJSON?.error || 'Không thể tải danh sách video'));
        }
    });
}

// Cập nhật hàm renderUsers để thêm các nút mới
function renderUsers(users) {
    const tbody = $('#usersTableBody');
    tbody.empty();
    
    users.forEach(function(user) {
        const actions = `
            <button class="btn btn-sm btn-primary" onclick="showEditUserModal(${user.user_id})">
                <i class="fas fa-edit"></i>
            </button>
            <button class="btn btn-sm btn-success" onclick="showAddMPModal(${user.user_id})">
                <i class="fas fa-plus"></i> MP
            </button>
            <button class="btn btn-sm btn-info" onclick="showHistoryModal(${user.user_id})">
                <i class="fas fa-history"></i>
            </button>
            <button class="btn btn-sm btn-warning" onclick="showVideosModal(${user.user_id})">
                <i class="fas fa-video"></i>
            </button>
        `;
        
        tbody.append(`
            <tr>
                <td>${user.username}</td>
                <td>${user.unit_code}</td>
                <td>${user.role}</td>
                <td>${user.team_name || ''}</td>
                <td>${user.mp_balance}</td>
                <td>${user.total_videos}</td>
                <td>${actions}</td>
            </tr>
        `);
    });
}

function editUser(userId) {
    fetch(`/get_user/${userId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('edit_user_id').value = data.user_id;
            document.getElementById('edit_username').value = data.username;
            document.getElementById('edit_role').value = data.role;
            document.getElementById('edit_team_id').value = data.team_id || '';
            document.getElementById('edit_unit_code').value = data.unit_code || '';
            new bootstrap.Modal(document.getElementById('editUserModal')).show();
        });
}

function generateUnitCode() {
    const userId = document.getElementById('edit_user_id').value;
    const form = document.getElementById('editUserForm');
    const input = document.createElement('input');
    input.type = 'hidden';
    input.name = 'generate_unit_code';
    input.value = '1';
    form.appendChild(input);
    form.submit();
}

function deleteUser(userId) {
    if (confirm('Bạn có chắc muốn xóa người dùng này không?')) {
        fetch(`/delete_user/${userId}`, { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    alert(data.message);
                    location.reload();
                }
            })
            .catch(error => {
                alert("Lỗi: " + error);
            });
    }
}
</script>
{% endblock %}