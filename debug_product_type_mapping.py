#!/usr/bin/env python3
"""
Debug product type mapping issues
"""

import psycopg2
import psycopg2.extras
from db_config import PG_CONFIG

def get_db_connection():
    """Kết nối database PostgreSQL từ config"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def debug_product_types():
    """Debug product types và mapping"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🔍 Debugging Product Type Mapping Issues")
        print("=" * 60)
        
        # 1. Check ProductTypes table
        print("📋 1. ProductTypes Table:")
        cursor.execute('''
            SELECT type_id, name, description, is_active, is_default
            FROM "ProductTypes"
            ORDER BY type_id
        ''')
        
        product_types = cursor.fetchall()
        print(f"{'ID':<5} {'Name':<20} {'Description':<30} {'Active':<8} {'Default'}")
        print("-" * 80)
        
        for pt in product_types:
            print(f"{pt['type_id']:<5} {pt['name']:<20} {(pt['description'] or '')[:30]:<30} {pt['is_active']:<8} {pt['is_default']}")
        
        # 2. Check CategoryProductTypes mapping
        print(f"\n📋 2. Category-ProductType Mapping:")
        cursor.execute('''
            SELECT c.name as category_name, pt.name as product_type_name, cpt.category_id, cpt.type_id
            FROM "CategoryProductTypes" cpt
            JOIN "ProductCategories" c ON cpt.category_id = c.category_id
            JOIN "ProductTypes" pt ON cpt.type_id = pt.type_id
            ORDER BY c.name, pt.name
        ''')
        
        mappings = cursor.fetchall()
        print(f"{'Category':<20} {'Product Type':<20} {'Cat ID':<8} {'Type ID'}")
        print("-" * 60)
        
        for mapping in mappings:
            print(f"{mapping['category_name']:<20} {mapping['product_type_name']:<20} {mapping['category_id']:<8} {mapping['type_id']}")
        
        # 3. Check existing products
        print(f"\n📋 3. Existing Products:")
        cursor.execute('''
            SELECT p.product_id, p.name, p.product_type, p.product_type_id, pt.name as type_name
            FROM "Products" p
            LEFT JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
            WHERE p.is_deleted = false OR p.is_deleted IS NULL
            ORDER BY p.product_id DESC
            LIMIT 10
        ''')
        
        products = cursor.fetchall()
        print(f"{'ID':<5} {'Product Name':<25} {'product_type':<15} {'type_id':<8} {'Type Name'}")
        print("-" * 80)
        
        for product in products:
            print(f"{product['product_id']:<5} {(product['name'] or '')[:25]:<25} {(product['product_type'] or ''):<15} {product['product_type_id'] or 'NULL':<8} {product['type_name'] or 'NULL'}")
        
        # 4. Test type mapping logic
        print(f"\n📋 4. Testing Type Mapping Logic:")
        
        type_mapping = {
            'Account': 'account',
            'Sản Phẩm Win': 'win_product',
            'Win Product': 'win_product',
            'Course Basic': 'course',
            'Course Advanced': 'course',
            'TikTok Formula': 'win_product',
            'AI Video Formula': 'videos',
            'AFF Package': 'aff_package',
            'TikTok Playbook': 'win_product',
            'Digital Product': 'win_product',
            'E-book': 'win_product',
            'Template': 'win_product',
            'Tool': 'win_product',
            'Video': 'videos',
            'videos': 'videos',
            'Videos': 'videos'
        }
        
        print(f"{'Type Name':<20} {'Mapped To':<15} {'Expected'}")
        print("-" * 50)
        
        for pt in product_types:
            type_name = pt['name']
            mapped_type = type_mapping.get(type_name)
            
            # Apply default logic
            if not mapped_type:
                if type_name.lower() in ['account', 'tài khoản']:
                    mapped_type = 'account'
                elif type_name.lower() in ['video', 'videos']:
                    mapped_type = 'videos'
                else:
                    mapped_type = 'win_product'
            
            # Determine expected
            if 'account' in type_name.lower() or 'tài khoản' in type_name.lower():
                expected = 'account'
            elif 'video' in type_name.lower():
                expected = 'videos'
            elif 'course' in type_name.lower():
                expected = 'course'
            elif 'aff' in type_name.lower():
                expected = 'aff_package'
            else:
                expected = 'win_product'
            
            status = "✅" if mapped_type == expected else "❌"
            print(f"{type_name:<20} {mapped_type:<15} {expected} {status}")
        
        # 5. Check for inconsistencies
        print(f"\n📋 5. Checking for Inconsistencies:")
        
        cursor.execute('''
            SELECT p.product_id, p.name, p.product_type, pt.name as type_name
            FROM "Products" p
            JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
            WHERE p.is_deleted = false OR p.is_deleted IS NULL
        ''')
        
        inconsistencies = []
        for product in cursor.fetchall():
            product_type = product['product_type']
            type_name = product['type_name']
            
            # Check if mapping is correct
            expected_type = type_mapping.get(type_name)
            if not expected_type:
                if type_name.lower() in ['account', 'tài khoản']:
                    expected_type = 'account'
                elif type_name.lower() in ['video', 'videos']:
                    expected_type = 'videos'
                else:
                    expected_type = 'win_product'
            
            if product_type != expected_type:
                inconsistencies.append({
                    'product_id': product['product_id'],
                    'name': product['name'],
                    'current_type': product_type,
                    'type_name': type_name,
                    'expected_type': expected_type
                })
        
        if inconsistencies:
            print(f"❌ Found {len(inconsistencies)} inconsistencies:")
            for inc in inconsistencies:
                print(f"   Product {inc['product_id']}: '{inc['name']}'")
                print(f"      Type Name: {inc['type_name']}")
                print(f"      Current: {inc['current_type']} → Expected: {inc['expected_type']}")
        else:
            print("✅ No inconsistencies found")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error in debug: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_simulation():
    """Simulate API calls to test mapping"""
    print(f"\n🧪 Simulating API Calls")
    print("=" * 60)
    
    # Test scenarios
    scenarios = [
        {'type_id': 1, 'expected_name': 'Account', 'expected_mapping': 'account'},
        {'type_id': 2, 'expected_name': 'Win Product', 'expected_mapping': 'win_product'},
        {'type_id': 12, 'expected_name': 'Video', 'expected_mapping': 'videos'},  # Assuming video type has ID 12
    ]
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        for scenario in scenarios:
            type_id = scenario['type_id']
            
            # Simulate API logic
            cursor.execute('SELECT name FROM "ProductTypes" WHERE type_id = %s', (type_id,))
            type_result = cursor.fetchone()
            
            if type_result:
                type_name = type_result['name']
                
                # Apply mapping logic
                type_mapping = {
                    'Account': 'account',
                    'Win Product': 'win_product',
                    'Course Basic': 'course',
                    'Course Advanced': 'course',
                    'AFF Package': 'aff_package',
                    'TikTok Playbook': 'win_product',
                    'Digital Product': 'win_product',
                    'E-book': 'win_product',
                    'Template': 'win_product',
                    'Tool': 'win_product',
                    'Video': 'videos',
                    'Videos': 'videos'
                }
                
                # Use intelligent default
                if type_name.lower() in ['account', 'tài khoản']:
                    default_type = 'account'
                elif type_name.lower() in ['video', 'videos']:
                    default_type = 'videos'
                else:
                    default_type = 'win_product'
                
                mapped_type = type_mapping.get(type_name, default_type)
                
                print(f"Type ID {type_id}:")
                print(f"   Name: {type_name}")
                print(f"   Mapped to: {mapped_type}")
                print(f"   Expected: {scenario['expected_mapping']}")
                print(f"   Status: {'✅' if mapped_type == scenario['expected_mapping'] else '❌'}")
                print()
            else:
                print(f"Type ID {type_id}: Not found")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error in API simulation: {e}")
        return False

def main():
    """Main function"""
    print("🔍 Product Type Mapping Debug Tool")
    print("=" * 60)
    
    # Debug product types
    debug_success = debug_product_types()
    
    # Test API simulation
    api_success = test_api_simulation()
    
    if debug_success and api_success:
        print("\n✅ Debug completed successfully!")
        print("\n💡 Common issues and solutions:")
        print("  1. Type mapping inconsistency → Update type_mapping dict")
        print("  2. Missing product types → Add to ProductTypes table")
        print("  3. Wrong category mapping → Update CategoryProductTypes")
        print("  4. Frontend caching → Clear browser cache")
        print("  5. API response mismatch → Check server logs")
    else:
        print("\n❌ Debug failed!")

if __name__ == "__main__":
    main()
