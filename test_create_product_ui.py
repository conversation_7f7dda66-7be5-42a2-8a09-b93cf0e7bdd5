#!/usr/bin/env python3
"""
Test tạo sản phẩm qua UI để kiểm tra product type mapping
"""

import psycopg2
import psycopg2.extras
import json

def get_db_connection():
    """Kết nối database PostgreSQL local"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def create_more_product_types():
    """Tạo thêm một số product types để test"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🧪 Creating More Product Types for Testing")
        print("=" * 50)
        
        # Danh sách product types để test
        test_types = [
            {
                'name': 'Digital Marketing Course',
                'description': '<PERSON>h<PERSON><PERSON> học marketing số toàn diện',
                'icon': 'cil-education',
                'metadata': {"requires_files": True, "course_type": True}
            },
            {
                'name': 'AI Content Generator',
                'description': 'Công cụ tạo nội dung bằng AI',
                'icon': 'cil-robot',
                'metadata': {"requires_files": True, "tool_type": True}
            },
            {
                'name': 'Social Media Template Pack',
                'description': 'Bộ template cho social media',
                'icon': 'cil-image',
                'metadata': {"requires_files": True, "template_type": True}
            }
        ]
        
        # Lấy category để gán
        cursor.execute('SELECT category_id FROM "ProductCategories" LIMIT 1')
        category_result = cursor.fetchone()
        category_id = category_result['category_id'] if category_result else 1
        
        created_types = []
        
        for type_data in test_types:
            # Kiểm tra xem đã tồn tại chưa
            cursor.execute('SELECT type_id FROM "ProductTypes" WHERE name = %s', (type_data['name'],))
            existing = cursor.fetchone()
            
            if existing:
                type_id = existing['type_id']
                print(f"✅ Product type '{type_data['name']}' đã tồn tại (ID: {type_id})")
            else:
                cursor.execute('''
                    INSERT INTO "ProductTypes" (name, description, icon, is_active, metadata)
                    VALUES (%s, %s, %s, %s, %s)
                    RETURNING type_id
                ''', (
                    type_data['name'],
                    type_data['description'],
                    type_data['icon'],
                    True,
                    json.dumps(type_data['metadata'])
                ))
                type_id = cursor.fetchone()[0]
                print(f"✅ Tạo product type '{type_data['name']}' thành công (ID: {type_id})")
            
            # Link với category
            cursor.execute('''
                INSERT INTO "CategoryProductTypes" (category_id, type_id, is_primary)
                VALUES (%s, %s, %s)
                ON CONFLICT (category_id, type_id) DO NOTHING
            ''', (category_id, type_id, False))
            
            created_types.append({
                'type_id': type_id,
                'name': type_data['name']
            })
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return created_types
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_product_creation():
    """Simulate việc tạo sản phẩm qua API như UI sẽ làm"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n🧪 Simulating Product Creation via API")
        print("=" * 50)
        
        # Lấy một product type để test
        cursor.execute('SELECT type_id, name FROM "ProductTypes" WHERE name = %s', ('Digital Marketing Course',))
        type_result = cursor.fetchone()
        
        if not type_result:
            print("❌ Không tìm thấy product type để test")
            return False
        
        type_id = type_result['type_id']
        type_name = type_result['name']
        
        print(f"📋 Testing với product type: '{type_name}' (ID: {type_id})")
        
        # Simulate logic trong API create product
        print(f"\n🔍 Simulating API logic:")
        print(f"   Input: product_type_id = {type_id}")
        
        # Get product_type string for backward compatibility (logic từ API)
        cursor.execute('SELECT name FROM "ProductTypes" WHERE type_id = %s', (type_id,))
        type_result = cursor.fetchone()
        
        if type_result:
            type_name_from_db = type_result['name']
            
            # Map specific known types to old product_type values for backward compatibility
            type_mapping = {
                'Account': 'account',
                'Sản Phẩm Win': 'win_product',
                'Win Product': 'win_product',
                'Course Basic': 'course',
                'Course Advanced': 'course',
                'AFF Package': 'aff_package',
                'AI Video Formula': 'videos',
                'Video': 'videos',
                'videos': 'videos',
                'Videos': 'videos'
            }
            
            # Check if it's a known type first
            if type_name_from_db in type_mapping:
                product_type = type_mapping[type_name_from_db]
                print(f"   ✅ Found in mapping: '{type_name_from_db}' → '{product_type}'")
            else:
                # For new/custom types, use intelligent detection based on name
                type_name_lower = type_name_from_db.lower()
                if 'account' in type_name_lower or 'tài khoản' in type_name_lower:
                    product_type = 'account'
                    print(f"   ✅ Detected as account: '{type_name_from_db}' → '{product_type}'")
                elif 'video' in type_name_lower:
                    product_type = 'videos'
                    print(f"   ✅ Detected as videos: '{type_name_from_db}' → '{product_type}'")
                elif 'aff' in type_name_lower or 'affiliate' in type_name_lower:
                    product_type = 'aff_package'
                    print(f"   ✅ Detected as aff_package: '{type_name_from_db}' → '{product_type}'")
                elif 'course' in type_name_lower or 'khóa học' in type_name_lower:
                    product_type = 'course'
                    print(f"   ✅ Detected as course: '{type_name_from_db}' → '{product_type}'")
                else:
                    # For all other types, use the type name directly as product_type for flexibility
                    product_type = type_name_from_db.lower().replace(' ', '_')
                    print(f"   ✅ Using flexible naming: '{type_name_from_db}' → '{product_type}'")
        
        # Tạo sản phẩm test
        test_product_name = f"Test {type_name_from_db} - API Simulation"
        
        # Xóa sản phẩm test cũ nếu có
        cursor.execute('DELETE FROM "Products" WHERE name = %s', (test_product_name,))
        
        # Lấy category
        cursor.execute('SELECT category_id FROM "ProductCategories" LIMIT 1')
        category_result = cursor.fetchone()
        category_id = category_result['category_id'] if category_result else 1
        
        cursor.execute('''
            INSERT INTO "Products" (
                category_id, product_type_id, name, short_description, description,
                price, stock, unlimited_stock, product_type, is_active, is_featured
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING product_id
        ''', (
            category_id,
            type_id,
            test_product_name,
            f"Test sản phẩm cho {type_name_from_db}",
            f"Sản phẩm test để kiểm tra logic mapping từ '{type_name_from_db}' thành '{product_type}'",
            100000,  # 100k MP
            5,
            False,
            product_type,  # Sử dụng product_type đã được map
            True,
            False
        ))
        
        product_id = cursor.fetchone()[0]
        print(f"   ✅ Tạo sản phẩm thành công (ID: {product_id})")
        
        # Kiểm tra kết quả
        cursor.execute('''
            SELECT p.product_id, p.name, p.product_type, pt.name as type_name
            FROM "Products" p
            LEFT JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
            WHERE p.product_id = %s
        ''', (product_id,))
        
        result = cursor.fetchone()
        print(f"\n📋 Kết quả:")
        print(f"   Product ID: {result['product_id']}")
        print(f"   Product Name: {result['name']}")
        print(f"   product_type (string): '{result['product_type']}'")
        print(f"   type_name (from ProductTypes): '{result['type_name']}'")
        
        # Test hiển thị
        if result['type_name']:
            display_name = result['type_name']
        else:
            labels = {
                'account': 'Account',
                'win_product': 'Win Product',
                'course': 'Khóa học',
                'aff_package': 'AFF Package'
            }
            display_name = labels.get(result['product_type'], result['product_type'])
        
        print(f"   Hiển thị trên UI: '{display_name}'")
        
        if display_name == type_name_from_db:
            print(f"   ✅ SUCCESS - UI sẽ hiển thị đúng tên ProductType")
        else:
            print(f"   ❌ FAIL - UI không hiển thị đúng tên ProductType")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🧪 Testing Product Creation UI Flow")
    print("=" * 60)
    
    # Test 1: Tạo thêm product types
    created_types = create_more_product_types()
    
    if created_types:
        # Test 2: Simulate việc tạo sản phẩm
        simulation_success = simulate_product_creation()
        
        if simulation_success:
            print(f"\n✅ Test hoàn thành!")
            print(f"📋 Kết quả:")
            print(f"   - Logic mapping product type đã hoạt động đúng")
            print(f"   - Sản phẩm mới sẽ hiển thị đúng tên ProductType")
            print(f"   - Không còn bị gán cứng thành 'Win Product'")
            
            print(f"\n🔧 Để test trên UI:")
            print(f"   1. Mở /admin/marketplace/create-product")
            print(f"   2. Chọn category và product type 'Digital Marketing Course'")
            print(f"   3. Tạo sản phẩm và kiểm tra hiển thị")
        else:
            print(f"\n❌ Test simulation thất bại!")
    else:
        print(f"\n❌ Test tạo product types thất bại!")

if __name__ == "__main__":
    main()
