#!/usr/bin/env python3
"""
Add thumbnail_url column to ProductCategories table
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_thumbnail_url_column():
    """Add thumbnail_url column to ProductCategories table"""
    
    print("🚀 Adding thumbnail_url column to ProductCategories table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current ProductCategories table structure
        print("\n🔧 Step 1: Checking current ProductCategories table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductCategories' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Current columns: {existing_column_names}")
        
        # Step 2: Add thumbnail_url column if not exists
        if 'thumbnail_url' not in existing_column_names:
            print("\n🔧 Step 2: Adding thumbnail_url column...")
            
            cursor.execute('''
                ALTER TABLE "ProductCategories" 
                ADD COLUMN thumbnail_url VARCHAR(255);
            ''')
            
            print("  ✅ Added thumbnail_url column (VARCHAR(255))")
        else:
            print("\n✅ Step 2: thumbnail_url column already exists")
        
        # Step 3: Create index for performance
        print("\n🔧 Step 3: Creating index...")
        
        try:
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_product_categories_thumbnail 
                ON "ProductCategories"(thumbnail_url) 
                WHERE thumbnail_url IS NOT NULL;
            ''')
            print("  ✅ Created index: idx_product_categories_thumbnail")
        except Exception as e:
            print(f"  ⚠️  Index creation: {e}")
        
        # Step 4: Verify final structure
        print("\n🔧 Step 4: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductCategories' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Final ProductCategories columns:")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 5: Test data
        print("\n🔧 Step 5: Testing data...")
        
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                COUNT(thumbnail_url) as with_thumbnail,
                COUNT(*) - COUNT(thumbnail_url) as without_thumbnail
            FROM "ProductCategories";
        ''')
        
        stats = cursor.fetchone()
        print(f"📊 Category statistics:")
        print(f"  - Total categories: {stats[0]}")
        print(f"  - With thumbnail: {stats[1]}")
        print(f"  - Without thumbnail: {stats[2]}")
        
        # Step 6: Test API query
        print("\n🔧 Step 6: Testing API query...")
        
        try:
            cursor.execute('''
                SELECT category_id, name, description, icon, thumbnail_url,
                       (SELECT COUNT(*) FROM "Products" WHERE category_id = c.category_id AND (status = 'active' OR status IS NULL) AND product_type != 'aff_package') as product_count
                FROM "ProductCategories" c
                WHERE (is_active = true OR is_active IS NULL)
                ORDER BY sort_order, name
                LIMIT 3;
            ''')
            
            test_rows = cursor.fetchall()
            print(f"  ✅ API query test successful - {len(test_rows)} rows returned")
            
            for row in test_rows:
                print(f"    - {row[1]}: thumbnail_url = {row[4]}")
                
        except Exception as e:
            print(f"  ❌ API query test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Migration completed successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test user marketplace page: /marketplace")
        print("   3. Test admin categories management")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_thumbnail_url_column()
    sys.exit(0 if success else 1)
