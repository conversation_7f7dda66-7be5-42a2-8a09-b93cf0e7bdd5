#!/usr/bin/env python3
"""
Fix Deposits table timezone and data type issues
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_deposits_timezone():
    """Fix Deposits table timezone and data type issues"""
    
    print("🔧 Fixing Deposits table timezone issues...")
    print("=" * 60)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Check current Deposits table structure
        print("\n🔍 Checking current Deposits table structure...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Deposits' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        columns = cursor.fetchall()
        print(f"📋 Current Deposits table structure:")
        for col in columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Check if created_at and completed_at are TIME instead of TIMESTAMP
        time_columns = [col for col in columns if col[0] in ['created_at', 'completed_at'] and 'time' in col[1]]
        
        if time_columns:
            print(f"\n❌ Found TIME columns that should be TIMESTAMP:")
            for col in time_columns:
                print(f"  - {col[0]}: {col[1]} (should be timestamp)")
            
            print("\n🔧 Converting TIME columns to TIMESTAMP...")
            
            # First, let's see the current data
            cursor.execute('SELECT id, created_at, completed_at FROM "Deposits" LIMIT 3;')
            sample_data = cursor.fetchall()
            print(f"\n📋 Sample current data:")
            for row in sample_data:
                print(f"  - ID {row[0]}: created_at={row[1]}, completed_at={row[2]}")
            
            # Add new timestamp columns
            print("\n🔧 Adding new timestamp columns...")
            cursor.execute('ALTER TABLE "Deposits" ADD COLUMN created_at_new TIMESTAMP DEFAULT CURRENT_TIMESTAMP;')
            cursor.execute('ALTER TABLE "Deposits" ADD COLUMN completed_at_new TIMESTAMP;')
            
            # Convert existing time data to today's timestamp
            print("🔧 Converting existing time data to timestamps...")
            cursor.execute('''
                UPDATE "Deposits" 
                SET created_at_new = CURRENT_DATE + created_at
                WHERE created_at IS NOT NULL;
            ''')
            
            cursor.execute('''
                UPDATE "Deposits" 
                SET completed_at_new = CURRENT_DATE + completed_at
                WHERE completed_at IS NOT NULL;
            ''')
            
            # Drop old columns and rename new ones
            print("🔧 Replacing old columns...")
            cursor.execute('ALTER TABLE "Deposits" DROP COLUMN created_at;')
            cursor.execute('ALTER TABLE "Deposits" DROP COLUMN completed_at;')
            cursor.execute('ALTER TABLE "Deposits" RENAME COLUMN created_at_new TO created_at;')
            cursor.execute('ALTER TABLE "Deposits" RENAME COLUMN completed_at_new TO completed_at;')
            
            print("✅ Converted TIME columns to TIMESTAMP")
        else:
            print("✅ Deposits table already has correct TIMESTAMP columns")
        
        # Commit changes
        conn.commit()
        
        # Validate final structure
        print("\n🔍 Final Deposits table structure...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Deposits' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Final structure:")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Test final data
        print("\n🔍 Testing final data...")
        cursor.execute('SELECT id, created_at, completed_at FROM "Deposits" LIMIT 3;')
        final_data = cursor.fetchall()
        print(f"📋 Sample final data:")
        for row in final_data:
            print(f"  - ID {row[0]}: created_at={row[1]}, completed_at={row[2]}")
        
        # Test timezone conversion
        print("\n🔍 Testing timezone conversion to Vietnam (+7)...")
        cursor.execute('''
            SELECT id, 
                   created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh' as created_at_vn,
                   completed_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh' as completed_at_vn
            FROM "Deposits" 
            WHERE created_at IS NOT NULL
            LIMIT 3;
        ''')
        
        vn_data = cursor.fetchall()
        print(f"📋 Vietnam timezone (+7) data:")
        for row in vn_data:
            print(f"  - ID {row[0]}: created_at_vn={row[1]}, completed_at_vn={row[2]}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Deposits timezone fix completed!")
        print("📝 Next steps:")
        print("   1. Update frontend code to handle timezone conversion")
        print("   2. Test /deposit page")
        print("   3. Test /transactions page")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_deposits_timezone()
    sys.exit(0 if success else 1)
