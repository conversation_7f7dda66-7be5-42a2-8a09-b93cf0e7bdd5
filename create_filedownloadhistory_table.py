#!/usr/bin/env python3
"""
Create FileDownloadHistory table for tracking file downloads
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def create_filedownloadhistory_table():
    """Create FileDownloadHistory table"""
    
    print("🚀 Creating FileDownloadHistory table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check if table exists
        print("\n🔧 Step 1: Checking if FileDownloadHistory table exists...")
        
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'FileDownloadHistory'
            );
        ''')
        
        table_exists = cursor.fetchone()[0]
        print(f"📋 FileDownloadHistory table exists: {table_exists}")
        
        if table_exists:
            print("✅ Table already exists, skipping creation")
            return True
        
        # Step 2: Create sequence
        print("\n🔧 Step 2: Creating sequence...")
        
        cursor.execute('''
            CREATE SEQUENCE IF NOT EXISTS "FileDownloadHistory_id_seq"
            INCREMENT 1
            MINVALUE 1
            MAXVALUE 2147483647
            START 1
            CACHE 1;
        ''')
        print("  ✅ Created sequence: FileDownloadHistory_id_seq")
        
        # Step 3: Create table
        print("\n🔧 Step 3: Creating FileDownloadHistory table...")
        
        cursor.execute('''
            CREATE TABLE "FileDownloadHistory" (
                "id" int4 NOT NULL DEFAULT nextval('"FileDownloadHistory_id_seq"'::regclass),
                "file_id" int4 NOT NULL,
                "user_id" int4 NOT NULL,
                "product_id" int4,
                "download_time" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                "ip_address" varchar(45),
                "user_agent" text,
                "download_size" int8,
                "download_status" varchar(20) DEFAULT 'completed',
                "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        print("  ✅ Created FileDownloadHistory table")
        
        # Step 4: Add comments
        print("\n🔧 Step 4: Adding column comments...")
        
        comments = [
            ("file_id", "ID của file được download"),
            ("user_id", "ID của user download"),
            ("product_id", "ID của sản phẩm chứa file"),
            ("download_time", "Thời gian download"),
            ("ip_address", "IP address của user"),
            ("user_agent", "User agent của browser"),
            ("download_size", "Kích thước file download (bytes)"),
            ("download_status", "Trạng thái download: completed, failed, cancelled")
        ]
        
        for column, comment in comments:
            cursor.execute(f'''
                COMMENT ON COLUMN "FileDownloadHistory"."{column}" IS '{comment}';
            ''')
            print(f"    ✅ Added comment for {column}")
        
        cursor.execute('''
            COMMENT ON TABLE "FileDownloadHistory" IS 'Lịch sử download files của users';
        ''')
        print("  ✅ Added table comment")
        
        # Step 5: Create indexes
        print("\n🔧 Step 5: Creating indexes...")
        
        indexes = [
            ('idx_filedownloadhistory_file_id', 'file_id'),
            ('idx_filedownloadhistory_user_id', 'user_id'),
            ('idx_filedownloadhistory_product_id', 'product_id'),
            ('idx_filedownloadhistory_download_time', 'download_time'),
            ('idx_filedownloadhistory_user_file', 'user_id, file_id'),
            ('idx_filedownloadhistory_status', 'download_status')
        ]
        
        for index_name, columns in indexes:
            cursor.execute(f'''
                CREATE INDEX "{index_name}" ON "FileDownloadHistory" ({columns});
            ''')
            print(f"  ✅ Created index: {index_name}")
        
        # Step 6: Add constraints
        print("\n🔧 Step 6: Adding constraints...")
        
        # Primary key
        cursor.execute('''
            ALTER TABLE "FileDownloadHistory" ADD CONSTRAINT "FileDownloadHistory_pkey" PRIMARY KEY ("id");
        ''')
        print("  ✅ Added primary key constraint")
        
        # Check constraint for download_status
        cursor.execute('''
            ALTER TABLE "FileDownloadHistory" ADD CONSTRAINT "chk_download_status" 
            CHECK (download_status::text = ANY (ARRAY['completed'::character varying, 'failed'::character varying, 'cancelled'::character varying, 'in_progress'::character varying]::text[]));
        ''')
        print("  ✅ Added download_status check constraint")
        
        # Step 7: Add foreign keys (check if referenced tables exist)
        print("\n🔧 Step 7: Adding foreign keys...")
        
        # Check if ProductFiles table exists
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'ProductFiles'
            );
        ''')
        productfiles_exists = cursor.fetchone()[0]
        
        if productfiles_exists:
            cursor.execute('''
                ALTER TABLE "FileDownloadHistory" ADD CONSTRAINT "FileDownloadHistory_file_id_fkey" 
                FOREIGN KEY ("file_id") REFERENCES "ProductFiles" ("file_id") ON DELETE CASCADE ON UPDATE NO ACTION;
            ''')
            print("  ✅ Added foreign key to ProductFiles table")
        else:
            print("  ⚠️  ProductFiles table not found, skipping foreign key")
        
        # Check if Users table exists
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Users'
            );
        ''')
        users_exists = cursor.fetchone()[0]
        
        if users_exists:
            cursor.execute('''
                ALTER TABLE "FileDownloadHistory" ADD CONSTRAINT "FileDownloadHistory_user_id_fkey" 
                FOREIGN KEY ("user_id") REFERENCES "Users" ("user_id") ON DELETE CASCADE ON UPDATE NO ACTION;
            ''')
            print("  ✅ Added foreign key to Users table")
        else:
            print("  ⚠️  Users table not found, skipping foreign key")
        
        # Check if Products table exists
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Products'
            );
        ''')
        products_exists = cursor.fetchone()[0]
        
        if products_exists:
            cursor.execute('''
                ALTER TABLE "FileDownloadHistory" ADD CONSTRAINT "FileDownloadHistory_product_id_fkey" 
                FOREIGN KEY ("product_id") REFERENCES "Products" ("product_id") ON DELETE SET NULL ON UPDATE NO ACTION;
            ''')
            print("  ✅ Added foreign key to Products table")
        else:
            print("  ⚠️  Products table not found, skipping foreign key")
        
        # Step 8: Verify final structure
        print("\n🔧 Step 8: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'FileDownloadHistory' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Final FileDownloadHistory columns ({len(final_columns)} total):")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 9: Test insert query
        print("\n🔧 Step 9: Testing insert query...")
        
        try:
            # Test the problematic INSERT query structure
            cursor.execute('''
                SELECT 1 FROM "FileDownloadHistory" WHERE 1=0;
            ''')
            print("  ✅ Table structure test successful")
            
        except Exception as e:
            print(f"  ❌ Table structure test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 FileDownloadHistory table created successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test file download functionality")
        print("   3. Check download history tracking")
        
        return True
        
    except Exception as e:
        print(f"❌ Creation failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = create_filedownloadhistory_table()
    sys.exit(0 if success else 1)
