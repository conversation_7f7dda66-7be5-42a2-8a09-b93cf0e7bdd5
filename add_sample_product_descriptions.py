#!/usr/bin/env python3
"""
Add sample product descriptions to test the new description feature
"""

import sys
import os

# Import database config
try:
    from db_config import PG_CONFIG
    print(f"✅ Loaded database config: {PG_CONFIG['host']}:{PG_CONFIG['port']}/{PG_CONFIG['database']}")
except ImportError:
    print("❌ Cannot import db_config.py")
    print("Make sure db_config.py exists in the same directory")
    sys.exit(1)

import psycopg2
import psycopg2.extras

def get_db_connection():
    """Kết nối database PostgreSQL"""
    try:
        conn = psycopg2.connect(
            host=PG_CONFIG['host'],
            database=PG_CONFIG['database'],
            user=PG_CONFIG['user'],
            password=PG_CONFIG['password'],
            port=PG_CONFIG['port']
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def check_products_table():
    """Kiểm tra bảng Products và cột description"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Check if Products table exists
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'Products'
            );
        ''')
        
        table_exists = cursor.fetchone()[0]
        if not table_exists:
            print("❌ Bảng Products không tồn tại")
            return False
        
        print("✅ Bảng Products tồn tại")
        
        # Check if description column exists
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Products' AND table_schema = 'public'
            AND column_name = 'description';
        ''')
        
        desc_column = cursor.fetchone()
        if not desc_column:
            print("❌ Cột description không tồn tại")
            return False
        
        print(f"✅ Cột description tồn tại: {desc_column['data_type']}")
        
        # Check current products
        cursor.execute('SELECT COUNT(*) FROM "Products"')
        product_count = cursor.fetchone()[0]
        print(f"📊 Số sản phẩm hiện có: {product_count}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Lỗi kiểm tra bảng: {e}")
        return False

def add_sample_descriptions():
    """Thêm mô tả mẫu cho các sản phẩm"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Sample descriptions
        sample_descriptions = [
            {
                'name': 'Gói Account TikTok Premium',
                'short_description': 'Gói tài khoản TikTok chất lượng cao với follower thật',
                'description': '''## Gói Account TikTok Premium

**Đặc điểm nổi bật:**
- Follower từ 10K - 50K thật 100%
- Tương tác cao, engagement rate > 3%
- Account đã verify, có tick xanh
- Bảo hành 30 ngày

### Thông tin chi tiết:
- **Độ tuổi account:** 6-12 tháng
- **Niche:** Lifestyle, Entertainment, Dance
- **Quốc gia:** Việt Nam, Thái Lan, Philippines
- **Gender:** 60% nữ, 40% nam

### Quy trình giao hàng:
1. Thanh toán đơn hàng
2. Nhận thông tin account trong 2-4 giờ
3. Kiểm tra và xác nhận
4. Hỗ trợ đổi account nếu có vấn đề

**Lưu ý quan trọng:**
- Không thay đổi thông tin cá nhân trong 7 ngày đầu
- Sử dụng proxy để tránh bị khóa
- Liên hệ support nếu có vấn đề

[Xem hướng dẫn sử dụng](https://example.com/guide)''',
                'price': 500000,
                'product_type': 'account'
            },
            {
                'name': 'Khóa học AI Video Formula',
                'short_description': 'Học cách tạo video AI viral trên TikTok',
                'description': '''# Khóa học AI Video Formula

Học cách tạo video AI viral trên TikTok với **công nghệ mới nhất** và *chiến lược đã được kiểm chứng*.

## Nội dung khóa học:

### Module 1: Cơ bản về AI Video
- Giới thiệu các công cụ AI tạo video
- Cách chọn niche phù hợp
- Phân tích xu hướng viral

### Module 2: Thực hành tạo video
- Sử dụng ChatGPT để viết script
- Tạo video với Runway ML
- Chỉnh sửa với CapCut Pro

### Module 3: Tối ưu hóa và phát triển
- SEO cho TikTok
- Phân tích metrics
- Scale up kênh

## Bonus materials:
- **100+ templates** video AI
- *Thư viện âm thanh* trending
- **Checklist** viral content
- *Group hỗ trợ* 24/7

## Cam kết:
1. Hoàn tiền 100% nếu không hài lòng
2. Cập nhật nội dung miễn phí
3. Hỗ trợ 1-1 trong 3 tháng

**Giá trị khóa học:** 5,000,000 VND
**Giá ưu đãi:** 1,200,000 MP

[Đăng ký ngay](https://example.com/register) | [Xem demo](https://example.com/demo)''',
                'price': 1200000,
                'product_type': 'course'
            },
            {
                'name': 'Win Product Dropshipping',
                'short_description': 'Sản phẩm win đã được test và verify',
                'description': '''## Win Product Dropshipping

### Sản phẩm đã được test:
- **Conversion rate:** 3-5%
- **ROI:** 200-300%
- **Profit margin:** 40-60%

### Package bao gồm:
- Product research report
- Supplier contact
- Marketing materials
- Video ads templates

### Hỗ trợ:
- Setup store miễn phí
- Tư vấn marketing strategy
- Group support 24/7''',
                'price': 800000,
                'product_type': 'win_product'
            }
        ]
        
        # Check if we need to create sample products or update existing ones
        cursor.execute('SELECT product_id, name FROM "Products" LIMIT 10')
        existing_products = cursor.fetchall()
        
        if existing_products:
            print(f"📝 Cập nhật mô tả cho {len(existing_products)} sản phẩm hiện có...")
            
            for i, product in enumerate(existing_products):
                if i < len(sample_descriptions):
                    sample = sample_descriptions[i]
                    cursor.execute('''
                        UPDATE "Products" 
                        SET description = %s, short_description = %s
                        WHERE product_id = %s
                    ''', (sample['description'], sample['short_description'], product['product_id']))
                    print(f"  ✅ Cập nhật sản phẩm: {product['name']}")
        else:
            print("📝 Tạo sản phẩm mẫu mới...")
            
            # Get or create default category
            cursor.execute('SELECT category_id FROM "ProductCategories" LIMIT 1')
            category = cursor.fetchone()
            
            if not category:
                cursor.execute('''
                    INSERT INTO "ProductCategories" (name, description, icon)
                    VALUES (%s, %s, %s)
                    RETURNING category_id
                ''', ('Sample Category', 'Category for sample products', 'cil-star'))
                category_id = cursor.fetchone()[0]
            else:
                category_id = category[0]
            
            # Create sample products
            for sample in sample_descriptions:
                cursor.execute('''
                    INSERT INTO "Products" (
                        category_id, name, description, short_description, price, 
                        product_type, stock, unlimited_stock, is_active, is_featured
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING product_id
                ''', (
                    category_id, sample['name'], sample['description'], 
                    sample['short_description'], sample['price'], sample['product_type'],
                    10, False, True, True
                ))
                
                product_id = cursor.fetchone()[0]
                print(f"  ✅ Tạo sản phẩm mới: {sample['name']} (ID: {product_id})")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("\n✅ Hoàn thành thêm mô tả sản phẩm!")
        return True
        
    except Exception as e:
        print(f"❌ Lỗi thêm mô tả: {e}")
        conn.rollback()
        return False

def verify_descriptions():
    """Kiểm tra mô tả đã được thêm"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        cursor.execute('''
            SELECT product_id, name, 
                   CASE 
                       WHEN description IS NOT NULL AND description != '' THEN 'Có'
                       ELSE 'Không'
                   END as has_description,
                   LENGTH(description) as desc_length
            FROM "Products"
            ORDER BY product_id
            LIMIT 10
        ''')
        
        products = cursor.fetchall()
        
        print("\n📋 Kiểm tra mô tả sản phẩm:")
        print("ID | Tên sản phẩm | Có mô tả | Độ dài")
        print("-" * 50)
        
        for product in products:
            print(f"{product['product_id']:2d} | {product['name'][:20]:20s} | {product['has_description']:8s} | {product['desc_length'] or 0:6d}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Lỗi kiểm tra: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Adding sample product descriptions...")
    print("=" * 60)
    
    # Step 1: Check table structure
    if not check_products_table():
        print("❌ Không thể kiểm tra bảng Products")
        return
    
    # Step 2: Add descriptions
    if not add_sample_descriptions():
        print("❌ Không thể thêm mô tả")
        return
    
    # Step 3: Verify
    verify_descriptions()
    
    print("\n🎉 Hoàn thành! Bây giờ có thể test modal description.")
    print("📝 Truy cập /marketplace và click 'Chi tiết' để xem mô tả.")

if __name__ == "__main__":
    main()
