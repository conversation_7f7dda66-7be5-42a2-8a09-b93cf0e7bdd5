#!/usr/bin/env python3
"""
Recreate ProductFiles table with complete structure
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def recreate_productfiles_table():
    """Recreate ProductFiles table with complete structure"""
    
    print("🚀 Recreating ProductFiles table with complete structure...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check if table exists
        print("\n🔧 Step 1: Checking if ProductFiles table exists...")
        
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'ProductFiles'
            );
        ''')
        
        table_exists = cursor.fetchone()[0]
        print(f"📋 ProductFiles table exists: {table_exists}")
        
        # Step 2: Drop table if exists (backup data first if needed)
        if table_exists:
            print("\n🔧 Step 2: Backing up existing data...")
            
            try:
                cursor.execute('SELECT COUNT(*) FROM "ProductFiles";')
                row_count = cursor.fetchone()[0]
                print(f"📊 Found {row_count} existing records")
                
                if row_count > 0:
                    print("⚠️  Warning: Existing data will be lost!")
                    print("💡 Consider backing up data manually if needed")
                
            except Exception as e:
                print(f"📊 Could not count existing records: {e}")
            
            print("\n🔧 Dropping existing ProductFiles table...")
            cursor.execute('DROP TABLE IF EXISTS "ProductFiles" CASCADE;')
            print("  ✅ Dropped existing table")
        
        # Step 3: Create sequence
        print("\n🔧 Step 3: Creating sequence...")
        
        cursor.execute('''
            CREATE SEQUENCE IF NOT EXISTS "ProductFiles_file_id_seq"
            INCREMENT 1
            MINVALUE 1
            MAXVALUE 2147483647
            START 1
            CACHE 1;
        ''')
        print("  ✅ Created sequence: ProductFiles_file_id_seq")
        
        # Step 4: Create table with complete structure
        print("\n🔧 Step 4: Creating ProductFiles table...")
        
        cursor.execute('''
            CREATE TABLE "ProductFiles" (
                "file_id" int4 NOT NULL DEFAULT nextval('"ProductFiles_file_id_seq"'::regclass),
                "product_id" int4,
                "filename" varchar(255) NOT NULL,
                "file_path" varchar(500) NOT NULL,
                "file_type" varchar(50),
                "file_size" int8,
                "is_primary" bool DEFAULT false,
                "download_count" int4 DEFAULT 0,
                "uploaded_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                "sort_order" int4 DEFAULT 0,
                "is_preview" bool DEFAULT false,
                "uploaded_by" int4,
                "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                "file_name" varchar(255) NOT NULL,
                "original_name" varchar(255) NOT NULL,
                "file_extension" varchar(10) NOT NULL,
                "mime_type" varchar(100),
                "description" text
            );
        ''')
        print("  ✅ Created ProductFiles table with all columns")
        
        # Step 5: Add comments
        print("\n🔧 Step 5: Adding column comments...")
        
        comments = [
            ("sort_order", "Thứ tự hiển thị files"),
            ("is_preview", "File có thể xem trước khi mua (preview)"),
            ("file_name", "Tên file được lưu trên server"),
            ("original_name", "Tên file gốc do user upload"),
            ("file_extension", "Phần mở rộng của file"),
            ("mime_type", "MIME type của file"),
            ("description", "Mô tả file do admin thêm")
        ]
        
        for column, comment in comments:
            cursor.execute(f'''
                COMMENT ON COLUMN "ProductFiles"."{column}" IS '{comment}';
            ''')
            print(f"    ✅ Added comment for {column}")
        
        cursor.execute('''
            COMMENT ON TABLE "ProductFiles" IS 'Files đính kèm cho sản phẩm (hình ảnh, tài liệu, video)';
        ''')
        print("  ✅ Added table comment")
        
        # Step 6: Create indexes
        print("\n🔧 Step 6: Creating indexes...")
        
        indexes = [
            ('idx_product_files_product_id', 'product_id'),
            ('idx_product_files_sort', 'product_id, sort_order'),
            ('idx_product_files_type', 'file_type')
        ]
        
        for index_name, columns in indexes:
            cursor.execute(f'''
                CREATE INDEX "{index_name}" ON "ProductFiles" ({columns});
            ''')
            print(f"  ✅ Created index: {index_name}")
        
        # Step 7: Add constraints
        print("\n🔧 Step 7: Adding constraints...")
        
        # Primary key
        cursor.execute('''
            ALTER TABLE "ProductFiles" ADD CONSTRAINT "ProductFiles_pkey" PRIMARY KEY ("file_id");
        ''')
        print("  ✅ Added primary key constraint")
        
        # Check constraint for file_type
        cursor.execute('''
            ALTER TABLE "ProductFiles" ADD CONSTRAINT "chk_file_type" 
            CHECK (file_type::text = ANY (ARRAY['image'::character varying, 'document'::character varying, 'video'::character varying, 'audio'::character varying, 'other'::character varying]::text[]));
        ''')
        print("  ✅ Added file_type check constraint")
        
        # Step 8: Add foreign keys (check if referenced tables exist)
        print("\n🔧 Step 8: Adding foreign keys...")
        
        # Check if Products table exists
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Products'
            );
        ''')
        products_exists = cursor.fetchone()[0]
        
        if products_exists:
            cursor.execute('''
                ALTER TABLE "ProductFiles" ADD CONSTRAINT "ProductFiles_product_id_fkey" 
                FOREIGN KEY ("product_id") REFERENCES "Products" ("product_id") ON DELETE NO ACTION ON UPDATE NO ACTION;
            ''')
            print("  ✅ Added foreign key to Products table")
        else:
            print("  ⚠️  Products table not found, skipping foreign key")
        
        # Check if Users table exists
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Users'
            );
        ''')
        users_exists = cursor.fetchone()[0]
        
        if users_exists:
            cursor.execute('''
                ALTER TABLE "ProductFiles" ADD CONSTRAINT "ProductFiles_uploaded_by_fkey" 
                FOREIGN KEY ("uploaded_by") REFERENCES "Users" ("user_id") ON DELETE NO ACTION ON UPDATE NO ACTION;
            ''')
            print("  ✅ Added foreign key to Users table")
        else:
            print("  ⚠️  Users table not found, skipping foreign key")
        
        # Step 9: Verify final structure (skip owner setting)
        print("\n🔧 Step 9: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductFiles' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Final ProductFiles columns ({len(final_columns)} total):")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 10: Test the problematic query
        print("\n🔧 Step 10: Testing the problematic query...")
        
        try:
            cursor.execute('''
                SELECT product_id, filename, file_name, original_name, file_size, 
                       file_type, uploaded_at as upload_date, is_preview, description
                FROM "ProductFiles" 
                LIMIT 1;
            ''')
            
            print("  ✅ Query test successful - table structure is correct")
            
        except Exception as e:
            print(f"  ❌ Query test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 ProductFiles table recreated successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test product file upload functionality")
        print("   3. Test product creation with files")
        
        return True
        
    except Exception as e:
        print(f"❌ Recreation failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = recreate_productfiles_table()
    sys.exit(0 if success else 1)
