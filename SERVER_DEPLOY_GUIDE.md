# Server Deploy Guide - Fix Password Column

## 🎯 Mục tiêu
Fix lỗi `column "password_hash" of relation "Users" does not exist` trên server.

## 📁 Files cần upload lên server
1. `fix_server_password_column_v2.py` - Migration script (sử dụng db_config.py)
2. `update_server_code.py` - Code update script
3. `mip_system.py` - Updated code (nếu cần)

## 🚀 Các bước thực hiện

### Bước 1: Upload files
```bash
# Upload files lên server vào thư mục project
scp fix_server_password_column_v2.py root@your-server:/var/www/sapmmo/
scp update_server_code.py root@your-server:/var/www/sapmmo/
```

### Bước 2: SSH vào server
```bash
ssh root@your-server
cd /var/www/sapmmo
```

### Bước 3: Chạy database migration
```bash
# Activate virtual environment nếu có
source sapmmo/bin/activate

# Chạy migration
python3 fix_server_password_column_v2.py
```

**Expected output:**
```
✅ Loaded database config: localhost:5432/sapmmo
✅ Database connection successful!
📋 Current Users table structure:
🔧 Only password_hash exists - renaming to password...
✅ Renamed password_hash to password
✅ Test INSERT successful
✅ Server password column fix completed successfully!
```

### Bước 4: Update code
```bash
# Chạy code update
python3 update_server_code.py
```

**Expected output:**
```
✅ Found mip_system.py at: /var/www/sapmmo/mip_system.py
✅ Fixed /register INSERT statement
✅ Fixed /add_user INSERT statement
✅ Fixed /edit_user UPDATE statement
✅ Code update completed successfully!
```

### Bước 5: Restart services
```bash
# Restart gunicorn
sudo systemctl restart gunicorn

# Check status
sudo systemctl status gunicorn
```

## ✅ Testing

### Test 1: Registration
```bash
# Truy cập: https://your-domain.com/register
# Thử đăng ký với:
Username: Test.User-123
Email: <EMAIL>  
Phone: 0123456789
Password: password123
```

**Expected:**
- ✅ Đăng ký thành công
- ✅ Redirect về /login với username="testuser123"
- ✅ Success message màu xanh

### Test 2: Login
```bash
# Username field đã pre-filled: testuser123
# Nhập password: password123
```

**Expected:**
- ✅ Login thành công
- ✅ Redirect về dashboard

### Test 3: Database check
```bash
# Kiểm tra user vừa tạo
sudo -u postgres psql sapmmo -c "SELECT username, unit_code FROM \"Users\" WHERE username = 'testuser123';"
```

**Expected:**
```
   username   |      unit_code       
--------------+---------------------
 testuser123  | SAPMMOtestuser123XXXX
```

## 🚨 Troubleshooting

### Lỗi kết nối database
```bash
# Kiểm tra PostgreSQL
sudo systemctl status postgresql

# Kiểm tra db_config.py
cat db_config.py
```

### Lỗi permission
```bash
# Check file permissions
ls -la fix_server_password_column_v2.py
chmod +x fix_server_password_column_v2.py
```

### Lỗi gunicorn không restart
```bash
# Force restart
sudo systemctl stop gunicorn
sudo systemctl start gunicorn

# Check logs
sudo journalctl -u gunicorn -f
```

### Lỗi vẫn còn password_hash
```bash
# Kiểm tra database columns
sudo -u postgres psql sapmmo -c "SELECT column_name FROM information_schema.columns WHERE table_name = 'Users' AND column_name LIKE '%password%';"

# Nếu vẫn có password_hash, chạy lại migration
python3 fix_server_password_column_v2.py
```

## 📋 Checklist

- [ ] Files uploaded to server
- [ ] Database migration completed
- [ ] Code updated
- [ ] Gunicorn restarted
- [ ] Registration test passed
- [ ] Login test passed
- [ ] Database check passed

## 🎉 Success Indicators

Khi mọi thứ hoạt động đúng:

1. **Registration**: Có thể đăng ký user mới
2. **Username normalization**: `Test.User-123` → `testuser123`
3. **Unit code clean**: `SAPMMOtestuser123XXXX`
4. **Redirect**: Sau đăng ký redirect về login với username pre-filled
5. **Alert colors**: Success màu xanh, error màu đỏ
6. **Login**: Có thể login với username normalized

---

**🔥 Quick Commands:**
```bash
# Full deployment sequence
cd /var/www/sapmmo
python3 fix_server_password_column_v2.py
python3 update_server_code.py
sudo systemctl restart gunicorn
```
