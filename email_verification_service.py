#!/usr/bin/env python3
"""
Email verification service using Mailtrap
"""

import random
import string
import psycopg2
import psycopg2.extras
from datetime import datetime, timedelta
from db_config import PG_CONFIG

# Import mailtrap (will be installed)
try:
    from mailtrap import MailtrapClient
    MAILTRAP_AVAILABLE = True
except ImportError:
    MAILTRAP_AVAILABLE = False
    print("⚠️ Mailtrap library not installed. Run: pip install mailtrap")

def get_db_connection():
    """Kết nối database PostgreSQL từ config"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def get_email_config():
    """Lấy email configuration từ database"""
    conn = get_db_connection()
    if not conn:
        print("❌ DEBUG: No database connection")
        return None

    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)

        # Get email configs
        cursor.execute('''
            SELECT config_key, config_value
            FROM "SystemConfig"
            WHERE config_key IN (
                'mailtrap_api_token',
                'mailtrap_sender_email',
                'mailtrap_sender_name',
                'email_verification_enabled',
                'otp_expiry_minutes',
                'max_otp_attempts'
            )
        ''')

        config = {}
        for row in cursor.fetchall():
            config[row['config_key']] = row['config_value']

        print(f"🔍 DEBUG get_email_config: {config}")

        cursor.close()
        conn.close()

        return config

    except Exception as e:
        print(f"❌ Error getting email config: {e}")
        import traceback
        traceback.print_exc()
        return None

def generate_otp():
    """Tạo mã OTP 6 số"""
    return ''.join(random.choices(string.digits, k=6))

def create_verification_code(email, code_type, user_id=None, ip_address=None, user_agent=None):
    """Tạo mã xác thực mới"""
    print(f"🔍 DEBUG create_verification_code: START")
    print(f"🔍 DEBUG create_verification_code: email={email}, type={code_type}, user_id={user_id}")
    print(f"🔍 DEBUG create_verification_code: ip={ip_address}, user_agent={user_agent}")

    conn = get_db_connection()
    print(f"🔍 DEBUG create_verification_code: conn={conn}")

    if not conn:
        print(f"❌ DEBUG create_verification_code: No database connection")
        return None

    try:
        print(f"🔍 DEBUG create_verification_code: Creating cursor...")
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        print(f"🔍 DEBUG create_verification_code: Cursor created successfully")

        # Get config
        print(f"🔍 DEBUG create_verification_code: Getting email config...")
        config = get_email_config()
        print(f"🔍 DEBUG create_verification_code: config={config}")

        if not config:
            print(f"❌ DEBUG create_verification_code: No email config")
            return None
        
        print(f"🔍 DEBUG create_verification_code: Parsing config values...")
        expiry_minutes = int(config.get('otp_expiry_minutes', 15))
        max_attempts = int(config.get('max_otp_attempts', 3))
        print(f"🔍 DEBUG create_verification_code: expiry_minutes={expiry_minutes}, max_attempts={max_attempts}")

        # Generate OTP
        print(f"🔍 DEBUG create_verification_code: Generating OTP...")
        otp_code = generate_otp()
        expires_at = datetime.now() + timedelta(minutes=expiry_minutes)
        print(f"🔍 DEBUG create_verification_code: otp_code={otp_code}, expires_at={expires_at}")

        # Invalidate old codes for this email and type
        print(f"🔍 DEBUG create_verification_code: Invalidating old codes...")
        cursor.execute('''
            UPDATE "EmailVerificationCodes"
            SET is_used = TRUE
            WHERE email = %s AND code_type = %s AND is_used = FALSE
        ''', (email, code_type))
        print(f"🔍 DEBUG create_verification_code: Old codes invalidated")
        
        # Create new code
        print(f"🔍 DEBUG create_verification_code: Inserting new code into database...")

        # Fix IP address - convert empty string to None for INET type
        clean_ip_address = ip_address if ip_address and ip_address.strip() else None
        clean_user_agent = user_agent if user_agent and user_agent.strip() else None

        print(f"🔍 DEBUG create_verification_code: INSERT params: email={email}, code={otp_code}, type={code_type}, user_id={user_id}, expires={expires_at}, max_attempts={max_attempts}, ip={clean_ip_address}")

        cursor.execute('''
            INSERT INTO "EmailVerificationCodes"
            (email, code, code_type, user_id, expires_at, max_attempts, ip_address, user_agent)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING code_id
        ''', (email, otp_code, code_type, user_id, expires_at, max_attempts, clean_ip_address, clean_user_agent))

        print(f"🔍 DEBUG create_verification_code: INSERT executed, fetching code_id...")
        code_id = cursor.fetchone()[0]
        print(f"🔍 DEBUG create_verification_code: code_id={code_id}")

        print(f"🔍 DEBUG create_verification_code: Committing transaction...")
        conn.commit()
        print(f"🔍 DEBUG create_verification_code: Transaction committed")

        cursor.close()
        conn.close()
        print(f"🔍 DEBUG create_verification_code: Database connection closed")

        result = {
            'code_id': code_id,
            'code': otp_code,
            'expires_at': expires_at,
            'max_attempts': max_attempts
        }
        print(f"✅ DEBUG create_verification_code: SUCCESS - returning {result}")
        return result
        
    except Exception as e:
        print(f"❌ DEBUG create_verification_code: EXCEPTION occurred: {e}")
        import traceback
        traceback.print_exc()
        print(f"🔍 DEBUG create_verification_code: Rolling back transaction...")
        conn.rollback()
        cursor.close()
        conn.close()
        print(f"❌ DEBUG create_verification_code: FAILED - returning None")
        return None

def verify_otp_code(email, code, code_type):
    """Xác thực mã OTP"""
    conn = get_db_connection()
    if not conn:
        return {'success': False, 'error': 'Database connection failed'}
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Find active code
        cursor.execute('''
            SELECT code_id, user_id, attempts, max_attempts, expires_at, is_used
            FROM "EmailVerificationCodes"
            WHERE email = %s AND code = %s AND code_type = %s
            ORDER BY created_at DESC
            LIMIT 1
        ''', (email, code, code_type))
        
        code_record = cursor.fetchone()
        
        if not code_record:
            return {'success': False, 'error': 'Mã xác thực không hợp lệ'}
        
        # Check if already used
        if code_record['is_used']:
            return {'success': False, 'error': 'Mã xác thực đã được sử dụng'}
        
        # Check if expired
        if datetime.now() > code_record['expires_at']:
            return {'success': False, 'error': 'Mã xác thực đã hết hạn'}
        
        # Check attempts
        if code_record['attempts'] >= code_record['max_attempts']:
            return {'success': False, 'error': 'Đã vượt quá số lần thử cho phép'}
        
        # Mark as used
        cursor.execute('''
            UPDATE "EmailVerificationCodes"
            SET is_used = TRUE, used_at = CURRENT_TIMESTAMP
            WHERE code_id = %s
        ''', (code_record['code_id'],))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return {
            'success': True,
            'user_id': code_record['user_id'],
            'code_id': code_record['code_id']
        }
        
    except Exception as e:
        print(f"❌ Error verifying OTP: {e}")
        conn.rollback()
        return {'success': False, 'error': 'Lỗi hệ thống'}

def increment_otp_attempts(email, code, code_type):
    """Tăng số lần thử OTP"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE "EmailVerificationCodes"
            SET attempts = attempts + 1
            WHERE email = %s AND code = %s AND code_type = %s AND is_used = FALSE
        ''', (email, code, code_type))
        
        conn.commit()
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error incrementing attempts: {e}")
        conn.rollback()
        return False

def send_verification_email(email, otp_code, code_type):
    """Gửi email xác thực"""
    print(f"🔍 DEBUG send_verification_email: email={email}, code={otp_code}, type={code_type}")
    print(f"🔍 DEBUG MAILTRAP_AVAILABLE: {MAILTRAP_AVAILABLE}")

    if not MAILTRAP_AVAILABLE:
        print(f"📧 [DEMO] Would send OTP {otp_code} to {email} for {code_type}")
        return True

    config = get_email_config()
    print(f"🔍 DEBUG config in send_verification_email: {config}")

    if not config:
        print("❌ DEBUG: No email config available")
        return False

    try:
        # Initialize Mailtrap client
        from mailtrap import Mail, Address

        # Prepare email content
        if code_type == 'registration':
            subject = "Xác thực tài khoản SAPMMO"
            content = f"""
            <h2>Xác thực tài khoản</h2>
            <p>Chào bạn,</p>
            <p>Mã xác thực tài khoản của bạn là: <strong style="font-size: 24px; color: #00C6AE;">{otp_code}</strong></p>
            <p>Mã này có hiệu lực trong {config.get('otp_expiry_minutes', 15)} phút.</p>
            <p>Nếu bạn không đăng ký tài khoản này, vui lòng bỏ qua email này.</p>
            <br>
            <p>Trân trọng,<br>Đội ngũ SAPMMO</p>
            """
        elif code_type == 'password_reset':
            subject = "Đặt lại mật khẩu SAPMMO"
            content = f"""
            <h2>Đặt lại mật khẩu</h2>
            <p>Chào bạn,</p>
            <p>Mã xác thực để đặt lại mật khẩu của bạn là: <strong style="font-size: 24px; color: #00C6AE;">{otp_code}</strong></p>
            <p>Mã này có hiệu lực trong {config.get('otp_expiry_minutes', 15)} phút.</p>
            <p>Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này.</p>
            <br>
            <p>Trân trọng,<br>Đội ngũ SAPMMO</p>
            """
        else:
            return False

        # Create mail object
        mail = Mail(
            sender=Address(email=config['mailtrap_sender_email'], name=config['mailtrap_sender_name']),
            to=[Address(email=email)],
            subject=subject,
            html=content,
            text=f"Mã xác thực của bạn là: {otp_code}"
        )

        # Send email
        client = MailtrapClient(token=config['mailtrap_api_token'])
        response = client.send(mail)
        print(f"📧 Email sent successfully to {email}")
        return True

    except Exception as e:
        print(f"❌ Error sending email: {e}")
        import traceback
        traceback.print_exc()
        return False

def send_otp_email(email, code_type, user_id=None, ip_address=None, user_agent=None):
    """Tạo và gửi mã OTP qua email"""
    print(f"🔍 DEBUG send_otp_email: email={email}, type={code_type}")

    # Create verification code
    code_data = create_verification_code(email, code_type, user_id, ip_address, user_agent)
    print(f"🔍 DEBUG create_verification_code result: {code_data}")

    if not code_data:
        return {'success': False, 'error': 'Không thể tạo mã xác thực'}

    # Send email
    print(f"🔍 DEBUG: Calling send_verification_email with code: {code_data['code']}")
    email_sent = send_verification_email(email, code_data['code'], code_type)
    print(f"🔍 DEBUG send_verification_email result: {email_sent}")

    if not email_sent:
        return {'success': False, 'error': 'Không thể gửi email'}

    return {
        'success': True,
        'code_id': code_data['code_id'],
        'expires_at': code_data['expires_at'],
        'max_attempts': code_data['max_attempts']
    }

def send_test_email(email):
    """Gửi email test để kiểm tra cấu hình"""
    if not MAILTRAP_AVAILABLE:
        print(f"📧 [DEMO] Would send test email to {email}")
        return True

    config = get_email_config()
    if not config:
        return False

    try:
        # Initialize Mailtrap client
        from mailtrap import Mail, Address

        # Prepare test email content
        subject = "Test Email - SAPMMO"
        content = f"""
        <h2>🎉 Test Email Thành Công!</h2>
        <p>Chào bạn,</p>
        <p>Đây là email test từ hệ thống SAPMMO.</p>
        <p>Nếu bạn nhận được email này, có nghĩa là cấu hình Mailtrap đã hoạt động chính xác.</p>

        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <h4 style="color: #00C6AE; margin-top: 0;">📧 Thông tin cấu hình:</h4>
            <ul style="margin-bottom: 0;">
                <li><strong>Sender:</strong> {config['mailtrap_sender_name']} &lt;{config['mailtrap_sender_email']}&gt;</li>
                <li><strong>OTP Expiry:</strong> {config.get('otp_expiry_minutes', 15)} phút</li>
                <li><strong>Max Attempts:</strong> {config.get('max_otp_attempts', 3)} lần</li>
                <li><strong>Email Verification:</strong> {'Bật' if config.get('email_verification_enabled') == 'true' else 'Tắt'}</li>
            </ul>
        </div>

        <p>Bây giờ bạn có thể sử dụng tính năng xác thực email trong hệ thống.</p>
        <br>
        <p>Trân trọng,<br>Đội ngũ SAPMMO</p>
        <hr>
        <p style="font-size: 12px; color: #6c757d;">
            Email này được gửi từ chức năng test cấu hình email.
            Thời gian: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}
        </p>
        """

        # Create mail object
        mail = Mail(
            sender=Address(email=config['mailtrap_sender_email'], name=config['mailtrap_sender_name']),
            to=[Address(email=email)],
            subject=subject,
            html=content,
            text=f"Test email từ SAPMMO. Cấu hình Mailtrap hoạt động chính xác!"
        )

        # Send email
        client = MailtrapClient(token=config['mailtrap_api_token'])
        response = client.send(mail)
        print(f"📧 Test email sent successfully to {email}")
        return True

    except Exception as e:
        print(f"❌ Error sending test email: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_expired_codes():
    """Dọn dẹp mã xác thực đã hết hạn"""
    conn = get_db_connection()
    if not conn:
        return 0

    try:
        cursor = conn.cursor()
        cursor.execute('SELECT cleanup_expired_verification_codes()')
        deleted_count = cursor.fetchone()[0]

        conn.commit()
        cursor.close()
        conn.close()

        return deleted_count

    except Exception as e:
        print(f"❌ Error cleaning up codes: {e}")
        return 0
