#!/usr/bin/env python3
"""
Add missing columns to ProductFiles table: original_name, description, is_preview
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_missing_productfiles_columns():
    """Add missing columns to ProductFiles table"""
    
    print("🚀 Adding missing columns to ProductFiles table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current ProductFiles table structure
        print("\n🔧 Step 1: Checking current ProductFiles table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductFiles' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Current columns: {existing_column_names}")
        
        # Step 2: Add missing columns
        missing_columns = [
            ('original_name', 'VARCHAR(255)', 'Original filename when uploaded'),
            ('description', 'TEXT', 'File description'),
            ('is_preview', 'BOOLEAN DEFAULT FALSE', 'Whether file is a preview file'),
            ('upload_date', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP', 'Upload date (alias for uploaded_at)')
        ]
        
        for col_name, col_type, col_desc in missing_columns:
            if col_name not in existing_column_names:
                print(f"\n🔧 Adding {col_name} column...")
                
                cursor.execute(f'''
                    ALTER TABLE "ProductFiles" 
                    ADD COLUMN {col_name} {col_type};
                ''')
                
                print(f"  ✅ Added {col_name} column ({col_type}) - {col_desc}")
            else:
                print(f"\n✅ {col_name} column already exists")
        
        # Step 3: Update existing records
        print("\n🔧 Step 3: Updating existing records...")
        
        # Set original_name = filename for existing records
        cursor.execute('''
            UPDATE "ProductFiles" 
            SET original_name = filename 
            WHERE original_name IS NULL AND filename IS NOT NULL;
        ''')
        updated_original = cursor.rowcount
        print(f"  ✅ Updated {updated_original} records: original_name = filename")
        
        # Set upload_date = uploaded_at for existing records
        cursor.execute('''
            UPDATE "ProductFiles" 
            SET upload_date = uploaded_at 
            WHERE upload_date IS NULL AND uploaded_at IS NOT NULL;
        ''')
        updated_date = cursor.rowcount
        print(f"  ✅ Updated {updated_date} records: upload_date = uploaded_at")
        
        # Step 4: Create indexes for performance
        print("\n🔧 Step 4: Creating indexes...")
        
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_product_files_original_name ON "ProductFiles"(original_name);',
            'CREATE INDEX IF NOT EXISTS idx_product_files_is_preview ON "ProductFiles"(is_preview);',
            'CREATE INDEX IF NOT EXISTS idx_product_files_upload_date ON "ProductFiles"(upload_date);'
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
                index_name = index_sql.split('idx_')[1].split()[0] if 'idx_' in index_sql else 'index'
                print(f"  ✅ Created index: {index_name}")
            except Exception as e:
                print(f"  ⚠️  Index creation: {e}")
        
        # Step 5: Verify final structure
        print("\n🔧 Step 5: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductFiles' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Final ProductFiles columns:")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 6: Test the problematic query
        print("\n🔧 Step 6: Testing the problematic query...")
        
        try:
            cursor.execute('''
                SELECT product_id, filename, file_name, original_name, file_size, 
                       file_type, upload_date, is_preview, description
                FROM "ProductFiles" 
                WHERE product_id IS NOT NULL
                LIMIT 3;
            ''')
            
            test_rows = cursor.fetchall()
            print(f"  ✅ Query test successful - {len(test_rows)} rows returned")
            
        except Exception as e:
            print(f"  ❌ Query test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Migration completed successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test product file upload functionality")
        print("   3. Test product creation with files")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_missing_productfiles_columns()
    sys.exit(0 if success else 1)
