#!/usr/bin/env python3
"""
Create database schema for Video Links system
"""

import psycopg2
import psycopg2.extras
from datetime import datetime

def get_db_connection():
    """Kết nối database PostgreSQL local"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def create_video_links_table(conn):
    """Tạo bảng VideoLinks để quản lý video links"""
    try:
        cursor = conn.cursor()
        
        print("🔧 Creating VideoLinks table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "VideoLinks" (
                link_id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                drive_url TEXT NOT NULL,
                video_count INTEGER NOT NULL DEFAULT 0,
                video_type VARCHAR(100),
                description TEXT,
                status VARCHAR(20) DEFAULT 'available',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER REFERENCES "Users"(user_id),
                
                CONSTRAINT check_status CHECK (status IN ('available', 'sold', 'reserved', 'inactive'))
            );
        ''')
        
        # Create indexes
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_videolinks_status ON "VideoLinks"(status);')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_videolinks_video_count ON "VideoLinks"(video_count);')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_videolinks_video_type ON "VideoLinks"(video_type);')
        
        print("✅ VideoLinks table created successfully")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating VideoLinks table: {e}")
        return False

def create_product_video_links_table(conn):
    """Tạo bảng mapping giữa Products và VideoLinks"""
    try:
        cursor = conn.cursor()
        
        print("🔧 Creating ProductVideoLinks table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "ProductVideoLinks" (
                mapping_id SERIAL PRIMARY KEY,
                product_id INTEGER NOT NULL REFERENCES "Products"(product_id) ON DELETE CASCADE,
                link_id INTEGER NOT NULL REFERENCES "VideoLinks"(link_id) ON DELETE CASCADE,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                UNIQUE(product_id, link_id)
            );
        ''')
        
        # Create indexes
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_productvideo_product ON "ProductVideoLinks"(product_id);')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_productvideo_link ON "ProductVideoLinks"(link_id);')
        
        print("✅ ProductVideoLinks table created successfully")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating ProductVideoLinks table: {e}")
        return False

def create_user_video_links_table(conn):
    """Tạo bảng lưu video links đã mua của user"""
    try:
        cursor = conn.cursor()
        
        print("🔧 Creating UserVideoLinks table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "UserVideoLinks" (
                user_link_id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL REFERENCES "Users"(user_id),
                order_id INTEGER NOT NULL REFERENCES "Orders"(order_id),
                product_id INTEGER NOT NULL REFERENCES "Products"(product_id),
                link_id INTEGER NOT NULL REFERENCES "VideoLinks"(link_id),
                purchased_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                
                UNIQUE(user_id, order_id, link_id)
            );
        ''')
        
        # Create indexes
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_uservideo_user ON "UserVideoLinks"(user_id);')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_uservideo_order ON "UserVideoLinks"(order_id);')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_uservideo_product ON "UserVideoLinks"(product_id);')
        
        print("✅ UserVideoLinks table created successfully")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating UserVideoLinks table: {e}")
        return False

def add_sample_video_links(conn):
    """Thêm sample video links để test"""
    try:
        cursor = conn.cursor()
        
        print("📝 Adding sample video links...")
        
        sample_links = [
            {
                'name': 'Link 0901',
                'drive_url': 'https://drive.google.com/drive/folders/sample001',
                'video_count': 50,
                'video_type': 'Nuôi kênh TikTok',
                'description': '50 video nuôi kênh TikTok chất lượng cao, thời lượng 15-30s'
            },
            {
                'name': 'Link 0902', 
                'drive_url': 'https://drive.google.com/drive/folders/sample002',
                'video_count': 100,
                'video_type': 'Viral Content',
                'description': '100 video viral content đa dạng chủ đề'
            },
            {
                'name': 'Link 0903',
                'drive_url': 'https://drive.google.com/drive/folders/sample003',
                'video_count': 30,
                'video_type': 'Dance Videos',
                'description': '30 video dance trending trên TikTok'
            },
            {
                'name': 'Link 0904',
                'drive_url': 'https://drive.google.com/drive/folders/sample004',
                'video_count': 75,
                'video_type': 'Lifestyle',
                'description': '75 video lifestyle phong cách trẻ trung'
            },
            {
                'name': 'Link 0905',
                'drive_url': 'https://drive.google.com/drive/folders/sample005',
                'video_count': 50,
                'video_type': 'Nuôi kênh TikTok',
                'description': '50 video nuôi kênh TikTok batch 2'
            }
        ]
        
        for link_data in sample_links:
            cursor.execute('''
                INSERT INTO "VideoLinks" (name, drive_url, video_count, video_type, description, status)
                VALUES (%(name)s, %(drive_url)s, %(video_count)s, %(video_type)s, %(description)s, 'available')
                ON CONFLICT (name) DO NOTHING
            ''', link_data)
            
            print(f"  ✅ Added: {link_data['name']} ({link_data['video_count']} videos)")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error adding sample links: {e}")
        return False

def verify_tables(conn):
    """Kiểm tra các bảng đã được tạo"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n🔍 Verifying tables...")
        
        tables = ['VideoLinks', 'ProductVideoLinks', 'UserVideoLinks']
        
        for table in tables:
            cursor.execute(f'''
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_name = '{table}' AND table_schema = 'public'
            ''')
            
            exists = cursor.fetchone()[0] > 0
            print(f"  {'✅' if exists else '❌'} {table}: {'EXISTS' if exists else 'NOT FOUND'}")
        
        # Check sample data
        cursor.execute('SELECT COUNT(*) FROM "VideoLinks"')
        link_count = cursor.fetchone()[0]
        print(f"  📊 VideoLinks records: {link_count}")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verifying tables: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Creating Video Links System Database Schema")
    print("=" * 60)
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database")
        return
    
    try:
        # Create tables
        success1 = create_video_links_table(conn)
        success2 = create_product_video_links_table(conn)
        success3 = create_user_video_links_table(conn)
        
        if success1 and success2 and success3:
            # Add sample data
            add_sample_video_links(conn)
            
            # Commit changes
            conn.commit()
            print("\n✅ Database schema created successfully!")
            
            # Verify
            verify_tables(conn)
            
            print("\n🎉 Video Links System ready!")
            print("📋 Next steps:")
            print("  1. Create admin interface for video links management")
            print("  2. Add 'videos' product type to marketplace")
            print("  3. Implement purchase flow")
            print("  4. Add videos tab to user orders")
            
        else:
            conn.rollback()
            print("\n❌ Failed to create database schema")
            
    except Exception as e:
        conn.rollback()
        print(f"\n❌ Unexpected error: {e}")
        
    finally:
        conn.close()
        print("\n🔒 Database connection closed")

if __name__ == "__main__":
    main()
