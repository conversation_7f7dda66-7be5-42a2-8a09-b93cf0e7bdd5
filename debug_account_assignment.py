#!/usr/bin/env python3
"""
Debug account assignment issue for product purchase
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def debug_account_assignment():
    """Debug account assignment for product purchase"""
    
    print("🚀 Debugging account assignment issue...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Find the product
        print("\n🔧 Step 1: Finding 'Tiktok 1k Follower' product...")
        
        cursor.execute('''
            SELECT product_id, name, product_type, stock, unlimited_stock, status
            FROM "Products" 
            WHERE name ILIKE '%Tiktok 1k Follower%' OR name ILIKE '%1k Follower%'
            ORDER BY product_id DESC;
        ''')
        
        products = cursor.fetchall()
        if products:
            for product in products:
                print(f"📦 Product ID: {product[0]}")
                print(f"   Name: {product[1]}")
                print(f"   Type: {product[2]}")
                print(f"   Stock: {product[3]}")
                print(f"   Unlimited: {product[4]}")
                print(f"   Status: {product[5]}")
                print()
        else:
            print("❌ No products found with that name")
            return False
        
        # Use the first product for debugging
        product_id = products[0][0]
        product_name = products[0][1]
        product_type = products[0][2]
        
        print(f"🎯 Debugging product: {product_name} (ID: {product_id})")
        
        # Step 2: Check if it's account type
        if product_type != 'account':
            print(f"❌ Product type is '{product_type}', not 'account'")
            print("💡 Account assignment only works for product_type = 'account'")
            return False
        
        # Step 3: Check AccountPackages
        print("\n🔧 Step 3: Checking AccountPackages...")
        
        cursor.execute('''
            SELECT package_id, package_name, assigned_count, criteria
            FROM "AccountPackages" 
            WHERE product_id = %s;
        ''', (product_id,))
        
        packages = cursor.fetchall()
        if packages:
            for package in packages:
                print(f"📋 Package ID: {package[0]}")
                print(f"   Name: {package[1]}")
                print(f"   Assigned Count: {package[2]}")
                print(f"   Criteria: {package[3]}")
                print()
        else:
            print("❌ No AccountPackages found for this product")
            print("💡 Need to create AccountPackage record")
            return False
        
        package_id = packages[0][0]
        
        # Step 4: Check PackageAccounts
        print("\n🔧 Step 4: Checking PackageAccounts...")
        
        cursor.execute('''
            SELECT pa.id, pa.account_id, pa.is_sold, pa.sold_to_user_id,
                   a.account_name, a.marketplace_status
            FROM "PackageAccounts" pa
            JOIN "Accounts" a ON pa.account_id = a.account_id
            WHERE pa.package_id = %s
            ORDER BY pa.is_sold, pa.id;
        ''', (package_id,))
        
        package_accounts = cursor.fetchall()
        if package_accounts:
            available_count = 0
            sold_count = 0
            
            print(f"📊 PackageAccounts for package {package_id}:")
            for acc in package_accounts:
                status = "SOLD" if acc[2] else "AVAILABLE"
                if not acc[2]:
                    available_count += 1
                else:
                    sold_count += 1
                    
                print(f"   - Account: {acc[4]} (ID: {acc[1]}) - {status}")
                if acc[2]:
                    print(f"     Sold to user: {acc[3]}")
                print(f"     Marketplace status: {acc[5]}")
            
            print(f"\n📈 Summary:")
            print(f"   Available accounts: {available_count}")
            print(f"   Sold accounts: {sold_count}")
            print(f"   Total accounts: {len(package_accounts)}")
            
            if available_count == 0:
                print("❌ NO AVAILABLE ACCOUNTS - This is the problem!")
                print("💡 Need to add more accounts to this package")
            else:
                print("✅ Available accounts exist")
                
        else:
            print("❌ No PackageAccounts found")
            print("💡 No accounts assigned to this package")
        
        # Step 5: Check general Accounts table
        print("\n🔧 Step 5: Checking general Accounts with package_id...")
        
        cursor.execute('''
            SELECT account_id, account_name, marketplace_status, package_id, is_sold
            FROM "Accounts" 
            WHERE package_id = %s
            ORDER BY is_sold, account_id;
        ''', (product_id,))  # Note: using product_id as package_id
        
        general_accounts = cursor.fetchall()
        if general_accounts:
            available_general = 0
            sold_general = 0
            
            print(f"📊 Accounts with package_id = {product_id}:")
            for acc in general_accounts:
                status = "SOLD" if acc[4] else "AVAILABLE"
                if not acc[4]:
                    available_general += 1
                else:
                    sold_general += 1
                    
                print(f"   - {acc[1]} (ID: {acc[0]}) - {status}")
                print(f"     Marketplace status: {acc[2]}")
                print(f"     Package ID: {acc[3]}")
            
            print(f"\n📈 General Accounts Summary:")
            print(f"   Available: {available_general}")
            print(f"   Sold: {sold_general}")
            print(f"   Total: {len(general_accounts)}")
            
        else:
            print("❌ No accounts found with this package_id in Accounts table")
        
        # Step 6: Check available accounts without package assignment
        print("\n🔧 Step 6: Checking unassigned available accounts...")
        
        cursor.execute('''
            SELECT COUNT(*) 
            FROM "Accounts" 
            WHERE (marketplace_status = 'available' OR marketplace_status IS NULL)
            AND (package_id IS NULL)
            AND (is_sold = false OR is_sold IS NULL);
        ''')
        
        unassigned_count = cursor.fetchone()[0]
        print(f"📊 Unassigned available accounts: {unassigned_count}")
        
        if unassigned_count > 0:
            print("💡 Solution: Assign some unassigned accounts to this package")
        
        # Step 7: Show solution steps
        print(f"\n🔧 Step 7: Solution steps...")
        
        if not packages:
            print("1. Create AccountPackage record for this product")
        
        if not package_accounts or available_count == 0:
            print("2. Assign accounts to this package:")
            print(f"   - Go to admin edit product page for '{product_name}'")
            print("   - Add accounts to the package")
            print("   - Or run SQL to assign accounts:")
            print(f"     UPDATE \"Accounts\" SET package_id = {product_id}, marketplace_status = 'reserved'")
            print(f"     WHERE account_id IN (SELECT account_id FROM \"Accounts\" WHERE marketplace_status = 'available' LIMIT 5);")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = debug_account_assignment()
    sys.exit(0 if success else 1)
