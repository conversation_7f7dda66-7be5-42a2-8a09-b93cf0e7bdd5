#!/usr/bin/env python3
"""
Deploy Email Verification System to Production Server
"""

import psycopg2
import psycopg2.extras
from db_config import PG_CONFIG

def get_db_connection():
    """Kết nối database PostgreSQL từ config"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def deploy_email_verification_system():
    """Deploy email verification system to production"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        print("🚀 Deploying Email Verification System to Production")
        print("=" * 70)

        # Step 0: Update SystemConfig table structure to match local
        print("📋 0. Updating SystemConfig table structure...")

        # Check if SystemConfig uses config_id or id
        cursor.execute('''
            SELECT column_name FROM information_schema.columns
            WHERE table_name = 'SystemConfig' AND column_name IN ('config_id', 'id')
        ''')

        config_columns = [row[0] for row in cursor.fetchall()]

        if 'config_id' in config_columns and 'id' not in config_columns:
            print("   🔄 Renaming config_id → id to match local structure...")
            cursor.execute('ALTER TABLE "SystemConfig" RENAME COLUMN config_id TO id')
            print("   ✅ Renamed config_id → id")
        elif 'id' in config_columns:
            print("   ℹ️ SystemConfig already uses 'id' column")
        else:
            print("   ❌ SystemConfig structure unclear")
            return False

        # Step 1: Add email verification columns to Users table
        print("📋 1. Updating Users table...")
        
        # Check if columns exist first
        cursor.execute('''
            SELECT column_name FROM information_schema.columns 
            WHERE table_name = 'Users' AND column_name IN ('email_verified', 'email_verified_at')
        ''')
        existing_columns = [row[0] for row in cursor.fetchall()]
        
        if 'email_verified' not in existing_columns:
            cursor.execute('ALTER TABLE "Users" ADD COLUMN email_verified BOOLEAN DEFAULT FALSE')
            print("   ✅ Added email_verified column")
        else:
            print("   ℹ️ email_verified column already exists")
        
        if 'email_verified_at' not in existing_columns:
            cursor.execute('ALTER TABLE "Users" ADD COLUMN email_verified_at TIMESTAMP NULL')
            print("   ✅ Added email_verified_at column")
        else:
            print("   ℹ️ email_verified_at column already exists")
        
        # Ensure email column is NOT NULL
        cursor.execute('''
            SELECT is_nullable FROM information_schema.columns 
            WHERE table_name = 'Users' AND column_name = 'email'
        ''')
        email_nullable = cursor.fetchone()
        
        if email_nullable and email_nullable[0] == 'YES':
            # Update NULL emails with placeholder first
            cursor.execute('SELECT COUNT(*) FROM "Users" WHERE email IS NULL OR email = \'\'')
            null_email_count = cursor.fetchone()[0]
            
            if null_email_count > 0:
                print(f"   ⚠️ Found {null_email_count} users with NULL/empty email")
                cursor.execute('''
                    UPDATE "Users" 
                    SET email = 'user' || user_id || '@placeholder.local'
                    WHERE email IS NULL OR email = ''
                ''')
                print(f"   ✅ Updated {null_email_count} users with placeholder emails")
            
            # Make email required
            cursor.execute('ALTER TABLE "Users" ALTER COLUMN email SET NOT NULL')
            print("   ✅ Made email column required")
        else:
            print("   ℹ️ email column already NOT NULL")
        
        # Step 2: Create EmailVerificationCodes table
        print("\n📋 2. Creating EmailVerificationCodes table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "EmailVerificationCodes" (
                code_id SERIAL PRIMARY KEY,
                email VARCHAR(255) NOT NULL,
                code VARCHAR(6) NOT NULL,
                code_type VARCHAR(20) NOT NULL, -- 'registration' or 'password_reset'
                user_id INTEGER REFERENCES "Users"(user_id) ON DELETE CASCADE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                used_at TIMESTAMP NULL,
                attempts INTEGER DEFAULT 0,
                max_attempts INTEGER DEFAULT 3,
                is_used BOOLEAN DEFAULT FALSE,
                ip_address INET,
                user_agent TEXT
            )
        ''')
        print("   ✅ EmailVerificationCodes table created")
        
        # Step 3: Create indexes for performance
        print("\n📋 3. Creating performance indexes...")
        
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_email_verification_codes_email ON "EmailVerificationCodes"(email)',
            'CREATE INDEX IF NOT EXISTS idx_email_verification_codes_code ON "EmailVerificationCodes"(code)',
            'CREATE INDEX IF NOT EXISTS idx_email_verification_codes_type ON "EmailVerificationCodes"(code_type)',
            'CREATE INDEX IF NOT EXISTS idx_email_verification_codes_expires ON "EmailVerificationCodes"(expires_at)',
            'CREATE INDEX IF NOT EXISTS idx_email_verification_codes_used ON "EmailVerificationCodes"(is_used)',
            'CREATE INDEX IF NOT EXISTS idx_users_email_verified ON "Users"(email_verified)',
            'CREATE INDEX IF NOT EXISTS idx_users_email ON "Users"(email)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("   ✅ All performance indexes created")
        
        # Step 4: Create cleanup function for expired codes
        print("\n📋 4. Creating cleanup function...")
        
        cursor.execute('''
            CREATE OR REPLACE FUNCTION cleanup_expired_verification_codes()
            RETURNS INTEGER AS $$
            DECLARE
                deleted_count INTEGER;
            BEGIN
                DELETE FROM "EmailVerificationCodes"
                WHERE expires_at < CURRENT_TIMESTAMP
                AND is_used = FALSE;
                
                GET DIAGNOSTICS deleted_count = ROW_COUNT;
                RETURN deleted_count;
            END;
            $$ LANGUAGE plpgsql;
        ''')
        print("   ✅ Cleanup function created")
        
        # Step 5: Add email configuration to SystemConfig
        print("\n📋 5. Adding email configurations...")
        
        email_configs = [
            {
                'key': 'mailtrap_api_token',
                'value': 'YOUR_MAILTRAP_API_TOKEN_HERE',
                'description': 'Mailtrap API Token for sending emails'
            },
            {
                'key': 'mailtrap_sender_email',
                'value': '<EMAIL>',
                'description': 'Sender email address for Mailtrap'
            },
            {
                'key': 'mailtrap_sender_name',
                'value': 'SAPMMO',
                'description': 'Sender name for emails'
            },
            {
                'key': 'email_verification_enabled',
                'value': 'true',
                'description': 'Enable email verification for new registrations'
            },
            {
                'key': 'otp_expiry_minutes',
                'value': '15',
                'description': 'OTP expiry time in minutes'
            },
            {
                'key': 'max_otp_attempts',
                'value': '3',
                'description': 'Maximum OTP verification attempts'
            }
        ]
        
        added_configs = 0
        for config in email_configs:
            # Check if config already exists (now using 'id' column after rename)
            cursor.execute('SELECT id FROM "SystemConfig" WHERE config_key = %s', (config['key'],))
            if not cursor.fetchone():
                cursor.execute('''
                    INSERT INTO "SystemConfig" (config_key, config_value, description, created_at, updated_at)
                    VALUES (%s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ''', (config['key'], config['value'], config['description']))
                added_configs += 1
        
        if added_configs > 0:
            print(f"   ✅ Added {added_configs} email configurations")
        else:
            print("   ℹ️ All email configurations already exist")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("\n🎉 Email Verification System deployed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error deploying email verification system: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        return False

def verify_deployment():
    """Verify deployment was successful"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n🔍 Verifying Deployment")
        print("=" * 50)
        
        # Check Users table columns
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' 
            AND column_name IN ('email', 'email_verified', 'email_verified_at')
            ORDER BY column_name
        ''')
        
        print("📋 Users table email columns:")
        for col in cursor.fetchall():
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            print(f"   {col['column_name']}: {col['data_type']} {nullable}")
        
        # Check EmailVerificationCodes table
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'EmailVerificationCodes'
            )
        ''')
        
        table_exists = cursor.fetchone()[0]
        if table_exists:
            print("✅ EmailVerificationCodes table exists")
            
            # Count indexes
            cursor.execute('''
                SELECT COUNT(*) FROM pg_indexes 
                WHERE tablename = 'EmailVerificationCodes'
            ''')
            index_count = cursor.fetchone()[0]
            print(f"✅ EmailVerificationCodes has {index_count} indexes")
        else:
            print("❌ EmailVerificationCodes table missing")
            return False
        
        # Check cleanup function
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM pg_proc 
                WHERE proname = 'cleanup_expired_verification_codes'
            )
        ''')
        
        function_exists = cursor.fetchone()[0]
        if function_exists:
            print("✅ Cleanup function exists")
        else:
            print("❌ Cleanup function missing")
            return False
        
        # Check email configs
        cursor.execute('''
            SELECT COUNT(*) FROM "SystemConfig"
            WHERE config_key IN (
                'mailtrap_api_token',
                'mailtrap_sender_email', 
                'mailtrap_sender_name',
                'email_verification_enabled',
                'otp_expiry_minutes',
                'max_otp_attempts'
            )
        ''')
        
        config_count = cursor.fetchone()[0]
        print(f"✅ Found {config_count}/6 email configurations")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verifying deployment: {e}")
        return False

def show_post_deployment_instructions():
    """Show instructions after deployment"""
    print("\n📋 Post-Deployment Instructions")
    print("=" * 50)
    print("1. 📧 Update email configurations:")
    print("   - Set your real Mailtrap API token")
    print("   - Update sender email domain")
    print("   - Configure sender name")
    print()
    print("2. 📁 Upload Python files:")
    print("   - email_verification_service.py")
    print("   - Updated mip_system.py")
    print()
    print("3. 📄 Upload HTML templates:")
    print("   - templates/verify_email.html")
    print("   - templates/forgot_password.html")
    print("   - templates/reset_password.html")
    print("   - Updated templates/login.html")
    print("   - Updated templates/register.html")
    print()
    print("4. 📦 Install Python package:")
    print("   pip install mailtrap")
    print()
    print("5. 🧪 Test email functionality:")
    print("   - Test email config in /config")
    print("   - Test registration flow")
    print("   - Test forgot password flow")
    print()
    print("6. 🔧 Optional: Set up cron job for cleanup:")
    print("   # Clean expired OTP codes daily")
    print("   0 2 * * * psql -d your_db -c \"SELECT cleanup_expired_verification_codes();\"")

def main():
    """Main deployment function"""
    print("🚀 Email Verification System Deployment")
    print("=" * 70)
    
    # Deploy system
    deploy_success = deploy_email_verification_system()
    
    if deploy_success:
        # Verify deployment
        verify_success = verify_deployment()
        
        if verify_success:
            print("\n🎉 Email Verification System deployment completed successfully!")
            show_post_deployment_instructions()
        else:
            print("\n⚠️ Deployment completed but verification failed")
    else:
        print("\n❌ Deployment failed!")

if __name__ == "__main__":
    main()
