#!/usr/bin/env python3
"""
Add phone and email columns to Users table
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_phone_email_to_users():
    """Add phone and email columns to Users table"""
    
    print("🚀 Adding phone and email columns to Users table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current Users table structure
        print("\n🔧 Step 1: Checking current Users table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Current columns: {existing_column_names}")
        
        # Step 2: Add phone column if not exists
        if 'phone' not in existing_column_names:
            print("\n🔧 Step 2: Adding phone column...")
            
            cursor.execute('''
                ALTER TABLE "Users" 
                ADD COLUMN phone varchar(20);
            ''')
            
            print("  ✅ Added phone column (varchar(20))")
        else:
            print("\n✅ Step 2: phone column already exists")
        
        # Step 3: Add email column if not exists
        if 'email' not in existing_column_names:
            print("\n🔧 Step 3: Adding email column...")
            
            cursor.execute('''
                ALTER TABLE "Users" 
                ADD COLUMN email varchar(255);
            ''')
            
            print("  ✅ Added email column (varchar(255))")
        else:
            print("\n✅ Step 3: email column already exists")
        
        # Step 4: Add comments
        print("\n🔧 Step 4: Adding column comments...")
        
        cursor.execute('''
            COMMENT ON COLUMN "Users"."phone" IS 'Số điện thoại của user (format: +84xxxxxxxxx)';
        ''')
        print("  ✅ Added comment for phone column")
        
        cursor.execute('''
            COMMENT ON COLUMN "Users"."email" IS 'Email của user';
        ''')
        print("  ✅ Added comment for email column")
        
        # Step 5: Create indexes for performance
        print("\n🔧 Step 5: Creating indexes...")
        
        try:
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_users_phone 
                ON "Users"(phone) 
                WHERE phone IS NOT NULL;
            ''')
            print("  ✅ Created index: idx_users_phone")
        except Exception as e:
            print(f"  ⚠️  Phone index creation: {e}")
        
        try:
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_users_email 
                ON "Users"(email) 
                WHERE email IS NOT NULL;
            ''')
            print("  ✅ Created index: idx_users_email")
        except Exception as e:
            print(f"  ⚠️  Email index creation: {e}")
        
        # Step 6: Add unique constraints (optional - for preventing duplicates)
        print("\n🔧 Step 6: Adding unique constraints...")
        
        try:
            cursor.execute('''
                ALTER TABLE "Users" ADD CONSTRAINT "Users_phone_unique" 
                UNIQUE ("phone");
            ''')
            print("  ✅ Added unique constraint on phone")
        except Exception as e:
            print(f"  ⚠️  Phone unique constraint: {e}")
        
        try:
            cursor.execute('''
                ALTER TABLE "Users" ADD CONSTRAINT "Users_email_unique" 
                UNIQUE ("email");
            ''')
            print("  ✅ Added unique constraint on email")
        except Exception as e:
            print(f"  ⚠️  Email unique constraint: {e}")
        
        # Step 7: Verify final structure
        print("\n🔧 Step 7: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            AND column_name IN ('phone', 'email', 'username', 'unit_code')
            ORDER BY column_name;
        ''')
        
        user_columns = cursor.fetchall()
        print(f"📋 User contact columns:")
        for col in user_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 8: Test INSERT query with new columns
        print("\n🔧 Step 8: Testing INSERT query with new columns...")
        
        try:
            # Test the INSERT query structure (without actually inserting)
            cursor.execute('''
                SELECT 1 FROM "Users" 
                WHERE username IS NOT NULL 
                AND phone IS NOT NULL 
                AND email IS NOT NULL 
                LIMIT 1;
            ''')
            
            print("  ✅ INSERT query structure test successful")
            
        except Exception as e:
            print(f"  ❌ INSERT query test failed: {e}")
        
        # Step 9: Show sample queries that should work
        print("\n🔧 Step 9: Sample queries that should work...")
        
        sample_queries = [
            "INSERT INTO \"Users\" (username, password_hash, role, email, phone, unit_code) VALUES (...)",
            "SELECT username, email, phone, unit_code FROM \"Users\" WHERE email IS NOT NULL",
            "UPDATE \"Users\" SET phone = '+84987654321' WHERE user_id = 1"
        ]
        
        for i, query in enumerate(sample_queries, 1):
            print(f"  {i}. {query}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Phone and email columns added successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test user registration with phone/email")
        print("   3. Test admin create user with phone/email")
        print("   4. Update UI forms to include phone/email fields")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_phone_email_to_users()
    sys.exit(0 if success else 1)
