#!/usr/bin/env python3
"""
Test the exact query that's failing on server
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def test_exact_query():
    """Test the exact query from mip_system.py"""
    
    print("🔍 Testing exact query from mip_system.py...")
    print("=" * 60)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Test the exact query from line 14637
        print("\n🔍 Testing list discounts query...")
        try:
            cursor.execute('''
                SELECT d.discount_id, d.code, d.description, d.discount_type, d.value,
                       d.min_order_value, d.usage_limit, d.used_count, d.is_active,
                       d.start_date, d.end_date, d.created_at,
                       COALESCE(SUM(o.discount_amount), 0) as total_savings
                FROM "Discounts" d
                LEFT JOIN "Orders" o ON d.code = o.discount_code AND o.status = 'completed'
                WHERE 1=1
                GROUP BY d.discount_id
                ORDER BY d.created_at DESC
                LIMIT 5;
            ''')
            
            results = cursor.fetchall()
            print(f"✅ List query successful - found {len(results)} discounts")
            
            for row in results:
                print(f"  - {row[1]}: {row[2]} ({row[3]} {row[4]})")
                
        except Exception as e:
            print(f"❌ List query failed: {e}")
            
            # Check which columns actually exist
            cursor.execute('''
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'Discounts' AND table_schema = 'public'
                ORDER BY ordinal_position;
            ''')
            
            actual_columns = [col[0] for col in cursor.fetchall()]
            print(f"📋 Actual columns: {actual_columns}")
            
            # Check which columns are missing
            expected_columns = ['discount_id', 'code', 'description', 'discount_type', 'value', 
                              'min_order_value', 'usage_limit', 'used_count', 'is_active',
                              'start_date', 'end_date', 'created_at']
            
            missing = [col for col in expected_columns if col not in actual_columns]
            if missing:
                print(f"❌ Missing columns: {missing}")
            else:
                print("✅ All expected columns exist")
        
        # Test create discount validation
        print("\n🔍 Testing create discount validation...")
        
        # Test the validation that might cause "could not convert string to float"
        test_values = ['', '0', '10.5', 'abc', None]
        
        for test_val in test_values:
            try:
                if test_val == '' or test_val is None:
                    print(f"⚠️  Empty value '{test_val}' - this might cause float conversion error")
                else:
                    float_val = float(test_val)
                    print(f"✅ '{test_val}' converts to {float_val}")
            except ValueError as e:
                print(f"❌ '{test_val}' conversion error: {e}")
        
        # Test form data processing
        print("\n🔍 Testing form data processing...")
        
        # Simulate form data that might be empty
        form_data = {
            'code': 'TEST123',
            'description': 'Test discount',
            'discount_type': 'percentage',
            'value': '',  # This might cause the error
            'min_order_value': '100000',
            'usage_limit': '',  # This might also cause error
            'is_active': 'true'
        }
        
        for key, value in form_data.items():
            if key in ['value', 'min_order_value', 'usage_limit'] and value == '':
                print(f"❌ {key}: empty string '{value}' will cause float conversion error")
            elif key in ['value', 'min_order_value', 'usage_limit']:
                try:
                    float_val = float(value) if value else 0.0
                    print(f"✅ {key}: '{value}' converts to {float_val}")
                except ValueError as e:
                    print(f"❌ {key}: '{value}' conversion error: {e}")
            else:
                print(f"✅ {key}: '{value}' (string field)")
        
        cursor.close()
        conn.close()
        
        print(f"\n📝 Recommendations:")
        print("1. Add validation in form processing to handle empty strings")
        print("2. Convert empty strings to 0 or None before float conversion")
        print("3. Add client-side validation to prevent empty numeric fields")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_exact_query()
    sys.exit(0 if success else 1)
