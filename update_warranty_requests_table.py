#!/usr/bin/env python3
"""
Update WarrantyRequests table to match local structure
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def update_warranty_requests_table():
    """Update WarrantyRequests table to match local structure"""
    
    print("🚀 Updating WarrantyRequests table to match local structure...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current structure
        print("\n🔧 Step 1: Checking current WarrantyRequests structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'WarrantyRequests' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Current columns: {existing_column_names}")
        
        # Step 2: Add missing columns
        print(f"\n🔧 Step 2: Adding missing columns...")
        
        missing_columns = [
            ('order_id', 'int4', 'ID của order'),
            ('evidence_image', 'varchar(255)', 'Hình ảnh bằng chứng'),
            ('admin_response', 'text', 'Phản hồi của admin'),
            ('admin_evidence_image', 'varchar(255)', 'Hình ảnh bằng chứng của admin'),
            ('processed_by', 'int4', 'ID admin xử lý'),
            ('processed_at', 'timestamp(6)', 'Thời gian xử lý'),
            ('expires_at', 'timestamp(6)', 'Thời gian hết hạn')
        ]
        
        added_count = 0
        for col_name, col_type, col_comment in missing_columns:
            if col_name not in existing_column_names:
                try:
                    cursor.execute(f'ALTER TABLE "WarrantyRequests" ADD COLUMN {col_name} {col_type};')
                    cursor.execute(f'COMMENT ON COLUMN "WarrantyRequests"."{col_name}" IS \'{col_comment}\';')
                    print(f"  ✅ Added: {col_name} ({col_type})")
                    added_count += 1
                except Exception as e:
                    print(f"  ❌ Failed to add {col_name}: {e}")
            else:
                print(f"  ⏭️  Exists: {col_name}")
        
        # Step 3: Add id column if not exists (for local compatibility)
        if 'id' not in existing_column_names:
            print(f"\n🔧 Step 3: Adding id column for compatibility...")
            try:
                # Add id column
                cursor.execute('ALTER TABLE "WarrantyRequests" ADD COLUMN id int4;')
                
                # Update id values to match warranty_id
                cursor.execute('UPDATE "WarrantyRequests" SET id = warranty_id;')
                
                # Set id as NOT NULL
                cursor.execute('ALTER TABLE "WarrantyRequests" ALTER COLUMN id SET NOT NULL;')
                
                print("  ✅ Added id column and synced with warranty_id")
            except Exception as e:
                print(f"  ❌ Failed to add id column: {e}")
        else:
            print(f"\n✅ Step 3: id column already exists")
        
        # Step 4: Update status column to match local format
        print(f"\n🔧 Step 4: Updating status column...")
        
        try:
            # Update status values to match local format
            status_mapping = {
                'pending': 'Pending',
                'approved': 'Approved', 
                'rejected': 'Rejected',
                'completed': 'Completed'
            }
            
            for old_status, new_status in status_mapping.items():
                cursor.execute(f'UPDATE "WarrantyRequests" SET status = %s WHERE status = %s;', (new_status, old_status))
                updated = cursor.rowcount
                if updated > 0:
                    print(f"  ✅ Updated {updated} records: {old_status} → {new_status}")
            
            # Update column length to match local (varchar(50))
            cursor.execute('ALTER TABLE "WarrantyRequests" ALTER COLUMN status TYPE varchar(50);')
            print("  ✅ Updated status column type to varchar(50)")
            
        except Exception as e:
            print(f"  ⚠️  Status update: {e}")
        
        # Step 5: Add foreign key constraints
        print(f"\n🔧 Step 5: Adding foreign key constraints...")
        
        foreign_keys = [
            ('WarrantyRequests_order_id_fkey', 'order_id', 'Orders', 'order_id'),
            ('WarrantyRequests_processed_by_fkey', 'processed_by', 'Users', 'user_id')
        ]
        
        for fk_name, col_name, ref_table, ref_col in foreign_keys:
            if col_name in [col[0] for col in missing_columns] or col_name == 'processed_by':
                try:
                    # Check if referenced table exists
                    cursor.execute(f'''
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_schema = 'public' AND table_name = '{ref_table}'
                        );
                    ''')
                    table_exists = cursor.fetchone()[0]
                    
                    if table_exists:
                        cursor.execute(f'''
                            ALTER TABLE "WarrantyRequests" ADD CONSTRAINT "{fk_name}" 
                            FOREIGN KEY ("{col_name}") REFERENCES "{ref_table}" ("{ref_col}") 
                            ON DELETE NO ACTION ON UPDATE NO ACTION;
                        ''')
                        print(f"  ✅ Added FK: {col_name} → {ref_table}")
                    else:
                        print(f"  ⚠️  Table {ref_table} not found, skipping FK")
                        
                except Exception as e:
                    print(f"  ⚠️  FK {fk_name}: {e}")
        
        # Step 6: Create missing indexes
        print(f"\n🔧 Step 6: Creating indexes...")
        
        indexes = [
            ('idx_warranty_requests_order_id', 'order_id'),
            ('idx_warranty_requests_processed_by', 'processed_by'),
            ('idx_warranty_requests_created_at', 'created_at'),
            ('idx_warranty_requests_expires_at', 'expires_at')
        ]
        
        for index_name, column in indexes:
            try:
                cursor.execute(f'CREATE INDEX IF NOT EXISTS {index_name} ON "WarrantyRequests"({column}) WHERE {column} IS NOT NULL;')
                print(f"  ✅ Created index: {index_name}")
            except Exception as e:
                print(f"  ⚠️  Index {index_name}: {e}")
        
        # Step 7: Verify final structure
        print(f"\n🔧 Step 7: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type
            FROM information_schema.columns 
            WHERE table_name = 'WarrantyRequests' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📊 Final WarrantyRequests columns ({len(final_columns)} total):")
        for col in final_columns:
            print(f"  - {col[0]}: {col[1]}")
        
        # Step 8: Test basic query
        print(f"\n🔧 Step 8: Testing basic query...")
        
        try:
            cursor.execute('SELECT COUNT(*) FROM "WarrantyRequests";')
            count = cursor.fetchone()[0]
            print(f"  ✅ Query test successful - {count} warranty requests found")
        except Exception as e:
            print(f"  ❌ Query test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 WarrantyRequests table updated successfully!")
        print(f"📊 Added {added_count} new columns")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test warranty functionality")
        
        return True
        
    except Exception as e:
        print(f"❌ Update failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = update_warranty_requests_table()
    sys.exit(0 if success else 1)
