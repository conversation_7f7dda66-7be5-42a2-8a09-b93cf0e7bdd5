// Metadata Presets cho các loại sản phẩm phổ biến
const METADATA_PRESETS = {
    // 1. Account Packages
    'account_package': {
        name: 'Account Package',
        description: '<PERSON><PERSON>i tài khoản TikTok',
        metadata: {
            requires_accounts: true,
            warranty_enabled: true
        },
        icon: 'cil-user'
    },
    
    // 2. Win Products / Digital Downloads
    'win_product': {
        name: 'Win Product',
        description: 'Sản phẩm digital có thể download',
        metadata: {
            requires_files: true,
            downloadable: true
        },
        icon: 'cil-star'
    },
    
    // 3. Video Packages
    'video_package': {
        name: 'Video Package',
        description: 'Gói video từ Google Drive',
        metadata: {
            requires_video_links: true,
            downloadable: true
        },
        icon: 'cil-video'
    },
    
    // 4. Courses
    'course_basic': {
        name: 'Course Basic',
        description: 'Khóa học cơ bản',
        metadata: {
            requires_files: true,
            course_type: 'basic',
            duration_weeks: 4,
            downloadable: true
        },
        icon: 'cil-book'
    },
    
    'course_advanced': {
        name: 'Course Advanced',
        description: '<PERSON><PERSON><PERSON><PERSON> học nâng cao',
        metadata: {
            requires_files: true,
            course_type: 'advanced',
            duration_weeks: 8,
            downloadable: true
        },
        icon: 'cil-graduation-cap'
    },
    
    // 5. TikTok Formulas
    'tiktok_formula': {
        name: 'TikTok Formula',
        description: 'Công thức nuôi kênh TikTok',
        metadata: {
            requires_files: true,
            formula_type: 'channel_growth',
            includes_templates: true,
            downloadable: true
        },
        icon: 'cil-chart-line'
    },
    
    'ai_video_formula': {
        name: 'AI Video Formula',
        description: 'Công thức làm video AI',
        metadata: {
            requires_files: true,
            formula_type: 'ai_video',
            includes_tools: true,
            downloadable: true
        },
        icon: 'cil-video'
    },
    
    // 6. AFF Packages
    'aff_package': {
        name: 'AFF Package',
        description: 'Gói quản lý AFF theo tháng',
        metadata: {
            subscription_based: true,
            account_limit: true,
            max_accounts: 10
        },
        icon: 'cil-chart-pie'
    },
    
    // 7. Tools & Software
    'tool_software': {
        name: 'Tool & Software',
        description: 'Công cụ và phần mềm',
        metadata: {
            requires_files: true,
            downloadable: true,
            includes_tools: true
        },
        icon: 'cil-settings'
    },
    
    // 8. Templates
    'template_pack': {
        name: 'Template Pack',
        description: 'Bộ template thiết kế',
        metadata: {
            requires_files: true,
            downloadable: true,
            includes_templates: true
        },
        icon: 'cil-image'
    },
    
    // 9. E-books
    'ebook': {
        name: 'E-book',
        description: 'Sách điện tử',
        metadata: {
            requires_files: true,
            downloadable: true
        },
        icon: 'cil-book'
    },
    
    // 10. Consultation Services
    'consultation': {
        name: 'Consultation',
        description: 'Dịch vụ tư vấn',
        metadata: {
            requires_files: false,
            service_based: true
        },
        icon: 'cil-chat-bubble'
    }
};

// Function để apply preset
function applyMetadataPreset(presetKey) {
    const preset = METADATA_PRESETS[presetKey];
    if (!preset) return;
    
    // Fill form fields
    document.querySelector('[name="name"]').value = preset.name;
    document.querySelector('[name="description"]').value = preset.description;
    document.querySelector('[name="icon"]').value = preset.icon;
    
    // Update icon preview
    document.getElementById('iconPreview').className = preset.icon;
    
    // Load metadata into builder
    loadExistingMetadata(JSON.stringify(preset.metadata));
}

// Function để tạo preset selector UI
function createPresetSelector() {
    const html = `
        <div class="mb-3">
            <label class="form-label">Preset nhanh</label>
            <div class="row">
                ${Object.keys(METADATA_PRESETS).map(key => {
                    const preset = METADATA_PRESETS[key];
                    return `
                        <div class="col-md-4 mb-2">
                            <button type="button" class="btn btn-outline-secondary w-100 text-start" 
                                    onclick="applyMetadataPreset('${key}')">
                                <i class="${preset.icon} me-2"></i>
                                <strong>${preset.name}</strong>
                                <br><small class="text-muted">${preset.description}</small>
                            </button>
                        </div>
                    `;
                }).join('')}
            </div>
            <div class="form-text">Click để áp dụng preset, sau đó có thể tùy chỉnh thêm</div>
        </div>
    `;
    
    return html;
}

// Metadata validation
function validateMetadata(metadata) {
    const errors = [];
    
    // Check for conflicting settings
    if (metadata.requires_accounts && metadata.requires_files) {
        errors.push('Sản phẩm không thể vừa cần accounts vừa cần files');
    }
    
    if (metadata.subscription_based && !metadata.account_limit) {
        errors.push('Subscription products nên có account_limit');
    }
    
    if (metadata.course_type && !metadata.duration_weeks) {
        errors.push('Khóa học cần có thời lượng');
    }
    
    if (metadata.account_limit && !metadata.max_accounts) {
        errors.push('Account limit cần có số lượng tối đa');
    }
    
    return errors;
}

// Smart suggestions based on product name
function suggestMetadataFromName(productName) {
    const name = productName.toLowerCase();
    const suggestions = {};
    
    // Auto-detect product type from name
    if (name.includes('account') || name.includes('tài khoản')) {
        suggestions.requires_accounts = true;
        suggestions.warranty_enabled = true;
    }
    
    if (name.includes('video')) {
        suggestions.requires_video_links = true;
    }
    
    if (name.includes('course') || name.includes('khóa học')) {
        suggestions.requires_files = true;
        suggestions.course_type = name.includes('advanced') || name.includes('nâng cao') ? 'advanced' : 'basic';
        suggestions.duration_weeks = 4;
    }
    
    if (name.includes('formula') || name.includes('công thức')) {
        suggestions.requires_files = true;
        if (name.includes('tiktok')) {
            suggestions.formula_type = 'channel_growth';
        } else if (name.includes('ai') || name.includes('video')) {
            suggestions.formula_type = 'ai_video';
        }
        suggestions.includes_templates = true;
    }
    
    if (name.includes('aff') || name.includes('affiliate')) {
        suggestions.subscription_based = true;
        suggestions.account_limit = true;
        suggestions.max_accounts = 10;
    }
    
    if (name.includes('template')) {
        suggestions.requires_files = true;
        suggestions.includes_templates = true;
    }
    
    if (name.includes('tool') || name.includes('software')) {
        suggestions.requires_files = true;
        suggestions.includes_tools = true;
    }
    
    if (name.includes('ebook') || name.includes('book')) {
        suggestions.requires_files = true;
        suggestions.downloadable = true;
    }
    
    return suggestions;
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        METADATA_PRESETS,
        applyMetadataPreset,
        createPresetSelector,
        validateMetadata,
        suggestMetadataFromName
    };
}
