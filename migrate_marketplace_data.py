#!/usr/bin/env python3
"""
Migrate marketplace data from local to server
1. Fix Products table schema (add missing columns)
2. Export data from local database
3. Import data to server database
"""

import sys
import os
import json
from datetime import datetime

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def migrate_marketplace_data():
    """Migrate marketplace data from local to server"""
    
    print("🚀 Starting marketplace data migration...")
    print("=" * 60)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Fix Products table schema
        print("\n🔧 Step 1: Fixing Products table schema...")
        
        # Add missing columns to Products table
        schema_fixes = [
            'ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT \'active\';',
            'ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;',
            'ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL;',
            'ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS deleted_by INTEGER REFERENCES "Users"(user_id);',
            'ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS product_type_id INTEGER REFERENCES "ProductTypes"(type_id);',
        ]
        
        for fix in schema_fixes:
            try:
                cursor.execute(fix)
                print(f"  ✅ {fix.split('ADD COLUMN')[1].split()[2] if 'ADD COLUMN' in fix else 'Schema fix'}")
            except Exception as e:
                print(f"  ⚠️  {fix.split('ADD COLUMN')[1].split()[2] if 'ADD COLUMN' in fix else 'Schema fix'}: {e}")
        
        # Update existing products to have active status
        cursor.execute('UPDATE "Products" SET status = \'active\' WHERE status IS NULL OR status = \'\';')
        print(f"  ✅ Updated existing products to active status")
        
        # Step 2: Create ProductTypes table and data
        print("\n🔧 Step 2: Creating ProductTypes table and default data...")
        
        # Create ProductTypes table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "ProductTypes" (
                type_id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                description TEXT,
                icon VARCHAR(50),
                is_default BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                metadata JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        print("  ✅ ProductTypes table created")
        
        # Insert default product types
        default_types = [
            ('Account', 'Tài khoản TikTok với các tiêu chí khác nhau', 'cil-user', True, True, '{"fields": ["followers", "likes", "videos", "status"]}'),
            ('Win Product', 'Sản phẩm win đã được test và verify', 'cil-star', True, True, '{"fields": ["shop_link", "kalodata_link", "live_hours"]}'),
            ('Course Basic', 'Khóa học cơ bản', 'cil-book', True, True, '{"fields": ["duration", "level", "certificate"]}'),
            ('Course Advanced', 'Khóa học nâng cao', 'cil-graduation-cap', True, True, '{"fields": ["duration", "level", "certificate", "prerequisites"]}'),
            ('AFF Package', 'Gói quản lý tài khoản AFF theo tháng', 'cil-chart-line', True, True, '{"fields": ["account_limit", "duration_months", "features"]}'),
            ('TikTok Playbook', 'Công thức và chiến lược phát triển TikTok', 'cil-lightbulb', True, True, '{"fields": ["strategy_type", "target_audience", "content_type"]}'),
        ]
        
        for name, desc, icon, is_default, is_active, metadata in default_types:
            cursor.execute('''
                INSERT INTO "ProductTypes" (name, description, icon, is_default, is_active, metadata)
                VALUES (%s, %s, %s, %s, %s, %s)
                ON CONFLICT (name) DO UPDATE SET
                    description = EXCLUDED.description,
                    icon = EXCLUDED.icon,
                    is_default = EXCLUDED.is_default,
                    is_active = EXCLUDED.is_active,
                    metadata = EXCLUDED.metadata,
                    updated_at = CURRENT_TIMESTAMP;
            ''', (name, desc, icon, is_default, is_active, metadata))
        
        print(f"  ✅ Inserted {len(default_types)} default product types")
        
        # Step 3: Create CategoryProductTypes table
        print("\n🔧 Step 3: Creating CategoryProductTypes table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "CategoryProductTypes" (
                id SERIAL PRIMARY KEY,
                category_id INTEGER REFERENCES "ProductCategories"(category_id) ON DELETE CASCADE,
                type_id INTEGER REFERENCES "ProductTypes"(type_id) ON DELETE CASCADE,
                is_primary BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(category_id, type_id)
            );
        ''')
        print("  ✅ CategoryProductTypes table created")
        
        # Step 4: Insert default categories
        print("\n🔧 Step 4: Inserting default categories...")
        
        default_categories = [
            ('Account Packages', 'Gói tài khoản TikTok với các tiêu chí khác nhau', 'cil-user', 1),
            ('Win Products', 'Sản phẩm win đã được test và verify', 'cil-star', 2),
            ('Courses', 'Khóa học và tài liệu đào tạo', 'cil-book', 3),
            ('AFF Packages', 'Gói quản lý tài khoản AFF theo tháng', 'cil-chart-line', 4),
            ('TikTok Playbook', 'Công thức và chiến lược phát triển TikTok', 'cil-lightbulb', 5),
        ]
        
        for name, desc, icon, sort_order in default_categories:
            # Check if category exists first
            cursor.execute('SELECT category_id FROM "ProductCategories" WHERE name = %s', (name,))
            existing = cursor.fetchone()

            if existing:
                # Update existing
                cursor.execute('''
                    UPDATE "ProductCategories"
                    SET description = %s, icon = %s, sort_order = %s
                    WHERE name = %s
                ''', (desc, icon, sort_order, name))
            else:
                # Insert new
                cursor.execute('''
                    INSERT INTO "ProductCategories" (name, description, icon, sort_order)
                    VALUES (%s, %s, %s, %s)
                ''', (name, desc, icon, sort_order))
        
        print(f"  ✅ Inserted {len(default_categories)} default categories")
        
        # Step 5: Link categories with product types
        print("\n🔧 Step 5: Linking categories with product types...")
        
        category_type_links = [
            ('Account Packages', 'Account', True),
            ('Win Products', 'Win Product', True),
            ('Courses', 'Course Basic', True),
            ('Courses', 'Course Advanced', False),
            ('AFF Packages', 'AFF Package', True),
            ('TikTok Playbook', 'TikTok Playbook', True),
        ]
        
        for cat_name, type_name, is_primary in category_type_links:
            cursor.execute('''
                INSERT INTO "CategoryProductTypes" (category_id, type_id, is_primary)
                SELECT pc.category_id, pt.type_id, %s
                FROM "ProductCategories" pc, "ProductTypes" pt
                WHERE pc.name = %s AND pt.name = %s
                ON CONFLICT (category_id, type_id) DO UPDATE SET
                    is_primary = EXCLUDED.is_primary;
            ''', (is_primary, cat_name, type_name))
        
        print(f"  ✅ Linked {len(category_type_links)} category-type relationships")
        
        # Step 6: Insert sample products
        print("\n🔧 Step 6: Inserting sample products...")
        
        # Get category and type IDs
        cursor.execute('SELECT category_id FROM "ProductCategories" WHERE name = %s', ('Account Packages',))
        account_cat_id = cursor.fetchone()[0]
        
        cursor.execute('SELECT category_id FROM "ProductCategories" WHERE name = %s', ('AFF Packages',))
        aff_cat_id = cursor.fetchone()[0]
        
        cursor.execute('SELECT type_id FROM "ProductTypes" WHERE name = %s', ('Account',))
        account_type_id = cursor.fetchone()[0]
        
        cursor.execute('SELECT type_id FROM "ProductTypes" WHERE name = %s', ('AFF Package',))
        aff_type_id = cursor.fetchone()[0]
        
        sample_products = [
            (account_cat_id, 'Gói Account TikTok Starter', 'Gói 10 tài khoản TikTok cơ bản', 'Gói 10 tài khoản TikTok với follower từ 1K-5K, phù hợp cho người mới bắt đầu', 500000, 'one_time', 1, 0, 10, False, 'account', account_type_id, '{"min_followers": 1000, "max_followers": 5000, "account_count": 10}', True, False, 'active'),
            (account_cat_id, 'Gói Account TikTok Premium', 'Gói 5 tài khoản TikTok chất lượng cao', 'Gói 5 tài khoản TikTok với follower từ 10K-50K, đã verify và có engagement tốt', 2000000, 'one_time', 1, 0, 5, False, 'account', account_type_id, '{"min_followers": 10000, "max_followers": 50000, "account_count": 5, "verified": true}', True, True, 'active'),
            # Không thêm AFF packages vì đã có data
        ]
        
        for product_data in sample_products:
            # Check if product exists first
            cursor.execute('SELECT product_id FROM "Products" WHERE name = %s', (product_data[1],))
            existing = cursor.fetchone()

            if not existing:
                # Insert new product only if it doesn't exist
                cursor.execute('''
                    INSERT INTO "Products" (
                        category_id, name, short_description, description, price, payment_type,
                        subscription_duration_months, subscription_discount_yearly, stock, unlimited_stock,
                        product_type, product_type_id, metadata, is_active, is_featured, status
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                ''', product_data)
        
        print(f"  ✅ Inserted {len(sample_products)} sample products")
        
        # Step 7: Create indexes
        print("\n🔧 Step 7: Creating indexes...")
        
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_products_category ON "Products"(category_id);',
            'CREATE INDEX IF NOT EXISTS idx_products_type ON "Products"(product_type);',
            'CREATE INDEX IF NOT EXISTS idx_products_type_id ON "Products"(product_type_id);',
            'CREATE INDEX IF NOT EXISTS idx_products_active ON "Products"(is_active);',
            'CREATE INDEX IF NOT EXISTS idx_products_status ON "Products"(status);',
            'CREATE INDEX IF NOT EXISTS idx_category_types_category ON "CategoryProductTypes"(category_id);',
            'CREATE INDEX IF NOT EXISTS idx_category_types_type ON "CategoryProductTypes"(type_id);',
        ]
        
        for index in indexes:
            try:
                cursor.execute(index)
                print(f"  ✅ {index.split('idx_')[1].split()[0] if 'idx_' in index else 'Index'}")
            except Exception as e:
                print(f"  ⚠️  Index: {e}")
        
        # Commit all changes
        conn.commit()
        
        # Step 8: Verify data
        print("\n🔍 Step 8: Verifying migrated data...")
        
        cursor.execute('SELECT COUNT(*) FROM "ProductCategories";')
        cat_count = cursor.fetchone()[0]
        print(f"  📊 ProductCategories: {cat_count} records")
        
        cursor.execute('SELECT COUNT(*) FROM "ProductTypes";')
        type_count = cursor.fetchone()[0]
        print(f"  📊 ProductTypes: {type_count} records")
        
        cursor.execute('SELECT COUNT(*) FROM "CategoryProductTypes";')
        link_count = cursor.fetchone()[0]
        print(f"  📊 CategoryProductTypes: {link_count} records")
        
        cursor.execute('SELECT COUNT(*) FROM "Products";')
        prod_count = cursor.fetchone()[0]
        print(f"  📊 Products: {prod_count} records")
        
        # Test the problematic query
        print("\n🧪 Testing the problematic query...")
        cursor.execute('''
            SELECT c.category_id, c.name, c.description, c.icon, c.sort_order,
                   c.is_active, c.created_at,
                   (SELECT COUNT(*) FROM "Products" WHERE category_id = c.category_id AND (status = 'active' OR status IS NULL) AND (is_deleted = FALSE OR is_deleted IS NULL)) as product_count
            FROM "ProductCategories" c
            ORDER BY c.sort_order, c.name
            LIMIT 3;
        ''')
        
        test_results = cursor.fetchall()
        print(f"  ✅ Query test successful - {len(test_results)} categories returned")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Marketplace data migration completed successfully!")
        print("📝 Next steps:")
        print("   1. Restart the application: sudo systemctl restart sapmmo")
        print("   2. Test /admin/marketplace/categories page")
        print("   3. Test creating new products and categories")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = migrate_marketplace_data()
    sys.exit(0 if success else 1)
