#!/usr/bin/env python3
"""
Script to fix existing mixed orders by splitting them into separate orders by product type
"""

import psycopg2
import json
import uuid
from collections import defaultdict
from datetime import datetime

def get_db_connection():
    """Get PostgreSQL database connection"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo", 
            user="alandoan",
            password=""
        )
        return conn
    except Exception as e:
        print(f"Database connection error: {e}")
        return None

def find_mixed_orders():
    """Find orders that contain multiple product types"""
    conn = get_db_connection()
    if not conn:
        return []
    
    cursor = conn.cursor()
    
    # Find orders with multiple product types
    cursor.execute('''
        SELECT o.order_id, o.order_number, o.user_id, o.total_amount, o.discount_amount,
               o.discount_code, o.final_amount, o.mp_amount, o.status, o.created_at,
               COUNT(DISTINCT p.product_type) as type_count
        FROM "Orders" o
        JOIN "OrderItems" oi ON o.order_id = oi.order_id
        JOIN "Products" p ON oi.product_id = p.product_id
        GROUP BY o.order_id, o.order_number, o.user_id, o.total_amount, o.discount_amount,
                 o.discount_code, o.final_amount, o.mp_amount, o.status, o.created_at
        HAVING COUNT(DISTINCT p.product_type) > 1
        ORDER BY o.created_at DESC
    ''')
    
    mixed_orders = cursor.fetchall()
    cursor.close()
    conn.close()
    
    return mixed_orders

def get_order_items_by_type(order_id):
    """Get order items grouped by product type"""
    conn = get_db_connection()
    if not conn:
        return {}
    
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT oi.item_id, oi.product_id, oi.quantity, oi.unit_price, oi.total_price,
               p.name as product_name, p.product_type, oi.item_data
        FROM "OrderItems" oi
        JOIN "Products" p ON oi.product_id = p.product_id
        WHERE oi.order_id = %s
        ORDER BY oi.item_id
    ''', (order_id,))
    
    items = cursor.fetchall()
    
    # Group by product type
    items_by_type = defaultdict(list)
    for item in items:
        item_data = {
            'item_id': item[0],
            'product_id': item[1],
            'quantity': item[2],
            'unit_price': float(item[3]),
            'total_price': float(item[4]),
            'product_name': item[5],
            'product_type': item[6],
            'item_data': item[7]
        }
        items_by_type[item[6]].append(item_data)
    
    cursor.close()
    conn.close()
    
    return dict(items_by_type)

def create_split_orders(original_order, items_by_type):
    """Create separate orders for each product type"""
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        # Calculate original totals
        original_total = float(original_order[3])
        original_discount = float(original_order[4]) if original_order[4] else 0
        
        created_orders = []
        
        # Create separate order for each product type
        for product_type, type_items in items_by_type.items():
            # Calculate totals for this product type
            type_total = sum(item['total_price'] for item in type_items)
            
            # Apply proportional discount
            type_discount = 0
            if original_discount > 0:
                type_discount = (type_total / original_total) * original_discount
            
            type_final_amount = type_total - type_discount
            type_mp_amount = int(type_final_amount)
            
            # Generate new order number
            order_number = f"ORD{uuid.uuid4().hex[:8].upper()}"
            
            # Create order data
            order_data_json = {
                'items': [{
                    'product_id': item['product_id'],
                    'product_name': item['product_name'],
                    'product_type': item['product_type'],
                    'quantity': item['quantity'],
                    'unit_price': item['unit_price'],
                    'total_price': item['total_price']
                } for item in type_items],
                'split_from_order': original_order[1]  # Reference to original order
            }

            # Insert new order
            cursor.execute('''
                INSERT INTO "Orders" (user_id, order_number, total_amount, discount_amount,
                                    discount_code, final_amount, mp_amount, status, order_data, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING order_id
            ''', (
                original_order[2],  # user_id
                order_number,
                float(type_total),
                float(type_discount),
                original_order[5],  # discount_code
                float(type_final_amount),
                type_mp_amount,
                original_order[8],  # status
                json.dumps(order_data_json),
                original_order[9]   # created_at
            ))

            new_order_id = cursor.fetchone()[0]
            
            # Create order items for new order
            for item in type_items:
                # Handle item_data - convert to JSON string if it's a dict
                item_data_json = item['item_data']
                if isinstance(item_data_json, dict):
                    item_data_json = json.dumps(item_data_json)

                cursor.execute('''
                    INSERT INTO "OrderItems" (order_id, product_id, quantity, unit_price, total_price, item_data)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    RETURNING item_id
                ''', (
                    new_order_id,
                    item['product_id'],
                    item['quantity'],
                    item['unit_price'],
                    item['total_price'],
                    item_data_json
                ))
                
                new_item_id = cursor.fetchone()[0]
                
                # Update PackageAccounts to reference new order item if exists
                if item['product_type'] == 'account':
                    cursor.execute('''
                        UPDATE "PackageAccounts"
                        SET order_item_id = %s
                        WHERE order_item_id = %s
                    ''', (new_item_id, item['item_id']))
            
            created_orders.append({
                'order_id': new_order_id,
                'order_number': order_number,
                'product_type': product_type
            })
            
            print(f"  ✅ Created {product_type} order: #{order_number} (ID: {new_order_id})")
        
        # Update MarketplaceTransactions to reference the first new order
        if created_orders:
            first_order_id = created_orders[0]['order_id']
            cursor.execute('''
                UPDATE "MarketplaceTransactions"
                SET order_id = %s
                WHERE order_id = %s
            ''', (first_order_id, original_order[0]))

        # Delete original order items
        cursor.execute('DELETE FROM "OrderItems" WHERE order_id = %s', (original_order[0],))

        # Delete original order
        cursor.execute('DELETE FROM "Orders" WHERE order_id = %s', (original_order[0],))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"  🗑️  Deleted original order: #{original_order[1]} (ID: {original_order[0]})")
        return True
        
    except Exception as e:
        print(f"  ❌ Error creating split orders: {e}")
        conn.rollback()
        cursor.close()
        conn.close()
        return False

def main():
    """Main function to fix mixed orders"""
    print("🔍 Finding mixed orders (orders with multiple product types)...")
    
    mixed_orders = find_mixed_orders()
    
    if not mixed_orders:
        print("✅ No mixed orders found!")
        return
    
    print(f"📋 Found {len(mixed_orders)} mixed orders to fix:")
    
    for order in mixed_orders:
        order_id = order[0]
        order_number = order[1]
        type_count = order[10]
        
        print(f"\n🔧 Processing order #{order_number} (ID: {order_id}) with {type_count} product types...")
        
        # Get items grouped by type
        items_by_type = get_order_items_by_type(order_id)
        
        if not items_by_type:
            print(f"  ⚠️  No items found for order {order_id}")
            continue
        
        print(f"  📦 Product types: {list(items_by_type.keys())}")
        
        # Create split orders
        success = create_split_orders(order, items_by_type)
        
        if success:
            print(f"  ✅ Successfully split order #{order_number}")
        else:
            print(f"  ❌ Failed to split order #{order_number}")
    
    print(f"\n🎉 Migration completed! Processed {len(mixed_orders)} mixed orders.")

if __name__ == "__main__":
    main()
