#!/usr/bin/env python3
"""
Debug transactions and timezone issues
"""

import sys
import os
from datetime import datetime

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def debug_transactions():
    """Debug transactions and timezone issues"""
    
    print("🔍 Debugging transactions and timezone...")
    print("=" * 60)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        print(f"🕐 Current server time: {datetime.now()}")
        
        # Check server timezone
        cursor.execute("SELECT NOW(), CURRENT_TIMESTAMP, timezone('UTC', NOW()), timezone('Asia/Ho_Chi_Minh', NOW());")
        times = cursor.fetchone()
        print(f"📅 Database times:")
        print(f"  - NOW(): {times[0]}")
        print(f"  - CURRENT_TIMESTAMP: {times[1]}")
        print(f"  - UTC: {times[2]}")
        print(f"  - Vietnam (+7): {times[3]}")
        
        # Check latest deposits
        print(f"\n🔍 Latest Deposits (raw data):")
        cursor.execute('''
            SELECT id, user_id, amount, mp_amount, status, created_at, completed_at
            FROM "Deposits"
            ORDER BY id DESC
            LIMIT 3;
        ''')
        
        deposits = cursor.fetchall()
        for dep in deposits:
            print(f"  - ID {dep[0]}: User {dep[1]}, {dep[2]} VND → {dep[3]} MP")
            print(f"    Created: {dep[5]} | Completed: {dep[6]}")
        
        # Check if MPTransactions are created when deposit completes
        print(f"\n🔍 Latest MPTransactions:")
        cursor.execute('''
            SELECT transaction_id, user_id, amount, transaction_type, description, created_at, created_by
            FROM "MPTransactions"
            WHERE is_deleted = 0
            ORDER BY transaction_id DESC
            LIMIT 5;
        ''')
        
        transactions = cursor.fetchall()
        if transactions:
            for trans in transactions:
                print(f"  - ID {trans[0]}: User {trans[1]}, {trans[2]} MP ({trans[3]})")
                print(f"    Description: {trans[4]}")
                print(f"    Created: {trans[5]} by User {trans[6]}")
        else:
            print("  ❌ No transactions found!")
        
        # Check if there's a gap between deposits and transactions
        cursor.execute('SELECT COUNT(*) FROM "Deposits" WHERE status = \'completed\';')
        completed_deposits = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM "MPTransactions" WHERE transaction_type = \'DEPOSIT\' AND is_deleted = 0;')
        deposit_transactions = cursor.fetchone()[0]
        
        print(f"\n📊 Data consistency:")
        print(f"  - Completed deposits: {completed_deposits}")
        print(f"  - Deposit transactions: {deposit_transactions}")
        print(f"  - Gap: {completed_deposits - deposit_transactions}")
        
        if completed_deposits > deposit_transactions:
            print("  ❌ Missing transactions for some deposits!")
            
            # Find deposits without corresponding transactions
            cursor.execute('''
                SELECT d.id, d.user_id, d.mp_amount, d.completed_at
                FROM "Deposits" d
                LEFT JOIN "MPTransactions" t ON t.user_id = d.user_id 
                    AND t.amount = d.mp_amount 
                    AND t.transaction_type = 'DEPOSIT'
                    AND DATE(t.created_at) = DATE(d.completed_at)
                WHERE d.status = 'completed' 
                AND t.transaction_id IS NULL
                ORDER BY d.id DESC
                LIMIT 5;
            ''')
            
            missing_trans = cursor.fetchall()
            if missing_trans:
                print(f"  📋 Deposits missing transactions:")
                for dep in missing_trans:
                    print(f"    - Deposit ID {dep[0]}: User {dep[1]}, {dep[2]} MP at {dep[3]}")
        
        # Test timezone conversion
        print(f"\n🔍 Testing timezone conversion:")
        cursor.execute('''
            SELECT 
                created_at as raw_time,
                created_at AT TIME ZONE 'UTC' as utc_time,
                created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh' as vn_time,
                created_at AT TIME ZONE 'Asia/Ho_Chi_Minh' as direct_vn
            FROM "Deposits"
            WHERE created_at IS NOT NULL
            ORDER BY id DESC
            LIMIT 2;
        ''')
        
        timezone_test = cursor.fetchall()
        for i, row in enumerate(timezone_test):
            print(f"  Row {i+1}:")
            print(f"    - Raw: {row[0]}")
            print(f"    - UTC: {row[1]}")
            print(f"    - VN (UTC→VN): {row[2]}")
            print(f"    - Direct VN: {row[3]}")
        
        # Check API query results
        print(f"\n🔍 Testing API queries:")
        
        # Test deposit history API query
        cursor.execute('''
            SELECT id, amount, mp_amount, status, 
                   created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh' as created_at_vn,
                   completed_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh' as completed_at_vn
            FROM "Deposits"
            WHERE user_id = (SELECT user_id FROM "Deposits" ORDER BY id DESC LIMIT 1)
            ORDER BY created_at DESC
            LIMIT 2;
        ''')
        
        api_deposits = cursor.fetchall()
        print(f"  📋 Deposit API results:")
        for dep in api_deposits:
            print(f"    - ID {dep[0]}: {dep[1]} VND → {dep[2]} MP ({dep[3]})")
            print(f"      Created VN: {dep[4]}")
            print(f"      Completed VN: {dep[5]}")
        
        # Test transactions API query
        cursor.execute('''
            SELECT t.transaction_id, t.user_id, t.amount, t.transaction_type,
                   t.description, 
                   t.created_at AT TIME ZONE 'UTC' AT TIME ZONE 'Asia/Ho_Chi_Minh' as created_at_vn,
                   t.created_by, u.username as created_by_name
            FROM "MPTransactions" t
            JOIN "Users" u ON t.created_by = u.user_id
            WHERE t.is_deleted = 0
            ORDER BY t.transaction_id DESC
            LIMIT 2;
        ''')
        
        api_transactions = cursor.fetchall()
        print(f"  📋 Transactions API results:")
        for trans in api_transactions:
            print(f"    - ID {trans[0]}: User {trans[1]}, {trans[2]} MP ({trans[3]})")
            print(f"      Created VN: {trans[5]} by {trans[7]}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Debug completed!")
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = debug_transactions()
    sys.exit(0 if success else 1)
