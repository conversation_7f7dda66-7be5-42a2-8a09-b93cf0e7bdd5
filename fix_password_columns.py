#!/usr/bin/env python3
"""
Fix password columns in Users table
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_password_columns():
    """Fix password columns in Users table"""
    
    print("🚀 Fixing password columns in Users table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current columns
        print("\n🔧 Step 1: Checking password columns...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND column_name IN ('password', 'password_hash');
        ''')
        
        password_cols = cursor.fetchall()
        has_password = any(col[0] == 'password' for col in password_cols)
        has_password_hash = any(col[0] == 'password_hash' for col in password_cols)
        
        print(f"📋 Has 'password' column: {has_password}")
        print(f"📋 Has 'password_hash' column: {has_password_hash}")
        
        # Step 2: Handle different scenarios
        if has_password and has_password_hash:
            print("\n🔧 Step 2: Both columns exist - migrating data...")
            
            # Copy password_hash to password for existing users
            cursor.execute('''
                UPDATE "Users" 
                SET password = password_hash 
                WHERE password IS NULL AND password_hash IS NOT NULL;
            ''')
            
            updated_count = cursor.rowcount
            print(f"✅ Updated {updated_count} users: copied password_hash to password")
            
            # Drop password_hash column (we'll use password)
            cursor.execute('ALTER TABLE "Users" DROP COLUMN IF EXISTS password_hash;')
            print("✅ Dropped password_hash column")
            
        elif has_password_hash and not has_password:
            print("\n🔧 Step 2: Only password_hash exists - renaming...")
            
            # Rename password_hash to password
            cursor.execute('ALTER TABLE "Users" RENAME COLUMN password_hash TO password;')
            print("✅ Renamed password_hash to password")
            
        elif has_password and not has_password_hash:
            print("\n🔧 Step 2: Only password exists - good!")
            print("✅ Password column already exists and is correct")
            
        else:
            print("\n❌ Step 2: No password columns found!")
            
            # Create password column
            cursor.execute('''
                ALTER TABLE "Users" 
                ADD COLUMN password TEXT NOT NULL DEFAULT 'temp_password';
            ''')
            print("✅ Created password column")
        
        # Step 3: Ensure password column is NOT NULL
        print("\n🔧 Step 3: Ensuring password column constraints...")
        
        cursor.execute('''
            SELECT is_nullable 
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND column_name = 'password';
        ''')
        
        is_nullable = cursor.fetchone()
        if is_nullable and is_nullable[0] == 'YES':
            print("🔧 Password column is nullable, making it NOT NULL...")
            
            # First, update any NULL passwords
            cursor.execute('''
                UPDATE "Users" 
                SET password = 'temp_password_' || user_id 
                WHERE password IS NULL;
            ''')
            
            # Then set NOT NULL constraint
            cursor.execute('ALTER TABLE "Users" ALTER COLUMN password SET NOT NULL;')
            print("✅ Set password column to NOT NULL")
        else:
            print("✅ Password column is already NOT NULL")
        
        # Step 4: Verify final structure
        print("\n🔧 Step 4: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND column_name LIKE '%password%';
        ''')
        
        final_cols = cursor.fetchall()
        print("📋 Final password columns:")
        for col in final_cols:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            print(f"  - {col[0]}: {col[1]} {nullable}")
        
        # Step 5: Test INSERT
        print("\n🔧 Step 5: Testing INSERT with password...")
        
        try:
            cursor.execute('SAVEPOINT test_insert;')
            
            cursor.execute('''
                INSERT INTO "Users" (username, password, role, unit_code, phone, email)
                VALUES ('test_password_user', 'test_hash_123', 'member', 'TEST002', '+84123456789', '<EMAIL>')
                RETURNING user_id;
            ''')
            
            test_user_id = cursor.fetchone()[0]
            print(f"✅ Test INSERT successful, got user_id: {test_user_id}")
            
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
            print("✅ Test INSERT rolled back")
            
        except Exception as e:
            print(f"❌ Test INSERT failed: {e}")
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Password columns fixed successfully!")
        print("📝 Next steps:")
        print("   1. Update code to use 'password' column instead of 'password_hash'")
        print("   2. Restart application: sudo systemctl restart sapmmo")
        print("   3. Test user registration")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_password_columns()
    sys.exit(0 if success else 1)
