#!/usr/bin/env python3
"""
Debug registration function specifically
"""

import psycopg2
import psycopg2.extras
from db_config import PG_CONFIG

def test_create_verification_code_with_params():
    """Test create_verification_code with exact registration parameters"""
    try:
        print("🧪 Testing create_verification_code with registration parameters")
        print("=" * 60)
        
        from email_verification_service import create_verification_code
        
        # Test with exact parameters that registration uses
        test_email = "<EMAIL>"
        code_type = 'registration'
        user_id = None  # Registration doesn't have user_id yet
        ip_address = '127.0.0.1'
        user_agent = 'Mozilla/5.0 Test'
        
        print(f"📧 Testing with:")
        print(f"   Email: {test_email}")
        print(f"   Type: {code_type}")
        print(f"   User ID: {user_id}")
        print(f"   IP: {ip_address}")
        print(f"   User Agent: {user_agent}")
        
        result = create_verification_code(
            email=test_email,
            code_type=code_type,
            user_id=user_id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        if result:
            print(f"✅ create_verification_code SUCCESS!")
            print(f"   Code ID: {result['code_id']}")
            print(f"   Code: {result['code']}")
            print(f"   Expires: {result['expires_at']}")
            print(f"   Max attempts: {result['max_attempts']}")
            return True
        else:
            print(f"❌ create_verification_code returned None")
            return False
            
    except Exception as e:
        print(f"❌ create_verification_code failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_insert_directly():
    """Test inserting into EmailVerificationCodes directly"""
    try:
        print(f"\n🧪 Testing direct database insert")
        print("=" * 60)
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        # Test insert with exact structure
        from datetime import datetime, timedelta
        import random
        import string
        
        test_data = {
            'email': '<EMAIL>',
            'code': ''.join(random.choices(string.digits, k=6)),
            'code_type': 'registration',
            'user_id': None,
            'expires_at': datetime.now() + timedelta(minutes=15),
            'max_attempts': 3,
            'ip_address': '127.0.0.1',
            'user_agent': 'Test Agent'
        }
        
        print(f"📝 Inserting test data: {test_data}")
        
        cursor.execute('''
            INSERT INTO "EmailVerificationCodes" 
            (email, code, code_type, user_id, expires_at, max_attempts, ip_address, user_agent)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING code_id
        ''', (
            test_data['email'],
            test_data['code'],
            test_data['code_type'],
            test_data['user_id'],
            test_data['expires_at'],
            test_data['max_attempts'],
            test_data['ip_address'],
            test_data['user_agent']
        ))
        
        code_id = cursor.fetchone()[0]
        conn.commit()
        
        print(f"✅ Direct insert SUCCESS! Code ID: {code_id}")
        
        # Clean up
        cursor.execute('DELETE FROM "EmailVerificationCodes" WHERE code_id = %s', (code_id,))
        conn.commit()
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Direct insert failed: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        return False

def test_registration_simulation():
    """Simulate exact registration flow"""
    try:
        print(f"\n🧪 Simulating exact registration flow")
        print("=" * 60)
        
        # Step 1: Check imports
        print("📋 1. Testing imports...")
        try:
            from email_verification_service import send_otp_email, create_verification_code, get_email_config
            print("   ✅ All imports successful")
        except ImportError as e:
            print(f"   ❌ Import failed: {e}")
            return False
        
        # Step 2: Test email config
        print("📋 2. Testing email config...")
        config = get_email_config()
        if config and config.get('mailtrap_api_token'):
            print("   ✅ Email config loaded")
        else:
            print("   ❌ Email config missing or invalid")
            return False
        
        # Step 3: Test create verification code
        print("📋 3. Testing create verification code...")
        test_email = "<EMAIL>"
        
        code_data = create_verification_code(
            email=test_email,
            code_type='registration',
            user_id=None,
            ip_address='127.0.0.1',
            user_agent='Registration Test'
        )
        
        if code_data:
            print(f"   ✅ Verification code created: {code_data['code']}")
        else:
            print("   ❌ Failed to create verification code")
            return False
        
        # Step 4: Test full send_otp_email
        print("📋 4. Testing send_otp_email...")
        result = send_otp_email(
            email=test_email,
            code_type='registration',
            user_id=None,
            ip_address='127.0.0.1',
            user_agent='Registration Test'
        )
        
        if result['success']:
            print(f"   ✅ send_otp_email successful!")
            print(f"      Code ID: {result['code_id']}")
            return True
        else:
            print(f"   ❌ send_otp_email failed: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ Registration simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_permissions():
    """Check database permissions"""
    try:
        print(f"\n🧪 Checking database permissions")
        print("=" * 60)
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        # Check if we can insert into EmailVerificationCodes
        cursor.execute('''
            SELECT has_table_privilege(current_user, 'EmailVerificationCodes', 'INSERT')
        ''')
        can_insert = cursor.fetchone()[0]
        
        if can_insert:
            print("✅ INSERT permission on EmailVerificationCodes: OK")
        else:
            print("❌ INSERT permission on EmailVerificationCodes: DENIED")
            return False
        
        # Check if we can select from SystemConfig
        cursor.execute('''
            SELECT has_table_privilege(current_user, 'SystemConfig', 'SELECT')
        ''')
        can_select = cursor.fetchone()[0]
        
        if can_select:
            print("✅ SELECT permission on SystemConfig: OK")
        else:
            print("❌ SELECT permission on SystemConfig: DENIED")
            return False
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Permission check failed: {e}")
        return False

def main():
    """Main debug function"""
    print("🔍 Registration Function Debug Tool")
    print("=" * 70)
    
    # Test database permissions
    perm_ok = check_database_permissions()
    
    # Test direct database insert
    insert_ok = test_database_insert_directly()
    
    # Test create_verification_code with exact params
    create_ok = test_create_verification_code_with_params()
    
    # Test full registration simulation
    sim_ok = test_registration_simulation()
    
    print(f"\n📊 Test Results:")
    print(f"   Database permissions: {'✅' if perm_ok else '❌'}")
    print(f"   Direct database insert: {'✅' if insert_ok else '❌'}")
    print(f"   create_verification_code: {'✅' if create_ok else '❌'}")
    print(f"   Registration simulation: {'✅' if sim_ok else '❌'}")
    
    if all([perm_ok, insert_ok, create_ok, sim_ok]):
        print(f"\n🎉 All tests passed! The issue is likely in the web app code.")
        print(f"\n💡 Possible causes:")
        print(f"   1. Web server hasn't reloaded the updated mip_system.py")
        print(f"   2. Different Python environment (virtualenv)")
        print(f"   3. Exception being caught and not displayed properly")
        print(f"   4. Session/request data issue")
        print(f"\n🔧 Try:")
        print(f"   1. Restart web server completely")
        print(f"   2. Check web server error logs")
        print(f"   3. Add more debug prints to registration function")
    else:
        print(f"\n❌ Some tests failed. Fix the failing components first.")

if __name__ == "__main__":
    main()
