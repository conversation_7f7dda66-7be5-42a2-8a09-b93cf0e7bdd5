#!/usr/bin/env python3
"""
Fix server password column - rename password_hash to password
<PERSON><PERSON> thống nh<PERSON>t với local database
"""

import psycopg2
import psycopg2.extras
from datetime import datetime

def get_db_connection():
    """Kết nối database PostgreSQL trên server"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def check_password_columns(conn):
    """Kiểm tra các cột password hiện có"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🔍 Checking password columns...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            AND column_name LIKE '%password%'
            ORDER BY column_name;
        ''')
        
        columns = cursor.fetchall()
        print(f"📋 Found {len(columns)} password-related columns:")
        
        has_password = False
        has_password_hash = False
        
        for col in columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            print(f"  - {col['column_name']}: {col['data_type']} {nullable}")
            
            if col['column_name'] == 'password':
                has_password = True
            elif col['column_name'] == 'password_hash':
                has_password_hash = True
        
        cursor.close()
        return has_password, has_password_hash
        
    except Exception as e:
        print(f"❌ Error checking columns: {e}")
        return False, False

def fix_password_column_server(conn):
    """Fix password column trên server - rename password_hash to password"""
    try:
        cursor = conn.cursor()
        
        print("\n🔧 Starting server password column fix...")
        
        # Step 1: Check current state
        has_password, has_password_hash = check_password_columns(conn)
        
        # Step 2: Handle different scenarios
        if has_password and has_password_hash:
            print("\n🔧 Both columns exist - removing password_hash column...")
            
            # Keep password column, drop password_hash
            cursor.execute('ALTER TABLE "Users" DROP COLUMN IF EXISTS password_hash;')
            print("✅ Dropped password_hash column")
            
        elif has_password_hash and not has_password:
            print("\n🔧 Only password_hash exists - renaming to password...")
            
            # Rename password_hash to password
            cursor.execute('ALTER TABLE "Users" RENAME COLUMN password_hash TO password;')
            print("✅ Renamed password_hash to password")
            
        elif has_password and not has_password_hash:
            print("\n🔧 Only password exists - good!")
            print("✅ Password column already exists and is correct")
            
        else:
            print("\n❌ No password columns found!")
            
            # Create password column
            cursor.execute('''
                ALTER TABLE "Users" 
                ADD COLUMN password TEXT NOT NULL DEFAULT 'temp_password';
            ''')
            print("✅ Created password column")
        
        # Step 3: Verify final structure
        print("\n🔧 Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            AND column_name LIKE '%password%'
            ORDER BY column_name;
        ''')
        
        final_cols = cursor.fetchall()
        print("📋 Final password columns:")
        for col in final_cols:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            print(f"  - {col[0]}: {col[1]} {nullable}")
        
        # Step 4: Test INSERT
        print("\n🔧 Testing INSERT with password...")
        
        try:
            cursor.execute('SAVEPOINT test_insert;')
            
            test_username = f"test_password_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            cursor.execute('''
                INSERT INTO "Users" (username, password, role, unit_code, phone, email, mp_balance, is_deleted)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING user_id;
            ''', (test_username, 'test_hash_123', 'member', 'TEST002', '+84123456789', '<EMAIL>', 0, 0))
            
            test_user_id = cursor.fetchone()[0]
            print(f"✅ Test INSERT successful, got user_id: {test_user_id}")
            
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
            print("✅ Test INSERT rolled back")
            
        except Exception as e:
            print(f"❌ Test INSERT failed: {e}")
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
            return False
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing password column: {e}")
        return False

def update_code_to_use_password():
    """Cập nhật code để sử dụng cột password thay vì password_hash"""
    
    print("\n📝 Code changes needed:")
    print("=" * 50)
    print("Trong file mip_system.py, cần thay đổi:")
    print()
    print("1. Route /register:")
    print("   FROM: INSERT INTO \"Users\" (username, password_hash, role, ...)")
    print("   TO:   INSERT INTO \"Users\" (username, password, role, ...)")
    print()
    print("2. Route /add_user:")
    print("   FROM: INSERT INTO \"Users\" (username, password_hash, role, ...)")
    print("   TO:   INSERT INTO \"Users\" (username, password, role, ...)")
    print()
    print("3. Route /edit_user:")
    print("   FROM: UPDATE \"Users\" SET username=%s, password_hash=%s, ...")
    print("   TO:   UPDATE \"Users\" SET username=%s, password=%s, ...")
    print()
    print("4. Route /api/auth/login:")
    print("   FROM: SELECT user_id, username, password_hash, role, ...")
    print("   TO:   SELECT user_id, username, password, role, ...")
    print()
    print("✅ Sau khi chạy migration này, hãy cập nhật code theo hướng dẫn trên!")

def main():
    """Main function"""
    print("🚀 Fixing server password column...")
    print("🎯 Goal: Rename password_hash to password for consistency")
    print("=" * 60)
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database")
        return
    
    try:
        # Fix password column
        success = fix_password_column_server(conn)
        
        if success:
            conn.commit()
            print("\n✅ Server password column fix completed successfully!")
            print("🎉 Database now uses 'password' column consistently!")
            
            # Show code update instructions
            update_code_to_use_password()
            
        else:
            conn.rollback()
            print("\n❌ Server password column fix failed!")
            
    except Exception as e:
        conn.rollback()
        print(f"\n❌ Unexpected error: {e}")
        
    finally:
        conn.close()
        print("\n🔒 Database connection closed")

if __name__ == "__main__":
    main()
