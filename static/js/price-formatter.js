/**
 * Price Formatter with Coffee Cup Support
 * Formats prices according to coffee cup configuration
 */

class PriceFormatter {
    constructor() {
        this.config = {
            enable_coffee_display: false,
            coffee_cup_value: 50000,
            coffee_cup_icon: '☕',
            coffee_icon_type: 'emoji',
            coffee_icon_width: 20,
            coffee_icon_height: 20
        };
        this.loadConfig();
    }

    async loadConfig() {
        try {
            const response = await fetch('/api/coffee-config');
            const data = await response.json();
            
            if (data.success) {
                this.config = data.config;
            }
        } catch (error) {
            console.error('Error loading coffee config:', error);
        }
    }

    /**
     * Format price according to coffee cup configuration
     * @param {number} amount - Price in MP
     * @returns {string} Formatted price string
     */
    formatPrice(amount) {
        if (!this.config.enable_coffee_display || this.config.coffee_cup_value <= 0) {
            return this.formatMP(amount);
        }

        const cups = Math.floor(amount / this.config.coffee_cup_value);
        const remainder = amount % this.config.coffee_cup_value;

        // Check icon type from config
        let iconHtml;
        if (this.config.coffee_icon_type === 'image' && this.config.coffee_cup_icon.startsWith('/static/')) {
            // Image icon with custom width/height
            iconHtml = `<img src="${this.config.coffee_cup_icon}" style="width: ${this.config.coffee_icon_width}px; height: ${this.config.coffee_icon_height}px; vertical-align: middle;" alt="coffee">`;
        } else {
            // Emoji icon (default)
            iconHtml = this.config.coffee_cup_icon;
        }

        if (remainder === 0) {
            // Chia hết, chỉ hiển thị ly cafe
            return `${cups} ${iconHtml}`;
        } else {
            // Không chia hết
            if (cups > 0) {
                return `${cups} ${iconHtml} + ${this.formatMP(remainder)}`;
            } else {
                return this.formatMP(amount);
            }
        }
    }

    /**
     * Format price and update DOM element with HTML content (for products only)
     * @param {number} amount - Price in MP
     * @param {HTMLElement} element - DOM element to update
     */
    formatProductPriceToElement(amount, element) {
        if (!this.config.enable_coffee_display || this.config.coffee_cup_value <= 0) {
            element.textContent = this.formatMP(amount);
            return;
        }

        const cups = Math.floor(amount / this.config.coffee_cup_value);
        const remainder = amount % this.config.coffee_cup_value;

        // Clear element first
        element.innerHTML = '';

        if (remainder === 0) {
            // Chia hết, chỉ hiển thị ly cafe
            const textNode = document.createTextNode(`${cups} `);
            element.appendChild(textNode);

            if (this.config.coffee_icon_type === 'image' && (this.config.coffee_cup_icon.startsWith('/static/') || this.config.coffee_cup_icon.includes('/static/'))) {
                // Create image element with custom width/height
                const img = document.createElement('img');
                img.src = this.config.coffee_cup_icon;
                img.style.width = this.config.coffee_icon_width + 'px';
                img.style.height = this.config.coffee_icon_height + 'px';
                img.style.verticalAlign = 'middle';
                img.alt = 'coffee';
                img.style.marginLeft = '2px';
                element.appendChild(img);
            } else {
                // Emoji icon
                const emojiNode = document.createTextNode(this.config.coffee_cup_icon);
                element.appendChild(emojiNode);
            }
        } else {
            // Không chia hết
            if (cups > 0) {
                const cupsText = document.createTextNode(`${cups} `);
                element.appendChild(cupsText);

                if (this.config.coffee_icon_type === 'image' && (this.config.coffee_cup_icon.startsWith('/static/') || this.config.coffee_cup_icon.includes('/static/'))) {
                    // Create image element with custom width/height
                    const img = document.createElement('img');
                    img.src = this.config.coffee_cup_icon;
                    img.style.width = this.config.coffee_icon_width + 'px';
                    img.style.height = this.config.coffee_icon_height + 'px';
                    img.style.verticalAlign = 'middle';
                    img.alt = 'coffee';
                    img.style.marginLeft = '2px';
                    img.style.marginRight = '2px';
                    element.appendChild(img);
                } else {
                    // Emoji icon
                    const emojiNode = document.createTextNode(this.config.coffee_cup_icon);
                    element.appendChild(emojiNode);
                }

                const remainderText = document.createTextNode(` + ${this.formatMP(remainder)}`);
                element.appendChild(remainderText);
            } else {
                element.textContent = this.formatMP(amount);
            }
        }
    }

    /**
     * Format price and update DOM element with HTML content (backward compatibility)
     * @param {number} amount - Price in MP
     * @param {HTMLElement} element - DOM element to update
     */
    formatPriceToElement(amount, element) {
        // For backward compatibility, just format as MP
        element.textContent = this.formatMP(amount);
    }

    /**
     * Format MP with thousand separators
     * @param {number} amount - Amount in MP
     * @returns {string} Formatted MP string
     */
    formatMP(amount) {
        return new Intl.NumberFormat('vi-VN').format(amount) + ' MP';
    }

    /**
     * Get coffee cup icon
     * @returns {string} Coffee cup icon
     */
    getCoffeeIcon() {
        return this.config.coffee_cup_icon;
    }

    /**
     * Check if coffee display is enabled
     * @returns {boolean} True if coffee display is enabled
     */
    isCoffeeDisplayEnabled() {
        return this.config.enable_coffee_display;
    }

    /**
     * Get coffee cup value
     * @returns {number} Value of one coffee cup in MP
     */
    getCoffeeValue() {
        return this.config.coffee_cup_value;
    }

    /**
     * Calculate number of coffee cups for given amount
     * @param {number} amount - Amount in MP
     * @returns {number} Number of coffee cups
     */
    calculateCups(amount) {
        if (this.config.coffee_cup_value <= 0) return 0;
        return Math.floor(amount / this.config.coffee_cup_value);
    }
}

// Create global instance
window.priceFormatter = new PriceFormatter();

// Helper function for backward compatibility - returns plain text only
window.formatPrice = function(amount) {
    return window.priceFormatter.formatMP(amount);
};

// Auto-reload config when page loads
document.addEventListener('DOMContentLoaded', function() {
    window.priceFormatter.loadConfig();
});
