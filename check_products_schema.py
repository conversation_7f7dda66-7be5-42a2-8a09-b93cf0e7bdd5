#!/usr/bin/env python3
"""
Check Products table schema
"""

import psycopg2
import psycopg2.extras

def get_db_connection():
    """Kết nối database PostgreSQL local"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def check_products_schema():
    """Check Products table schema"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Check Products table columns
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Products' AND table_schema = 'public'
            ORDER BY ordinal_position
        ''')
        
        columns = cursor.fetchall()
        
        print("📋 Products table schema:")
        print("-" * 60)
        for col in columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
            print(f"  {col['column_name']:20s} {col['data_type']:15s} {nullable}{default}")
        
        # Check if product_type column exists (might be named differently)
        cursor.execute('''
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'Products' AND table_schema = 'public'
            AND column_name LIKE '%type%'
        ''')
        
        type_columns = cursor.fetchall()
        print(f"\n🔍 Type-related columns: {[col['column_name'] for col in type_columns]}")
        
        # Check sample data
        cursor.execute('SELECT * FROM "Products" LIMIT 3')
        products = cursor.fetchall()
        
        print(f"\n📊 Sample products ({len(products)} rows):")
        if products:
            for product in products:
                print(f"  ID: {product.get('product_id')}, Name: {product.get('name')}, Type: {product.get('product_type')}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking schema: {e}")
        return False

def main():
    """Main function"""
    print("🔍 Checking Products Table Schema")
    print("=" * 50)
    
    check_products_schema()

if __name__ == "__main__":
    main()
