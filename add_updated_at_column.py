#!/usr/bin/env python3
"""
Add updated_at column to ProductCategories table
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_updated_at_column():
    """Add updated_at column to ProductCategories table"""
    
    print("🚀 Adding updated_at column to ProductCategories table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current ProductCategories table structure
        print("\n🔧 Step 1: Checking current ProductCategories table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductCategories' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Current columns: {existing_column_names}")
        
        # Step 2: Add updated_at column if not exists
        if 'updated_at' not in existing_column_names:
            print("\n🔧 Step 2: Adding updated_at column...")
            
            cursor.execute('''
                ALTER TABLE "ProductCategories" 
                ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            ''')
            
            print("  ✅ Added updated_at column (TIMESTAMP DEFAULT CURRENT_TIMESTAMP)")
        else:
            print("\n✅ Step 2: updated_at column already exists")
        
        # Step 3: Update existing records with current timestamp
        print("\n🔧 Step 3: Updating existing records...")
        
        cursor.execute('''
            UPDATE "ProductCategories" 
            SET updated_at = CURRENT_TIMESTAMP 
            WHERE updated_at IS NULL;
        ''')
        
        updated_rows = cursor.rowcount
        print(f"  ✅ Updated {updated_rows} existing records with current timestamp")
        
        # Step 4: Create index for performance
        print("\n🔧 Step 4: Creating index...")
        
        try:
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_product_categories_updated_at 
                ON "ProductCategories"(updated_at);
            ''')
            print("  ✅ Created index: idx_product_categories_updated_at")
        except Exception as e:
            print(f"  ⚠️  Index creation: {e}")
        
        # Step 5: Verify final structure
        print("\n🔧 Step 5: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductCategories' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Final ProductCategories columns:")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 6: Test data
        print("\n🔧 Step 6: Testing data...")
        
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                COUNT(updated_at) as with_updated_at,
                MIN(updated_at) as earliest_update,
                MAX(updated_at) as latest_update
            FROM "ProductCategories";
        ''')
        
        stats = cursor.fetchone()
        print(f"📊 Category statistics:")
        print(f"  - Total categories: {stats[0]}")
        print(f"  - With updated_at: {stats[1]}")
        print(f"  - Earliest update: {stats[2]}")
        print(f"  - Latest update: {stats[3]}")
        
        # Step 7: Test UPDATE query
        print("\n🔧 Step 7: Testing UPDATE query...")
        
        try:
            # Test the problematic UPDATE query
            cursor.execute('''
                SELECT category_id, name FROM "ProductCategories" LIMIT 1;
            ''')
            test_category = cursor.fetchone()
            
            if test_category:
                cursor.execute('''
                    UPDATE "ProductCategories"
                    SET name = %s, description = %s, icon = %s, sort_order = %s,
                        is_active = %s, thumbnail_url = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE category_id = %s
                ''', (test_category[1], 'Test description', 'cil-folder', 0, True, '', test_category[0]))
                
                print(f"  ✅ UPDATE query test successful for category: {test_category[1]}")
            else:
                print("  ⚠️  No categories found for UPDATE test")
                
        except Exception as e:
            print(f"  ❌ UPDATE query test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Migration completed successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test admin category update functionality")
        print("   3. Test category creation")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_updated_at_column()
    sys.exit(0 if success else 1)
