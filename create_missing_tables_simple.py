#!/usr/bin/env python3
"""
Simple script to create missing tables one by one
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def create_table_safe(cursor, table_name, sql, description):
    """Create table safely with error handling"""
    try:
        cursor.execute(sql)
        print(f"✅ {description}")
        return True
    except Exception as e:
        print(f"⚠️  {description}: {e}")
        return False

def main():
    """Create missing tables"""
    
    print("🔧 Creating missing marketplace tables...")
    print("=" * 50)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        conn.autocommit = True  # Auto commit each statement
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Create PackageAccounts table
        print("\n🔧 Creating PackageAccounts table...")
        create_table_safe(cursor, "PackageAccounts", '''
            CREATE TABLE IF NOT EXISTS "PackageAccounts" (
                id SERIAL PRIMARY KEY,
                package_id INTEGER,
                account_id INTEGER,
                is_sold BOOLEAN DEFAULT FALSE,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                sold_at TIMESTAMP,
                order_item_id INTEGER
            );
        ''', "PackageAccounts table created")
        
        # Create WarrantyRequests table
        print("\n🔧 Creating WarrantyRequests table...")
        create_table_safe(cursor, "WarrantyRequests", '''
            CREATE TABLE IF NOT EXISTS "WarrantyRequests" (
                warranty_id SERIAL PRIMARY KEY,
                user_id INTEGER,
                order_item_id INTEGER,
                account_id INTEGER,
                reason VARCHAR(100) NOT NULL,
                description TEXT,
                status VARCHAR(20) DEFAULT 'pending',
                replacement_account_id INTEGER,
                admin_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                resolved_at TIMESTAMP
            );
        ''', "WarrantyRequests table created")
        
        # Create indexes
        print("\n🔧 Creating indexes...")
        indexes = [
            ('idx_package_accounts_package', 'CREATE INDEX IF NOT EXISTS idx_package_accounts_package ON "PackageAccounts"(package_id);'),
            ('idx_package_accounts_account', 'CREATE INDEX IF NOT EXISTS idx_package_accounts_account ON "PackageAccounts"(account_id);'),
            ('idx_warranty_requests_user', 'CREATE INDEX IF NOT EXISTS idx_warranty_requests_user ON "WarrantyRequests"(user_id);'),
            ('idx_warranty_requests_status', 'CREATE INDEX IF NOT EXISTS idx_warranty_requests_status ON "WarrantyRequests"(status);'),
            ('idx_products_category', 'CREATE INDEX IF NOT EXISTS idx_products_category ON "Products"(category_id);'),
            ('idx_products_active', 'CREATE INDEX IF NOT EXISTS idx_products_active ON "Products"(is_active);'),
            ('idx_orders_user', 'CREATE INDEX IF NOT EXISTS idx_orders_user ON "Orders"(user_id);'),
            ('idx_orders_status', 'CREATE INDEX IF NOT EXISTS idx_orders_status ON "Orders"(status);')
        ]
        
        for idx_name, idx_sql in indexes:
            create_table_safe(cursor, idx_name, idx_sql, f"Index {idx_name}")
        
        # Validate all tables
        print("\n🔍 Validating tables...")
        tables = [
            "ProductCategories", "Products", "ProductFiles", "AccountPackages", 
            "PackageAccounts", "Orders", "OrderItems", "AFFPackages", 
            "UserSubscriptions", "ProductTypes", "WarrantyRequests", 
            "MarketplaceTransactions", "Config"
        ]
        
        all_good = True
        for table in tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{table}";')
                count = cursor.fetchone()[0]
                print(f"✅ {table}: {count} records")
            except Exception as e:
                print(f"❌ {table}: {e}")
                all_good = False
        
        # Check revenue_enabled column
        print("\n🔍 Checking revenue_enabled column...")
        try:
            cursor.execute('SELECT COUNT(*) FROM "Accounts" WHERE revenue_enabled IS NOT NULL;')
            total_with_column = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM "Accounts" WHERE revenue_enabled = TRUE;')
            enabled_count = cursor.fetchone()[0]
            print(f"✅ Accounts.revenue_enabled: {enabled_count}/{total_with_column} enabled")
        except Exception as e:
            print(f"❌ revenue_enabled column: {e}")
            all_good = False
        
        # Check coffee configs
        print("\n🔍 Checking coffee configs...")
        coffee_configs = ['enable_coffee_display', 'coffee_cup_value', 'coffee_cup_icon', 'coffee_icon_type', 'coffee_icon_width', 'coffee_icon_height']
        
        for key in coffee_configs:
            try:
                cursor.execute('SELECT config_value FROM "Config" WHERE config_key = %s;', (key,))
                result = cursor.fetchone()
                if result:
                    print(f"✅ {key}: {result[0]}")
                else:
                    print(f"❌ {key}: missing")
                    all_good = False
            except Exception as e:
                print(f"❌ {key}: {e}")
                all_good = False
        
        cursor.close()
        conn.close()
        
        if all_good:
            print(f"\n🎉 All marketplace tables created successfully!")
            print("\n📝 Next steps:")
            print("   1. Test marketplace at /marketplace")
            print("   2. Test AFF packages at /marketplace/aff-packages") 
            print("   3. Configure coffee display at /config")
            print("   4. Test revenue toggle in /accounts")
            print("\n🚀 Marketplace is ready to use!")
        else:
            print(f"\n⚠️  Some issues found, but core tables are created")
        
        return all_good
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
