#!/usr/bin/env python3
"""
Update local Users table to match server structure
"""

import psycopg2
import psycopg2.extras
from db_config import PG_CONFIG

def update_users_table():
    """Update Users table structure to match server"""
    conn = psycopg2.connect(**PG_CONFIG)
    cursor = conn.cursor()
    
    try:
        print("🔧 Updating Users table structure...")
        
        # Step 1: Check if password_hash column exists
        cursor.execute('''
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND column_name = 'password_hash'
        ''')
        
        has_password_hash = cursor.fetchone() is not None
        
        if has_password_hash:
            print("📋 Found password_hash column, renaming to password...")
            
            # Rename password_hash to password
            cursor.execute('ALTER TABLE "Users" RENAME COLUMN password_hash TO password')
            print("   ✅ Renamed password_hash → password")
        else:
            print("   ℹ️ password_hash column not found, checking for password column...")
            
            cursor.execute('''
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'Users' AND column_name = 'password'
            ''')
            
            has_password = cursor.fetchone() is not None
            
            if has_password:
                print("   ✅ password column already exists")
            else:
                print("   ❌ Neither password_hash nor password column found!")
                return False
        
        # Step 2: Verify final structure
        print("\n📋 Verifying updated structure...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' 
            AND column_name IN ('password', 'password_hash', 'email', 'email_verified', 'email_verified_at')
            ORDER BY column_name
        ''')
        
        columns = cursor.fetchall()
        print("Updated columns:")
        for col in columns:
            nullable = 'NULL' if col[2] == 'YES' else 'NOT NULL'
            print(f"   {col[0]}: {col[1]} {nullable}")
        
        # Step 3: Test insert query
        print("\n🧪 Testing insert query structure...")
        try:
            # This should not actually insert, just test the query structure
            cursor.execute('''
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'Users' 
                AND column_name IN ('username', 'password', 'role', 'email', 'phone', 'unit_code', 'mp_balance', 'email_verified', 'email_verified_at', 'is_deleted')
                ORDER BY column_name
            ''')
            
            available_columns = [row[0] for row in cursor.fetchall()]
            print(f"Available columns for insert: {available_columns}")
            
            required_columns = ['username', 'password', 'role', 'email', 'phone', 'unit_code', 'mp_balance', 'email_verified', 'email_verified_at', 'is_deleted']
            missing_columns = [col for col in required_columns if col not in available_columns]
            
            if missing_columns:
                print(f"❌ Missing columns: {missing_columns}")
                return False
            else:
                print("✅ All required columns available for registration")
        
        except Exception as e:
            print(f"❌ Error testing insert structure: {e}")
            return False
        
        conn.commit()
        print("\n🎉 Users table updated successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating Users table: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        return False
    
    finally:
        cursor.close()
        conn.close()

def test_registration_insert():
    """Test registration insert with new structure"""
    conn = psycopg2.connect(**PG_CONFIG)
    cursor = conn.cursor()
    
    try:
        print("\n🧪 Testing registration insert...")
        
        # Test data
        test_data = {
            'username': 'test_user_structure',
            'password': 'hashed_password_test',
            'role': 'leader',
            'email': '<EMAIL>',
            'phone': '+84123456789',
            'unit_code': 'TEST001',
            'mp_balance': 0,
            'email_verified': True,
            'is_deleted': 0
        }
        
        # Try the insert query that registration uses
        insert_query = '''
            INSERT INTO "Users" (username, password, role, email, phone, unit_code, mp_balance, 
                               email_verified, email_verified_at, is_deleted)
            VALUES (%(username)s, %(password)s, %(role)s, %(email)s, %(phone)s, %(unit_code)s, %(mp_balance)s, 
                   %(email_verified)s, CURRENT_TIMESTAMP, %(is_deleted)s)
            RETURNING user_id
        '''
        
        cursor.execute(insert_query, test_data)
        user_id = cursor.fetchone()[0]
        
        print(f"✅ Test insert successful! Created user ID: {user_id}")
        
        # Clean up test user
        cursor.execute('DELETE FROM "Users" WHERE user_id = %s', (user_id,))
        print("✅ Test user cleaned up")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ Test insert failed: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        return False
    
    finally:
        cursor.close()
        conn.close()

def main():
    """Main function"""
    print("🔧 Local Users Table Update Tool")
    print("=" * 60)
    
    # Update table structure
    update_success = update_users_table()
    
    if update_success:
        # Test registration insert
        test_success = test_registration_insert()
        
        if test_success:
            print("\n🎉 Users table update completed successfully!")
            print("\n📋 What was updated:")
            print("  ✅ Renamed password_hash → password")
            print("  ✅ Verified email verification columns")
            print("  ✅ Tested registration insert query")
            print("\n🚀 Registration should now work!")
        else:
            print("\n⚠️ Table updated but insert test failed")
    else:
        print("\n❌ Table update failed!")

if __name__ == "__main__":
    main()
