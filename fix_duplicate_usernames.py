#!/usr/bin/env python3
"""
Fix duplicate usernames by renaming duplicates
"""

import psycopg2
import psycopg2.extras
from datetime import datetime

def get_db_connection():
    """Kết nối database PostgreSQL"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def find_duplicate_usernames(conn):
    """Tìm username trùng lặp"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🔍 Finding duplicate usernames...")
        cursor.execute('''
            SELECT username, COUNT(*) as count, 
                   array_agg(user_id ORDER BY user_id) as user_ids
            FROM "Users"
            WHERE (is_deleted = 0 OR is_deleted IS NULL)
            GROUP BY username
            HAVING COUNT(*) > 1
            ORDER BY count DESC, username;
        ''')
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"⚠️  Found {len(duplicates)} duplicate usernames:")
            for dup in duplicates:
                print(f"  - '{dup['username']}': {dup['count']} occurrences (IDs: {dup['user_ids']})")
        else:
            print("✅ No duplicate usernames found")
        
        cursor.close()
        return duplicates
        
    except Exception as e:
        print(f"❌ Error finding duplicates: {e}")
        return []

def get_user_details(conn, user_ids):
    """Lấy chi tiết user"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        cursor.execute('''
            SELECT user_id, username, role, team_id, unit_code, mp_balance, total_videos,
                   email, phone, created_at
            FROM "Users"
            WHERE user_id = ANY(%s)
            ORDER BY user_id;
        ''', (user_ids,))
        
        users = cursor.fetchall()
        cursor.close()
        return users
        
    except Exception as e:
        print(f"❌ Error getting user details: {e}")
        return []

def fix_duplicate_usernames(conn):
    """Fix duplicate usernames"""
    try:
        cursor = conn.cursor()
        
        print("\n🔧 Fixing duplicate usernames...")
        
        # Find duplicates
        duplicates = find_duplicate_usernames(conn)
        
        if not duplicates:
            print("✅ No duplicates to fix")
            return True
        
        fixed_count = 0
        
        for dup in duplicates:
            username = dup['username']
            user_ids = dup['user_ids']
            
            print(f"\n--- Fixing '{username}' ---")
            
            # Get user details
            users = get_user_details(conn, user_ids)
            
            if not users:
                print(f"❌ Could not get details for users: {user_ids}")
                continue
            
            # Keep the first user (oldest), rename others
            keep_user = users[0]
            rename_users = users[1:]
            
            print(f"✅ Keeping user {keep_user['user_id']} (oldest)")
            
            for i, user in enumerate(rename_users, 1):
                old_username = user['username']
                new_username = f"{old_username}_{user['user_id']}"
                
                print(f"🔧 Renaming user {user['user_id']}: '{old_username}' -> '{new_username}'")
                
                # Update username
                cursor.execute('''
                    UPDATE "Users" 
                    SET username = %s
                    WHERE user_id = %s;
                ''', (new_username, user['user_id']))
                
                print(f"✅ User {user['user_id']} renamed successfully")
                fixed_count += 1
        
        print(f"\n✅ Fixed {fixed_count} duplicate usernames")
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing duplicates: {e}")
        return False

def verify_no_duplicates(conn):
    """Verify no duplicates remain"""
    try:
        cursor = conn.cursor()
        
        print("\n🔍 Verifying no duplicates remain...")
        cursor.execute('''
            SELECT username, COUNT(*) as count
            FROM "Users"
            WHERE (is_deleted = 0 OR is_deleted IS NULL)
            GROUP BY username
            HAVING COUNT(*) > 1;
        ''')
        
        remaining_duplicates = cursor.fetchall()
        
        if remaining_duplicates:
            print(f"❌ Still have {len(remaining_duplicates)} duplicate usernames:")
            for dup in remaining_duplicates:
                print(f"  - '{dup[0]}': {dup[1]} occurrences")
            return False
        else:
            print("✅ No duplicate usernames remain")
            return True
        
        cursor.close()
        
    except Exception as e:
        print(f"❌ Error verifying: {e}")
        return False

def show_final_usernames(conn):
    """Show final username list"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n📋 Final username list:")
        cursor.execute('''
            SELECT user_id, username, role, team_id
            FROM "Users"
            WHERE (is_deleted = 0 OR is_deleted IS NULL)
            ORDER BY user_id;
        ''')
        
        users = cursor.fetchall()
        
        for user in users:
            print(f"  - ID {user['user_id']}: '{user['username']}' ({user['role']}, team: {user['team_id']})")
        
        cursor.close()
        
    except Exception as e:
        print(f"❌ Error showing usernames: {e}")

def main():
    """Main function"""
    print("🚀 Fixing duplicate usernames...")
    print("=" * 60)
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database")
        return
    
    try:
        # Fix duplicates
        success = fix_duplicate_usernames(conn)
        
        if not success:
            conn.rollback()
            print("\n❌ Failed to fix duplicates!")
            return
        
        # Verify no duplicates
        no_duplicates = verify_no_duplicates(conn)
        
        if no_duplicates:
            conn.commit()
            print("\n✅ All duplicate usernames fixed successfully!")
            
            # Show final list
            show_final_usernames(conn)
            
            print("\n🎉 Ready to add unique constraint!")
        else:
            conn.rollback()
            print("\n❌ Still have duplicates - rollback changes!")
            
    except Exception as e:
        conn.rollback()
        print(f"\n❌ Unexpected error: {e}")
        
    finally:
        conn.close()
        print("\n🔒 Database connection closed")

if __name__ == "__main__":
    main()
