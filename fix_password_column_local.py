#!/usr/bin/env python3
"""
Fix password column issue in local database
Đ<PERSON>i tên cột password thành password_hash để khớp với server
"""

import psycopg2
import psycopg2.extras
from datetime import datetime

def get_db_connection():
    """Kết nối database PostgreSQL"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def check_password_columns(conn):
    """Ki<PERSON>m tra các cột password hiện có"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🔍 Checking password columns...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            AND column_name LIKE '%password%'
            ORDER BY column_name;
        ''')
        
        columns = cursor.fetchall()
        print(f"📋 Found {len(columns)} password-related columns:")
        
        has_password = False
        has_password_hash = False
        
        for col in columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            print(f"  - {col['column_name']}: {col['data_type']} {nullable}")
            
            if col['column_name'] == 'password':
                has_password = True
            elif col['column_name'] == 'password_hash':
                has_password_hash = True
        
        cursor.close()
        return has_password, has_password_hash
        
    except Exception as e:
        print(f"❌ Error checking columns: {e}")
        return False, False

def fix_password_column(conn):
    """Fix password column issue"""
    try:
        cursor = conn.cursor()
        
        print("\n🔧 Starting password column fix...")
        
        # Step 1: Check current state
        has_password, has_password_hash = check_password_columns(conn)
        
        # Step 2: Handle different scenarios
        if has_password and has_password_hash:
            print("\n🔧 Both columns exist - removing password column...")
            
            # Drop password column (keep password_hash)
            cursor.execute('ALTER TABLE "Users" DROP COLUMN IF EXISTS password;')
            print("✅ Dropped password column")
            
        elif has_password and not has_password_hash:
            print("\n🔧 Only password exists - renaming to password_hash...")
            
            # Rename password to password_hash
            cursor.execute('ALTER TABLE "Users" RENAME COLUMN password TO password_hash;')
            print("✅ Renamed password to password_hash")
            
        elif has_password_hash and not has_password:
            print("\n🔧 Only password_hash exists - good!")
            print("✅ Password_hash column already exists and is correct")
            
        else:
            print("\n❌ No password columns found!")
            
            # Create password_hash column
            cursor.execute('''
                ALTER TABLE "Users" 
                ADD COLUMN password_hash TEXT NOT NULL DEFAULT 'temp_password';
            ''')
            print("✅ Created password_hash column")
        
        # Step 3: Verify final structure
        print("\n🔧 Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            AND column_name LIKE '%password%'
            ORDER BY column_name;
        ''')
        
        final_cols = cursor.fetchall()
        print("📋 Final password columns:")
        for col in final_cols:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            print(f"  - {col[0]}: {col[1]} {nullable}")
        
        # Step 4: Test INSERT
        print("\n🔧 Testing INSERT with password_hash...")
        
        try:
            cursor.execute('SAVEPOINT test_insert;')
            
            test_username = f"test_password_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            cursor.execute('''
                INSERT INTO "Users" (username, password_hash, role, unit_code, phone, email, mp_balance, is_deleted)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING user_id;
            ''', (test_username, 'test_hash_123', 'member', 'TEST002', '+84123456789', '<EMAIL>', 0, 0))
            
            test_user_id = cursor.fetchone()[0]
            print(f"✅ Test INSERT successful, got user_id: {test_user_id}")
            
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
            print("✅ Test INSERT rolled back")
            
        except Exception as e:
            print(f"❌ Test INSERT failed: {e}")
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
            return False
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing password column: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Starting password column fix for local database...")
    print("=" * 60)
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database")
        return
    
    try:
        # Fix password column
        success = fix_password_column(conn)
        
        if success:
            conn.commit()
            print("\n✅ Password column fix completed successfully!")
            print("🎉 Local database is now compatible with server!")
        else:
            conn.rollback()
            print("\n❌ Password column fix failed!")
            
    except Exception as e:
        conn.rollback()
        print(f"\n❌ Unexpected error: {e}")
        
    finally:
        conn.close()
        print("\n🔒 Database connection closed")

if __name__ == "__main__":
    main()
