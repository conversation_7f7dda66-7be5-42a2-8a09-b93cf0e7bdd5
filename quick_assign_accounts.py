#!/usr/bin/env python3
"""
Quick fix: Assign available accounts to product package
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def quick_assign_accounts():
    """Quick assign available accounts to product"""
    
    print("🚀 Quick assigning accounts to product...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Find the product
        cursor.execute('''
            SELECT product_id, name, product_type
            FROM "Products" 
            WHERE name ILIKE '%Tiktok 1k Follower%'
            ORDER BY product_id DESC
            LIMIT 1;
        ''')
        
        product = cursor.fetchone()
        if not product:
            print("❌ Product not found")
            return False
        
        product_id, product_name, product_type = product
        print(f"🎯 Product: {product_name} (ID: {product_id}, Type: {product_type})")
        
        if product_type != 'account':
            print(f"❌ Product type is '{product_type}', not 'account'")
            return False
        
        # Step 2: Check/Create AccountPackage
        cursor.execute('''
            SELECT package_id FROM "AccountPackages" WHERE product_id = %s;
        ''', (product_id,))
        
        package = cursor.fetchone()
        if not package:
            print("🔧 Creating AccountPackage record...")
            cursor.execute('''
                INSERT INTO "AccountPackages" (product_id, package_name, assigned_count)
                VALUES (%s, %s, 0)
                RETURNING package_id;
            ''', (product_id, product_name))
            package_id = cursor.fetchone()[0]
            print(f"✅ Created AccountPackage with ID: {package_id}")
        else:
            package_id = package[0]
            print(f"✅ Found existing AccountPackage ID: {package_id}")
        
        # Step 3: Find available accounts
        cursor.execute('''
            SELECT account_id, account_name
            FROM "Accounts" 
            WHERE (marketplace_status = 'available' OR marketplace_status IS NULL)
            AND (package_id IS NULL)
            AND (is_sold = false OR is_sold IS NULL)
            LIMIT 10;
        ''')
        
        available_accounts = cursor.fetchall()
        if not available_accounts:
            print("❌ No available accounts to assign")
            return False
        
        print(f"📊 Found {len(available_accounts)} available accounts")
        
        # Step 4: Assign accounts
        assigned_count = 0
        for account_id, account_name in available_accounts:
            try:
                # Update Accounts table
                cursor.execute('''
                    UPDATE "Accounts" 
                    SET package_id = %s, marketplace_status = 'reserved'
                    WHERE account_id = %s;
                ''', (product_id, account_id))
                
                # Insert into PackageAccounts
                cursor.execute('''
                    INSERT INTO "PackageAccounts" (package_id, account_id, is_sold)
                    VALUES (%s, %s, false)
                    ON CONFLICT DO NOTHING;
                ''', (package_id, account_id))
                
                assigned_count += 1
                print(f"✅ Assigned account: {account_name} (ID: {account_id})")
                
            except Exception as e:
                print(f"❌ Failed to assign {account_name}: {e}")
        
        # Step 5: Update package assigned count
        cursor.execute('''
            UPDATE "AccountPackages" 
            SET assigned_count = %s
            WHERE package_id = %s;
        ''', (assigned_count, package_id))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Successfully assigned {assigned_count} accounts to '{product_name}'!")
        print("📝 Next steps:")
        print("   1. Test purchasing the product again")
        print("   2. Check that accounts are properly assigned to buyers")
        
        return True
        
    except Exception as e:
        print(f"❌ Assignment failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = quick_assign_accounts()
    sys.exit(0 if success else 1)
