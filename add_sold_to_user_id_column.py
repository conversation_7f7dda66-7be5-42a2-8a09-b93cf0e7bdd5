#!/usr/bin/env python3
"""
Add sold_to_user_id column to Accounts table
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_sold_to_user_id_column():
    """Add sold_to_user_id column to Accounts table"""
    
    print("🔧 Adding sold_to_user_id column to Accounts table...")
    print("=" * 60)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Check current Accounts table structure
        print("\n🔍 Checking current Accounts table structure...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        
        print(f"📋 Current Accounts table has {len(columns)} columns")
        
        # Check if sold_to_user_id already exists
        if 'sold_to_user_id' in column_names:
            print("✅ sold_to_user_id column already exists")
        else:
            print("🔧 Adding sold_to_user_id column...")
            
            # Add the column
            cursor.execute('''
                ALTER TABLE "Accounts" 
                ADD COLUMN sold_to_user_id INTEGER;
            ''')
            
            print("✅ sold_to_user_id column added successfully")
            
            # Add comment to explain the column
            cursor.execute('''
                COMMENT ON COLUMN "Accounts".sold_to_user_id 
                IS 'ID của user đã mua account này từ marketplace';
            ''')
            
            print("✅ Added column comment")
        
        # Check if we need to add other marketplace-related columns
        marketplace_columns = {
            'sold_at': 'TIMESTAMP',
            'sold_price': 'INTEGER',
            'order_item_id': 'INTEGER'
        }
        
        for col_name, col_type in marketplace_columns.items():
            if col_name not in column_names:
                print(f"🔧 Adding {col_name} column...")
                try:
                    cursor.execute(f'ALTER TABLE "Accounts" ADD COLUMN {col_name} {col_type};')
                    print(f"✅ {col_name} column added")
                except Exception as e:
                    print(f"⚠️  Could not add {col_name}: {e}")
            else:
                print(f"✅ {col_name} column already exists")
        
        # Add indexes for performance
        print("\n🔧 Creating indexes...")
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_accounts_sold_to_user ON "Accounts"(sold_to_user_id);',
            'CREATE INDEX IF NOT EXISTS idx_accounts_sold_at ON "Accounts"(sold_at);',
            'CREATE INDEX IF NOT EXISTS idx_accounts_order_item ON "Accounts"(order_item_id);'
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
                print(f"✅ Index created: {index_sql.split('idx_')[1].split(' ')[0]}")
            except Exception as e:
                print(f"⚠️  Index creation issue: {e}")
        
        # Commit changes
        conn.commit()
        
        # Validate final structure
        print("\n🔍 Final Accounts table structure...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            AND column_name IN ('sold_to_user_id', 'sold_at', 'sold_price', 'order_item_id', 'revenue_enabled')
            ORDER BY ordinal_position;
        ''')
        
        marketplace_cols = cursor.fetchall()
        print(f"📋 Marketplace-related columns in Accounts:")
        for col in marketplace_cols:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Test the query that was failing
        print("\n🔍 Testing the failing query...")
        try:
            # Simulate the query from line 1147 (with user_id = 2 as example)
            test_user_id = 2
            cursor.execute('''
                SELECT COUNT(*) 
                FROM "Accounts" 
                WHERE is_deleted = 0 
                AND (team_id = %s OR sold_to_user_id IN (SELECT user_id FROM "Users" WHERE user_id = %s))
            ''', (test_user_id, test_user_id))
            
            count = cursor.fetchone()[0]
            print(f"✅ Query test successful - found {count} accounts for user {test_user_id}")
            
        except Exception as e:
            print(f"❌ Query test failed: {e}")
        
        # Check sample data
        cursor.execute('SELECT COUNT(*) FROM "Accounts" WHERE is_deleted = 0;')
        total_accounts = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM "Accounts" WHERE sold_to_user_id IS NOT NULL;')
        sold_accounts = cursor.fetchone()[0]
        
        print(f"\n📊 Account statistics:")
        print(f"    - Total active accounts: {total_accounts}")
        print(f"    - Sold accounts: {sold_accounts}")
        print(f"    - Available accounts: {total_accounts - sold_accounts}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 sold_to_user_id column added successfully!")
        print("📝 Next steps:")
        print("   1. Restart sapmmo service")
        print("   2. Test /accounts page")
        print("   3. Test marketplace account purchasing")
        
        return True
        
    except Exception as e:
        print(f"❌ Addition failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_sold_to_user_id_column()
    sys.exit(0 if success else 1)
