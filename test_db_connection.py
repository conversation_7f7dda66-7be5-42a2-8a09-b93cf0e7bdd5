#!/usr/bin/env python3
"""
Test database connection using db_config.py
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def test_connection():
    """Test database connection"""
    
    print("🔍 Testing Database Connection...")
    print("=" * 40)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        print(f"✅ Successfully loaded db_config.py")
        print(f"   Host: {PG_CONFIG['host']}")
        print(f"   Database: {PG_CONFIG['database']}")
        print(f"   User: {PG_CONFIG['user']}")
        print(f"   Password: {'***' if PG_CONFIG['password'] else '(empty)'}")
        
        # Test connection
        import psycopg2
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        # Test query
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"✅ Database connection successful")
        print(f"   PostgreSQL version: {version}")
        
        # Check if sapmmo database exists
        cursor.execute("SELECT current_database();")
        current_db = cursor.fetchone()[0]
        print(f"✅ Connected to database: {current_db}")
        
        # Check some tables
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('Users', 'Accounts', 'Teams')
            ORDER BY table_name;
        """)
        
        tables = cursor.fetchall()
        if tables:
            print(f"✅ Found core tables: {[t[0] for t in tables]}")
        else:
            print("⚠️  Core tables not found")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 Database connection test passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import db_config: {e}")
        print(f"   Make sure db_config.py exists in {APP_DIR}")
        return False
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    success = test_connection()
    sys.exit(0 if success else 1)
