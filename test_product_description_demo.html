<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Product Description</title>
    <link href="https://cdn.jsdelivr.net/npm/@coreui/coreui@5.0.0/dist/css/coreui.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@coreui/icons@3.0.1/css/all.min.css" rel="stylesheet">
    <style>
        .description-content {
            line-height: 1.6;
            color: #495057;
        }
        .description-content h4,
        .description-content h5,
        .description-content h6 {
            margin-top: 1.5rem;
            margin-bottom: 0.75rem;
        }
        .description-content ul,
        .description-content ol {
            padding-left: 1.5rem;
        }
        .description-content li {
            margin-bottom: 0.5rem;
        }
        .description-content strong {
            color: #212529;
            font-weight: 600;
        }
        .description-content em {
            font-style: italic;
            color: #6c757d;
        }
        .description-content a {
            color: #00C6AE;
            text-decoration: none;
        }
        .description-content a:hover {
            color: #00a693;
            text-decoration: underline;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="cil-star me-2"></i>Demo: Product Description Enhancement</h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- Sample Product 1 -->
                        <div class="mb-5">
                            <h5 class="text-primary mb-3">📱 Gói Account TikTok Premium</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <img src="https://via.placeholder.com/300x200/00C6AE/white?text=TikTok+Account" 
                                         class="img-fluid rounded shadow-sm" alt="TikTok Account">
                                </div>
                                <div class="col-md-8">
                                    <p class="text-muted mb-3"><i class="cil-info me-2"></i>Gói tài khoản TikTok chất lượng cao với follower thật</p>
                                    <div class="mb-3">
                                        <span class="badge bg-info me-2">Account Package</span>
                                        <span class="badge bg-warning">Nổi bật</span>
                                    </div>
                                    <h4 class="text-primary mb-3">500,000 MP</h4>
                                    <div class="mb-3">
                                        <strong><i class="cil-layers me-2"></i>Tình trạng:</strong> 
                                        <span class="text-success">15 còn lại</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Description Section -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card border-0 bg-light">
                                        <div class="card-header bg-transparent border-0 pb-0">
                                            <h6 class="mb-0"><i class="cil-description me-2 text-primary"></i>Mô tả chi tiết</h6>
                                        </div>
                                        <div class="card-body pt-2">
                                            <div class="description-content" id="demo1">
                                                <!-- Will be populated by JavaScript -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Sample Product 2 -->
                        <div class="mb-5">
                            <h5 class="text-primary mb-3">🎯 Khóa học AI Video Formula</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <img src="https://via.placeholder.com/300x200/FF6B6B/white?text=AI+Video+Course" 
                                         class="img-fluid rounded shadow-sm" alt="AI Video Course">
                                </div>
                                <div class="col-md-8">
                                    <p class="text-muted mb-3"><i class="cil-info me-2"></i>Học cách tạo video AI viral trên TikTok</p>
                                    <div class="mb-3">
                                        <span class="badge bg-info me-2">Course</span>
                                        <span class="badge bg-success">Mới</span>
                                    </div>
                                    <h4 class="text-primary mb-3">1,200,000 MP</h4>
                                    <div class="mb-3">
                                        <strong><i class="cil-layers me-2"></i>Tình trạng:</strong> 
                                        <span class="text-success">Không giới hạn</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Description Section -->
                            <div class="row mt-4">
                                <div class="col-12">
                                    <div class="card border-0 bg-light">
                                        <div class="card-header bg-transparent border-0 pb-0">
                                            <h6 class="mb-0"><i class="cil-description me-2 text-primary"></i>Mô tả chi tiết</h6>
                                        </div>
                                        <div class="card-body pt-2">
                                            <div class="description-content" id="demo2">
                                                <!-- Will be populated by JavaScript -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Features -->
                        <div class="alert alert-info">
                            <h6><i class="cil-lightbulb me-2"></i>Tính năng mới:</h6>
                            <ul class="mb-0">
                                <li>✅ Modal size lớn hơn (modal-xl)</li>
                                <li>✅ Phần mô tả chi tiết riêng biệt với card đẹp</li>
                                <li>✅ Format markdown: **bold**, *italic*, ### headers</li>
                                <li>✅ Hỗ trợ bullet points và numbered lists</li>
                                <li>✅ Links với icon external</li>
                                <li>✅ CSS styling chuyên nghiệp</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@coreui/coreui@5.0.0/dist/js/coreui.bundle.min.js"></script>
    <script>
        function formatDescription(description) {
            if (!description) return '';
            
            // Convert line breaks to HTML
            let formatted = description.replace(/\n/g, '<br>');
            
            // Format bullet points
            formatted = formatted.replace(/^[-*•]\s+(.+)$/gm, '<li class="mb-1">$1</li>');
            
            // Wrap consecutive list items in ul tags
            formatted = formatted.replace(/(<li[^>]*>.*?<\/li>(?:\s*<br>\s*<li[^>]*>.*?<\/li>)*)/gs, '<ul class="mb-3">$1</ul>');
            
            // Format numbered lists
            formatted = formatted.replace(/^(\d+)\.\s+(.+)$/gm, '<li class="mb-1">$2</li>');
            formatted = formatted.replace(/(<li[^>]*>.*?<\/li>(?:\s*<br>\s*<li[^>]*>.*?<\/li>)*)/gs, '<ol class="mb-3">$1</ol>');
            
            // Format bold text **text** or __text__
            formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            formatted = formatted.replace(/__(.*?)__/g, '<strong>$1</strong>');
            
            // Format italic text *text* or _text_
            formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');
            formatted = formatted.replace(/_(.*?)_/g, '<em>$1</em>');
            
            // Format headers ### Header
            formatted = formatted.replace(/^###\s+(.+)$/gm, '<h6 class="text-primary mt-3 mb-2">$1</h6>');
            formatted = formatted.replace(/^##\s+(.+)$/gm, '<h5 class="text-primary mt-3 mb-2">$1</h5>');
            formatted = formatted.replace(/^#\s+(.+)$/gm, '<h4 class="text-primary mt-3 mb-2">$1</h4>');
            
            // Format links [text](url)
            formatted = formatted.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" class="text-decoration-none">$1 <i class="cil-external-link"></i></a>');
            
            // Clean up extra br tags
            formatted = formatted.replace(/<br>\s*<br>/g, '<br>');
            
            return formatted;
        }

        // Sample descriptions
        const description1 = `## Gói Account TikTok Premium

**Đặc điểm nổi bật:**
- Follower từ 10K - 50K thật 100%
- Tương tác cao, engagement rate > 3%
- Account đã verify, có tick xanh
- Bảo hành 30 ngày

### Thông tin chi tiết:
- **Độ tuổi account:** 6-12 tháng
- **Niche:** Lifestyle, Entertainment, Dance
- **Quốc gia:** Việt Nam, Thái Lan, Philippines
- **Gender:** 60% nữ, 40% nam

### Quy trình giao hàng:
1. Thanh toán đơn hàng
2. Nhận thông tin account trong 2-4 giờ
3. Kiểm tra và xác nhận
4. Hỗ trợ đổi account nếu có vấn đề

**Lưu ý quan trọng:**
- Không thay đổi thông tin cá nhân trong 7 ngày đầu
- Sử dụng proxy để tránh bị khóa
- Liên hệ support nếu có vấn đề

[Xem hướng dẫn sử dụng](https://example.com/guide)`;

        const description2 = `# Khóa học AI Video Formula

Học cách tạo video AI viral trên TikTok với **công nghệ mới nhất** và *chiến lược đã được kiểm chứng*.

## Nội dung khóa học:

### Module 1: Cơ bản về AI Video
- Giới thiệu các công cụ AI tạo video
- Cách chọn niche phù hợp
- Phân tích xu hướng viral

### Module 2: Thực hành tạo video
- Sử dụng ChatGPT để viết script
- Tạo video với Runway ML
- Chỉnh sửa với CapCut Pro

### Module 3: Tối ưu hóa và phát triển
- SEO cho TikTok
- Phân tích metrics
- Scale up kênh

## Bonus materials:
- **100+ templates** video AI
- *Thư viện âm thanh* trending
- **Checklist** viral content
- *Group hỗ trợ* 24/7

## Cam kết:
1. Hoàn tiền 100% nếu không hài lòng
2. Cập nhật nội dung miễn phí
3. Hỗ trợ 1-1 trong 3 tháng

**Giá trị khóa học:** 5,000,000 VND
**Giá ưu đãi:** 1,200,000 MP

[Đăng ký ngay](https://example.com/register) | [Xem demo](https://example.com/demo)`;

        // Populate descriptions
        document.getElementById('demo1').innerHTML = formatDescription(description1);
        document.getElementById('demo2').innerHTML = formatDescription(description2);
    </script>
</body>
</html>
