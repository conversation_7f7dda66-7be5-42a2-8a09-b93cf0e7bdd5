<!DOCTYPE html>
<html>
<head>
    <title>Debug Warranty Buttons</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
        button { margin: 5px; padding: 8px 15px; }
    </style>
</head>
<body>
    <h1>🔍 Debug Warranty Buttons</h1>
    
    <div class="debug-section">
        <h3>1. Check Current Page</h3>
        <p>Current URL: <span id="currentUrl"></span></p>
        <p>Page Type: <span id="pageType"></span></p>
    </div>
    
    <div class="debug-section">
        <h3>2. Check JavaScript Libraries</h3>
        <div id="jsLibraries"></div>
    </div>
    
    <div class="debug-section">
        <h3>3. Check Warranty Functions</h3>
        <div id="warrantyFunctions"></div>
    </div>
    
    <div class="debug-section">
        <h3>4. Check DOM Elements</h3>
        <div id="domElements"></div>
    </div>
    
    <div class="debug-section">
        <h3>5. Check Console Errors</h3>
        <div id="consoleErrors"></div>
    </div>
    
    <div class="debug-section">
        <h3>6. Test API Endpoints</h3>
        <button onclick="testWarrantyAPIs()">Test APIs</button>
        <div id="apiResults"></div>
    </div>
    
    <div class="debug-section">
        <h3>7. Force Reload Warranty Data</h3>
        <button onclick="forceReloadWarranty()">Force Reload</button>
        <div id="reloadResults"></div>
    </div>

    <script>
        // Debug script
        function debugWarrantyButtons() {
            console.log('🔍 Starting warranty buttons debug...');
            
            // 1. Check current page
            document.getElementById('currentUrl').textContent = window.location.href;
            const isAdmin = window.location.href.includes('/admin/marketplace/warranty');
            const isUser = window.location.href.includes('/marketplace/warranty');
            document.getElementById('pageType').textContent = isAdmin ? 'Admin' : (isUser ? 'User' : 'Unknown');
            
            // 2. Check JavaScript libraries
            const jsLibs = [];
            if (typeof jQuery !== 'undefined') jsLibs.push('✅ jQuery');
            else jsLibs.push('❌ jQuery missing');
            
            if (typeof coreui !== 'undefined') jsLibs.push('✅ CoreUI');
            else jsLibs.push('❌ CoreUI missing');
            
            if (typeof bootstrap !== 'undefined') jsLibs.push('✅ Bootstrap');
            else jsLibs.push('❌ Bootstrap missing');
            
            document.getElementById('jsLibraries').innerHTML = jsLibs.join('<br>');
            
            // 3. Check warranty functions
            const warrantyFuncs = [];
            
            if (isAdmin) {
                if (typeof getActionButtons !== 'undefined') warrantyFuncs.push('✅ getActionButtons');
                else warrantyFuncs.push('❌ getActionButtons missing');
                
                if (typeof approveWarranty !== 'undefined') warrantyFuncs.push('✅ approveWarranty');
                else warrantyFuncs.push('❌ approveWarranty missing');
                
                if (typeof rejectWarranty !== 'undefined') warrantyFuncs.push('✅ rejectWarranty');
                else warrantyFuncs.push('❌ rejectWarranty missing');
                
                if (typeof loadWarrantyRequests !== 'undefined') warrantyFuncs.push('✅ loadWarrantyRequests');
                else warrantyFuncs.push('❌ loadWarrantyRequests missing');
            }
            
            if (isUser) {
                if (typeof cancelWarrantyRequest !== 'undefined') warrantyFuncs.push('✅ cancelWarrantyRequest');
                else warrantyFuncs.push('❌ cancelWarrantyRequest missing');
                
                if (typeof loadWarrantyRequests !== 'undefined') warrantyFuncs.push('✅ loadWarrantyRequests');
                else warrantyFuncs.push('❌ loadWarrantyRequests missing');
            }
            
            document.getElementById('warrantyFunctions').innerHTML = warrantyFuncs.join('<br>');
            
            // 4. Check DOM elements
            const domElements = [];
            
            const warrantyTable = document.querySelector('#warrantyRequestsTable, table');
            if (warrantyTable) {
                domElements.push('✅ Warranty table found');
                
                const buttons = warrantyTable.querySelectorAll('button');
                domElements.push(`📊 Found ${buttons.length} buttons in table`);
                
                const approveButtons = warrantyTable.querySelectorAll('button[onclick*="approve"]');
                const rejectButtons = warrantyTable.querySelectorAll('button[onclick*="reject"]');
                const cancelButtons = warrantyTable.querySelectorAll('button[onclick*="cancel"]');
                
                domElements.push(`📊 Approve buttons: ${approveButtons.length}`);
                domElements.push(`📊 Reject buttons: ${rejectButtons.length}`);
                domElements.push(`📊 Cancel buttons: ${cancelButtons.length}`);
                
                // Show button details
                buttons.forEach((btn, index) => {
                    if (index < 5) { // Show first 5 buttons
                        const onclick = btn.getAttribute('onclick') || 'no onclick';
                        const text = btn.textContent.trim();
                        domElements.push(`🔘 Button ${index + 1}: "${text}" - ${onclick}`);
                    }
                });
                
            } else {
                domElements.push('❌ Warranty table not found');
            }
            
            document.getElementById('domElements').innerHTML = domElements.join('<br>');
            
            // 5. Check console errors (simplified)
            const consoleDiv = document.getElementById('consoleErrors');
            consoleDiv.innerHTML = '📝 Check browser console (F12) for JavaScript errors';
        }
        
        function testWarrantyAPIs() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '🔄 Testing APIs...';
            
            const isAdmin = window.location.href.includes('/admin/marketplace/warranty');
            const apis = [];
            
            if (isAdmin) {
                apis.push('/api/admin/warranty/requests');
                apis.push('/api/admin/warranty/replacement-accounts');
            } else {
                apis.push('/api/marketplace/user/warranty/requests');
                apis.push('/api/marketplace/user/warranty/count');
            }
            
            const results = [];
            let completed = 0;
            
            apis.forEach(api => {
                fetch(api)
                    .then(response => {
                        results.push(`✅ ${api}: ${response.status} ${response.statusText}`);
                        return response.json();
                    })
                    .then(data => {
                        results.push(`📊 ${api}: ${JSON.stringify(data).substring(0, 100)}...`);
                    })
                    .catch(error => {
                        results.push(`❌ ${api}: ${error.message}`);
                    })
                    .finally(() => {
                        completed++;
                        if (completed === apis.length) {
                            resultsDiv.innerHTML = results.join('<br>');
                        }
                    });
            });
        }
        
        function forceReloadWarranty() {
            const resultsDiv = document.getElementById('reloadResults');
            resultsDiv.innerHTML = '🔄 Force reloading...';
            
            try {
                // Clear any cached data
                if (typeof localStorage !== 'undefined') {
                    localStorage.removeItem('warrantyData');
                    localStorage.removeItem('warrantyRequests');
                }
                
                // Try to call loadWarrantyRequests if it exists
                if (typeof loadWarrantyRequests !== 'undefined') {
                    loadWarrantyRequests();
                    resultsDiv.innerHTML = '✅ Called loadWarrantyRequests()';
                } else {
                    resultsDiv.innerHTML = '❌ loadWarrantyRequests function not found';
                }
                
                // Force page reload after 2 seconds
                setTimeout(() => {
                    resultsDiv.innerHTML += '<br>🔄 Reloading page...';
                    window.location.reload(true);
                }, 2000);
                
            } catch (error) {
                resultsDiv.innerHTML = `❌ Error: ${error.message}`;
            }
        }
        
        // Auto-run debug when page loads
        window.addEventListener('load', function() {
            setTimeout(debugWarrantyButtons, 1000);
        });
        
        // Also run immediately
        debugWarrantyButtons();
    </script>
</body>
</html>
