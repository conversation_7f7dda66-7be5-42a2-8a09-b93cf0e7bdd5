#!/usr/bin/env python3
"""
Check warranty buttons and functionality on local
"""

import sys
import os
import re

def check_warranty_buttons_local():
    """Check warranty buttons and functionality on local"""
    
    print("🚀 Checking warranty buttons on local...")
    print("=" * 70)
    
    try:
        # Check admin warranty template
        print("\n🔧 Step 1: Checking admin warranty template...")
        
        admin_file = "templates/admin/warranty_management.html"
        if os.path.exists(admin_file):
            with open(admin_file, 'r', encoding='utf-8') as f:
                admin_content = f.read()
            
            print(f"✅ Found admin file: {admin_file}")
            
            # Find action buttons in admin
            admin_buttons = []
            
            # Look for approve/reject buttons
            approve_matches = re.findall(r'(btn[^>]*onclick[^>]*approve[^>]*>.*?</button>)', admin_content, re.IGNORECASE | re.DOTALL)
            reject_matches = re.findall(r'(btn[^>]*onclick[^>]*reject[^>]*>.*?</button>)', admin_content, re.IGNORECASE | re.DOTALL)
            
            print(f"📋 Admin approve buttons found: {len(approve_matches)}")
            for i, btn in enumerate(approve_matches[:3]):  # Show first 3
                clean_btn = re.sub(r'\s+', ' ', btn.strip())[:100]
                print(f"  {i+1}. {clean_btn}...")
            
            print(f"📋 Admin reject buttons found: {len(reject_matches)}")
            for i, btn in enumerate(reject_matches[:3]):  # Show first 3
                clean_btn = re.sub(r'\s+', ' ', btn.strip())[:100]
                print(f"  {i+1}. {clean_btn}...")
            
            # Look for getActionButtons function
            action_func_match = re.search(r'function getActionButtons\([^}]+\}[^}]*\}', admin_content, re.DOTALL)
            if action_func_match:
                print(f"✅ Found getActionButtons function")
                func_content = action_func_match.group(0)
                # Extract button creation logic
                button_lines = [line.strip() for line in func_content.split('\n') if 'btn' in line and ('approve' in line.lower() or 'reject' in line.lower() or 'check' in line.lower() or 'cil-x' in line.lower())]
                print(f"📋 Button creation lines:")
                for line in button_lines:
                    print(f"  - {line}")
            else:
                print(f"❌ getActionButtons function not found")
                
        else:
            print(f"❌ Admin file not found: {admin_file}")
        
        # Check user warranty template
        print(f"\n🔧 Step 2: Checking user warranty template...")
        
        user_file = "templates/marketplace/warranty.html"
        if os.path.exists(user_file):
            with open(user_file, 'r', encoding='utf-8') as f:
                user_content = f.read()
            
            print(f"✅ Found user file: {user_file}")
            
            # Look for cancel buttons
            cancel_matches = re.findall(r'(btn[^>]*onclick[^>]*cancel[^>]*>.*?</button>)', user_content, re.IGNORECASE | re.DOTALL)
            huy_matches = re.findall(r'(btn[^>]*onclick[^>]*hủy[^>]*>.*?</button>)', user_content, re.IGNORECASE | re.DOTALL)
            
            print(f"📋 User cancel buttons found: {len(cancel_matches)}")
            for i, btn in enumerate(cancel_matches[:3]):
                clean_btn = re.sub(r'\s+', ' ', btn.strip())[:100]
                print(f"  {i+1}. {clean_btn}...")
            
            print(f"📋 User hủy buttons found: {len(huy_matches)}")
            for i, btn in enumerate(huy_matches[:3]):
                clean_btn = re.sub(r'\s+', ' ', btn.strip())[:100]
                print(f"  {i+1}. {clean_btn}...")
            
            # Look for action buttons creation
            action_creation = re.search(r'let actionButtons = `[^`]+`', user_content, re.DOTALL)
            if action_creation:
                print(f"✅ Found actionButtons creation")
                print(f"📋 Action buttons template:")
                action_content = action_creation.group(0)
                print(f"  {action_content}")
            else:
                print(f"❌ actionButtons creation not found")
                
        else:
            print(f"❌ User file not found: {user_file}")
        
        # Check if there are any other warranty-related templates
        print(f"\n🔧 Step 3: Checking other warranty templates...")
        
        other_files = [
            "templates/warranty_requests.html",
            "templates/warranty_requests_coreui.html"
        ]
        
        for file_path in other_files:
            if os.path.exists(file_path):
                print(f"✅ Found: {file_path}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for buttons
                button_count = len(re.findall(r'<button[^>]*>', content))
                print(f"  📋 Contains {button_count} buttons")
            else:
                print(f"❌ Not found: {file_path}")
        
        # Check routes in mip_system.py
        print(f"\n🔧 Step 4: Checking warranty routes...")
        
        if os.path.exists("mip_system.py"):
            with open("mip_system.py", 'r', encoding='utf-8') as f:
                routes_content = f.read()
            
            # Find warranty routes
            warranty_routes = re.findall(r'@app\.route\([^)]*warranty[^)]*\)', routes_content, re.IGNORECASE)
            print(f"📋 Found {len(warranty_routes)} warranty routes:")
            for route in warranty_routes:
                print(f"  - {route}")
            
            # Check for specific admin and user warranty routes
            admin_warranty_route = '/admin/marketplace/warranty' in routes_content
            user_warranty_route = '/marketplace/warranty' in routes_content
            
            print(f"📋 Admin warranty route (/admin/marketplace/warranty): {'✅ Found' if admin_warranty_route else '❌ Not found'}")
            print(f"📋 User warranty route (/marketplace/warranty): {'✅ Found' if user_warranty_route else '❌ Not found'}")
        
        # Summary
        print(f"\n🔧 Step 5: Summary...")
        print(f"📊 Files to check on server:")
        print(f"  1. templates/admin/warranty_management.html")
        print(f"  2. templates/marketplace/warranty.html")
        print(f"  3. Check if warranty routes are working")
        print(f"  4. Check browser console for JavaScript errors")
        
        return True
        
    except Exception as e:
        print(f"❌ Check failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = check_warranty_buttons_local()
    sys.exit(0 if success else 1)
