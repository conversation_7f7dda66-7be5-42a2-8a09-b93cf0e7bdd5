#!/usr/bin/env python3
"""
Check Users table structure to identify password column issues
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def check_users_structure():
    """Check Users table structure"""
    
    print("🚀 Checking Users table structure...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check all columns
        print("\n🔧 Step 1: Checking all Users table columns...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        columns = cursor.fetchall()
        print(f"📋 Users table columns ({len(columns)} total):")
        for col in columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 2: Check password-related columns specifically
        print("\n🔧 Step 2: Password-related columns...")
        
        password_columns = [col for col in columns if 'password' in col[0].lower()]
        if password_columns:
            for col in password_columns:
                nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
                print(f"🔑 {col[0]}: {col[1]} {nullable}")
        else:
            print("❌ No password columns found!")
        
        # Step 3: Check constraints
        print("\n🔧 Step 3: Checking constraints...")
        
        cursor.execute('''
            SELECT constraint_name, constraint_type
            FROM information_schema.table_constraints 
            WHERE table_name = 'Users' AND table_schema = 'public';
        ''')
        
        constraints = cursor.fetchall()
        print("📋 Table constraints:")
        for constraint in constraints:
            print(f"  - {constraint[0]}: {constraint[1]}")
        
        # Step 4: Sample data check
        print("\n🔧 Step 4: Sample data check...")
        
        cursor.execute('''
            SELECT user_id, username, 
                   CASE WHEN password IS NULL THEN 'NULL' ELSE 'HAS_VALUE' END as password_status,
                   CASE WHEN password_hash IS NULL THEN 'NULL' ELSE 'HAS_VALUE' END as password_hash_status
            FROM "Users" 
            LIMIT 5;
        ''')
        
        sample_users = cursor.fetchall()
        print("📊 Sample users:")
        for user in sample_users:
            print(f"  - ID: {user[0]}, Username: {user[1]}, password: {user[2]}, password_hash: {user[3]}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Check failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = check_users_structure()
    sys.exit(0 if success else 1)
