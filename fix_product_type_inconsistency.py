#!/usr/bin/env python3
"""
Fix product type inconsistency in database
"""

import psycopg2
import psycopg2.extras
from db_config import PG_CONFIG

def get_db_connection():
    """Kết nối database PostgreSQL từ config"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def fix_product_type_inconsistency():
    """Fix product type inconsistency"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🔧 Fixing Product Type Inconsistency")
        print("=" * 50)
        
        # Type mapping logic (same as in API)
        type_mapping = {
            'Account': 'account',
            'Sản Phẩm Win': 'win_product',
            'Win Product': 'win_product',
            'Course Basic': 'course',
            'Course Advanced': 'course',
            'TikTok Formula': 'win_product',
            'AI Video Formula': 'videos',
            'AFF Package': 'aff_package',
            'TikTok Playbook': 'win_product',
            'Digital Product': 'win_product',
            'E-book': 'win_product',
            'Template': 'win_product',
            'Tool': 'win_product',
            'Video': 'videos',
            'videos': 'videos',
            'Videos': 'videos'
        }
        
        # Get all products with their type info
        cursor.execute('''
            SELECT p.product_id, p.name, p.product_type, p.product_type_id, pt.name as type_name
            FROM "Products" p
            LEFT JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
            WHERE p.is_deleted = false OR p.is_deleted IS NULL
        ''')
        
        products = cursor.fetchall()
        fixed_count = 0
        
        print(f"📋 Checking {len(products)} products...")
        
        for product in products:
            product_id = product['product_id']
            current_type = product['product_type']
            type_name = product['type_name']
            
            if not type_name:
                print(f"⚠️ Product {product_id} has no type_name, skipping")
                continue
            
            # Calculate expected type
            expected_type = type_mapping.get(type_name)
            if not expected_type:
                if type_name.lower() in ['account', 'tài khoản']:
                    expected_type = 'account'
                elif type_name.lower() in ['video', 'videos']:
                    expected_type = 'videos'
                else:
                    expected_type = 'win_product'
            
            # Fix if inconsistent
            if current_type != expected_type:
                print(f"🔧 Fixing Product {product_id}: '{product['name']}'")
                print(f"   Type Name: {type_name}")
                print(f"   Current: {current_type} → Expected: {expected_type}")
                
                cursor.execute('''
                    UPDATE "Products"
                    SET product_type = %s
                    WHERE product_id = %s
                ''', (expected_type, product_id))
                
                fixed_count += 1
        
        if fixed_count > 0:
            conn.commit()
            print(f"\n✅ Fixed {fixed_count} product type inconsistencies")
        else:
            print(f"\n✅ No inconsistencies found")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing inconsistency: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        return False

def verify_fix():
    """Verify the fix worked"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n🔍 Verifying Fix")
        print("=" * 50)
        
        # Type mapping logic (same as in API)
        type_mapping = {
            'Account': 'account',
            'Sản Phẩm Win': 'win_product',
            'Win Product': 'win_product',
            'Course Basic': 'course',
            'Course Advanced': 'course',
            'TikTok Formula': 'win_product',
            'AI Video Formula': 'videos',
            'AFF Package': 'aff_package',
            'TikTok Playbook': 'win_product',
            'Digital Product': 'win_product',
            'E-book': 'win_product',
            'Template': 'win_product',
            'Tool': 'win_product',
            'Video': 'videos',
            'videos': 'videos',
            'Videos': 'videos'
        }
        
        # Check for remaining inconsistencies
        cursor.execute('''
            SELECT p.product_id, p.name, p.product_type, pt.name as type_name
            FROM "Products" p
            JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
            WHERE p.is_deleted = false OR p.is_deleted IS NULL
        ''')
        
        inconsistencies = []
        for product in cursor.fetchall():
            product_type = product['product_type']
            type_name = product['type_name']
            
            # Check if mapping is correct
            expected_type = type_mapping.get(type_name)
            if not expected_type:
                if type_name.lower() in ['account', 'tài khoản']:
                    expected_type = 'account'
                elif type_name.lower() in ['video', 'videos']:
                    expected_type = 'videos'
                else:
                    expected_type = 'win_product'
            
            if product_type != expected_type:
                inconsistencies.append({
                    'product_id': product['product_id'],
                    'name': product['name'],
                    'current_type': product_type,
                    'type_name': type_name,
                    'expected_type': expected_type
                })
        
        if inconsistencies:
            print(f"❌ Still have {len(inconsistencies)} inconsistencies:")
            for inc in inconsistencies:
                print(f"   Product {inc['product_id']}: '{inc['name']}'")
                print(f"      Type Name: {inc['type_name']}")
                print(f"      Current: {inc['current_type']} → Expected: {inc['expected_type']}")
            return False
        else:
            print("✅ All inconsistencies fixed!")
            
            # Show current state
            cursor.execute('''
                SELECT p.product_type, pt.name as type_name, COUNT(*) as count
                FROM "Products" p
                JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
                WHERE p.is_deleted = false OR p.is_deleted IS NULL
                GROUP BY p.product_type, pt.name
                ORDER BY p.product_type, pt.name
            ''')
            
            print(f"\n📊 Current Product Distribution:")
            print(f"{'Product Type':<15} {'Type Name':<20} {'Count'}")
            print("-" * 50)
            
            for row in cursor.fetchall():
                print(f"{row['product_type']:<15} {row['type_name']:<20} {row['count']}")
            
            return True
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error verifying fix: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Product Type Inconsistency Fix Tool")
    print("=" * 60)
    
    # Fix inconsistencies
    fix_success = fix_product_type_inconsistency()
    
    # Verify fix
    verify_success = verify_fix()
    
    if fix_success and verify_success:
        print("\n🎉 Product type inconsistency fix completed successfully!")
        print("\n📋 What was fixed:")
        print("  ✅ Updated product_type to match ProductTypes.name")
        print("  ✅ Applied correct type mapping logic")
        print("  ✅ Ensured consistency between frontend and backend")
        print("\n🎯 This should resolve:")
        print("  - Product type jumping when saving")
        print("  - Incorrect type selection in forms")
        print("  - Mismatch between displayed and saved types")
    else:
        print("\n❌ Fix failed! Please check the errors above.")

if __name__ == "__main__":
    main()
