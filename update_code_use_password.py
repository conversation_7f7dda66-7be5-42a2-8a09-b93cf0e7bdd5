#!/usr/bin/env python3
"""
Update code to use 'password' column instead of 'password_hash'
<PERSON><PERSON> thống nh<PERSON>t với database sau khi migration
"""

import re
import os
import shutil
from datetime import datetime

def backup_file(file_path):
    """Backup file tr<PERSON><PERSON><PERSON> khi sửa"""
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"📁 Backup created: {backup_path}")
    return backup_path

def update_mip_system_file():
    """Update file mip_system.py"""
    
    file_path = "mip_system.py"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    print(f"🔧 Updating {file_path}...")
    
    # Backup file
    backup_path = backup_file(file_path)
    
    try:
        # Read file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Track changes
        changes_made = 0
        
        # 1. Fix route /register - INSERT statement
        old_pattern = r'INSERT INTO "Users" \(username, password_hash, role,'
        new_replacement = 'INSERT INTO "Users" (username, password, role,'
        if re.search(old_pattern, content):
            content = re.sub(old_pattern, new_replacement, content)
            changes_made += 1
            print("✅ Fixed /register INSERT statement")
        
        # 2. Fix route /add_user - INSERT statements
        old_pattern = r'INSERT INTO "Users" \(username, password_hash, role, team_id, unit_code'
        new_replacement = 'INSERT INTO "Users" (username, password, role, team_id, unit_code'
        if re.search(old_pattern, content):
            content = re.sub(old_pattern, new_replacement, content)
            changes_made += 1
            print("✅ Fixed /add_user INSERT statement")
        
        # 3. Fix route /add_user - INSERT with phone
        old_pattern = r'INSERT INTO "Users" \(username, password_hash, role, team_id, unit_code, phone\)'
        new_replacement = 'INSERT INTO "Users" (username, password, role, team_id, unit_code, phone)'
        if re.search(old_pattern, content):
            content = re.sub(old_pattern, new_replacement, content)
            changes_made += 1
            print("✅ Fixed /add_user INSERT with phone statement")
        
        # 4. Fix route /edit_user - UPDATE statements
        old_pattern = r'UPDATE "Users" SET username=%s, password_hash=%s, role=%s'
        new_replacement = 'UPDATE "Users" SET username=%s, password=%s, role=%s'
        if re.search(old_pattern, content):
            content = re.sub(old_pattern, new_replacement, content)
            changes_made += 1
            print("✅ Fixed /edit_user UPDATE statement")
        
        # 5. Fix route /api/auth/login - SELECT statement
        old_pattern = r'SELECT user_id, username, password_hash, role, team_id, unit_code'
        new_replacement = 'SELECT user_id, username, password, role, team_id, unit_code'
        if re.search(old_pattern, content):
            content = re.sub(old_pattern, new_replacement, content)
            changes_made += 1
            print("✅ Fixed /api/auth/login SELECT statement")
        
        # 6. Fix any other password_hash references in SQL
        old_pattern = r'password_hash'
        # Only replace in SQL contexts (within quotes or after SET/INSERT)
        sql_contexts = [
            r'INSERT INTO[^(]*\([^)]*password_hash',
            r'UPDATE[^S]*SET[^W]*password_hash',
            r'SELECT[^F]*password_hash'
        ]
        
        for context_pattern in sql_contexts:
            if re.search(context_pattern, content, re.IGNORECASE):
                content = re.sub(r'\bpassword_hash\b', 'password', content)
                changes_made += 1
                print("✅ Fixed additional password_hash references")
                break
        
        # Write updated content
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Updated {file_path} with {changes_made} changes")
            return True
        else:
            print("ℹ️  No changes needed in mip_system.py")
            # Remove backup if no changes
            os.remove(backup_path)
            return True
            
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        # Restore backup
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, file_path)
            print(f"🔄 Restored from backup")
        return False

def verify_changes():
    """Verify changes were applied correctly"""
    
    file_path = "mip_system.py"
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    print(f"\n🔍 Verifying changes in {file_path}...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for remaining password_hash references
        password_hash_matches = re.findall(r'password_hash', content)
        
        if password_hash_matches:
            print(f"⚠️  Found {len(password_hash_matches)} remaining 'password_hash' references")
            
            # Show context for each match
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'password_hash' in line:
                    print(f"   Line {i+1}: {line.strip()}")
            
            print("🔧 These might need manual review")
        else:
            print("✅ No 'password_hash' references found")
        
        # Check for password column usage
        password_matches = re.findall(r'\bpassword\b(?!\s*=\s*request\.)', content)
        print(f"✅ Found {len(password_matches)} 'password' column references")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying changes: {e}")
        return False

def show_summary():
    """Show summary of changes"""
    
    print("\n📋 Summary of Changes:")
    print("=" * 50)
    print("✅ Database migration:")
    print("   - Renamed 'password_hash' column to 'password'")
    print()
    print("✅ Code updates:")
    print("   - Route /register: INSERT statement")
    print("   - Route /add_user: INSERT statements")
    print("   - Route /edit_user: UPDATE statements")
    print("   - Route /api/auth/login: SELECT statement")
    print()
    print("🎯 Result:")
    print("   - Database and code now consistently use 'password' column")
    print("   - Registration should work on both local and server")
    print()
    print("🚀 Next steps:")
    print("   1. Upload updated mip_system.py to server")
    print("   2. Test registration functionality")
    print("   3. Test login functionality")

def main():
    """Main function"""
    print("🔧 Updating code to use 'password' column")
    print("🎯 Goal: Make code consistent with database schema")
    print("=" * 60)
    
    # Update mip_system.py
    success = update_mip_system_file()
    
    if success:
        # Verify changes
        verify_success = verify_changes()
        
        if verify_success:
            print("\n✅ Code update completed successfully!")
            
            # Show summary
            show_summary()
        else:
            print("\n⚠️  Code updated but verification had issues")
    else:
        print("\n❌ Code update failed!")
    
    print("\n" + "=" * 60)
    print("📁 Backup files created for safety")
    print("🔄 You can restore from backup if needed")

if __name__ == "__main__":
    main()
