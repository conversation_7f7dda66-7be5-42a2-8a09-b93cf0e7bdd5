-- Add max_quantity column to Products table
-- This column controls the maximum quantity a customer can purchase

ALTER TABLE "Products" 
ADD COLUMN max_quantity INTEGER DEFAULT NULL;

-- Add comment to explain the column
COMMENT ON COLUMN "Products".max_quantity IS 'Maximum quantity that can be purchased in a single order. NULL means no limit.';

-- Update existing products based on their metadata
-- Files/Win Products should typically have max_quantity = 1
UPDATE "Products" 
SET max_quantity = 1 
WHERE product_type IN ('win_product', 'files') 
   OR (metadata IS NOT NULL AND metadata::text LIKE '%"single_purchase_only"%true%');

-- Account packages can have multiple quantities (default behavior)
-- Videos can have multiple quantities (default behavior)

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_products_max_quantity ON "Products"(max_quantity);

-- Show updated products
SELECT product_id, name, product_type, max_quantity, 
       CASE 
           WHEN max_quantity = 1 THEN 'Single purchase only'
           WHEN max_quantity IS NULL THEN 'No limit'
           ELSE 'Max: ' || max_quantity::text
       END as quantity_limit
FROM "Products" 
WHERE is_deleted = FALSE OR is_deleted IS NULL
ORDER BY created_at DESC
LIMIT 10;
