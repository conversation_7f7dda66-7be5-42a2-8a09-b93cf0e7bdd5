#!/usr/bin/env python3
"""
Test complete registration flow
"""

import requests
import json

def test_registration_flow():
    """Test the complete registration flow"""
    print("🧪 Testing Complete Registration Flow")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    test_email = "<EMAIL>"
    
    # Create session
    session = requests.Session()
    
    # Step 1: Get registration page
    print("📋 1. Getting registration page...")
    try:
        response = session.get(f"{base_url}/register")
        if response.status_code == 200:
            print("✅ Registration page loaded")
        else:
            print(f"❌ Failed to load registration page: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error loading registration page: {e}")
        return False
    
    # Step 2: Submit registration form
    print(f"\n📋 2. Submitting registration for {test_email}...")
    
    registration_data = {
        'username': 'testuser123',
        'email': test_email,
        'phone': '0123456789',
        'password': 'testpass123',
        'confirm_password': 'testpass123'
    }
    
    try:
        response = session.post(f"{base_url}/register", data=registration_data)
        print(f"Registration response status: {response.status_code}")
        print(f"Registration response URL: {response.url}")
        
        if 'verify-email' in response.url:
            print("✅ Redirected to email verification page")
        else:
            print("❌ Not redirected to verification page")
            print(f"Response content: {response.text[:500]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error submitting registration: {e}")
        return False
    
    # Step 3: Test resend OTP API
    print(f"\n📋 3. Testing resend OTP API...")
    
    resend_data = {
        'email': test_email,
        'type': 'registration'
    }
    
    try:
        response = session.post(
            f"{base_url}/api/resend-otp",
            json=resend_data,
            headers={'Content-Type': 'application/json'}
        )
        
        result = response.json()
        print(f"Resend OTP response: {result}")
        
        if result.get('success'):
            print("✅ Resend OTP working")
        else:
            print(f"❌ Resend OTP failed: {result.get('error')}")
            
    except Exception as e:
        print(f"❌ Error testing resend OTP: {e}")
    
    # Step 4: Check database for OTP codes
    print(f"\n📋 4. Checking database for OTP codes...")
    
    try:
        import psycopg2
        import psycopg2.extras
        from db_config import PG_CONFIG
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        cursor.execute('''
            SELECT code_id, email, code, code_type, created_at, expires_at, is_used, attempts
            FROM "EmailVerificationCodes"
            WHERE email = %s
            ORDER BY created_at DESC
            LIMIT 5
        ''', (test_email,))
        
        codes = cursor.fetchall()
        
        if codes:
            print(f"Found {len(codes)} OTP codes for {test_email}:")
            for code in codes:
                status = "USED" if code['is_used'] else "ACTIVE"
                print(f"   Code: {code['code']} | Type: {code['code_type']} | Status: {status} | Attempts: {code['attempts']}")
                print(f"      Created: {code['created_at']} | Expires: {code['expires_at']}")
        else:
            print(f"❌ No OTP codes found for {test_email}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
    
    return True

def test_verify_otp():
    """Test OTP verification"""
    print(f"\n🧪 Testing OTP Verification")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:5000"
    test_email = "<EMAIL>"
    
    # Get latest OTP from database
    try:
        import psycopg2
        import psycopg2.extras
        from db_config import PG_CONFIG
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        cursor.execute('''
            SELECT code
            FROM "EmailVerificationCodes"
            WHERE email = %s AND code_type = 'registration' AND is_used = FALSE
            ORDER BY created_at DESC
            LIMIT 1
        ''', (test_email,))
        
        code_result = cursor.fetchone()
        
        if not code_result:
            print("❌ No active OTP found")
            return False
        
        otp_code = code_result['code']
        print(f"📝 Testing with OTP: {otp_code}")
        
        # Test verification
        session = requests.Session()
        
        verify_data = {
            'email': test_email,
            'code': otp_code,
            'type': 'registration'
        }
        
        response = session.post(
            f"{base_url}/api/verify-otp",
            json=verify_data,
            headers={'Content-Type': 'application/json'}
        )
        
        result = response.json()
        print(f"Verification result: {result}")
        
        if result.get('success'):
            print("✅ OTP verification working")
            return True
        else:
            print(f"❌ OTP verification failed: {result.get('error')}")
            return False
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error testing OTP verification: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Registration Flow Test Suite")
    print("=" * 60)
    
    # Test registration flow
    flow_success = test_registration_flow()
    
    if flow_success:
        print("\n✅ Registration flow test completed")
        
        # Test OTP verification
        verify_success = test_verify_otp()
        
        if verify_success:
            print("\n🎉 Complete registration flow working!")
        else:
            print("\n⚠️ Registration flow works but OTP verification has issues")
    else:
        print("\n❌ Registration flow test failed")
    
    print("\n💡 Next steps:")
    print("  1. Check Flask app logs for detailed debug info")
    print("  2. Test manually via web interface")
    print("  3. Check email inbox for OTP codes")

if __name__ == "__main__":
    main()
