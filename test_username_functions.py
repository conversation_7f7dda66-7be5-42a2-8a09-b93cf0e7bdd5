#!/usr/bin/env python3
"""
Test script để kiểm tra các function xử lý username và unit code mới
"""

import re
import secrets

def clean_username_for_unit_code(username):
    """
    Loại bỏ tất cả ký tự đặc biệt khỏi username, chỉ giữ lại chữ cái và số
    """
    # Chỉ giữ lại chữ cái và số
    cleaned = re.sub(r'[^a-zA-Z0-9]', '', username)
    return cleaned

def normalize_username(username):
    """
    Chuẩn hóa username: chuyển về chữ thường và chỉ cho phép chữ cái, số và dấu gạch dưới
    """
    # Chuyển về chữ thường
    username = username.lower()
    # Chỉ giữ lại chữ cái, số và dấu gạch dưới
    username = re.sub(r'[^a-z0-9_]', '', username)
    return username

def generate_unit_code(username):
    # Làm sạch username cho unit code (loại bỏ tất cả ký tự đặc biệt)
    clean_username = clean_username_for_unit_code(username)
    random_number = secrets.randbelow(10000)
    return f"SAPMMO{clean_username}{random_number:04d}"

def validate_user_input(data, input_type):
    if input_type == 'username':
        # Username chỉ cho phép chữ cái, số và dấu gạch dưới, độ dài 3-50 ký tự
        return isinstance(data, str) and 3 <= len(data) <= 50 and bool(re.match(r'^[a-zA-Z0-9_]+$', data))
    elif input_type == 'password':
        return isinstance(data, str) and len(data) >= 6
    elif input_type == 'email':
        pattern = r'^[\w\.-]+@[\w\.-]+\.\w+$'
        return isinstance(data, str) and bool(re.match(pattern, data))
    return True

def test_functions():
    """Test các function với các trường hợp khác nhau"""
    
    print("=== TEST CLEAN USERNAME FOR UNIT CODE ===")
    test_cases_clean = [
        "user.name",
        "user-name", 
        "user@domain",
        "user_name123",
        "User.Name-123@test",
        "special!@#$%chars",
        "123abc"
    ]
    
    for username in test_cases_clean:
        cleaned = clean_username_for_unit_code(username)
        print(f"'{username}' -> '{cleaned}'")
    
    print("\n=== TEST NORMALIZE USERNAME ===")
    test_cases_normalize = [
        "UserName",
        "user.name",
        "user-name",
        "User_Name123",
        "UPPERCASE",
        "Mixed.Case-Name_123",
        "special!@#$%chars"
    ]
    
    for username in test_cases_normalize:
        normalized = normalize_username(username)
        print(f"'{username}' -> '{normalized}'")
    
    print("\n=== TEST GENERATE UNIT CODE ===")
    test_cases_unit = [
        "user.name",
        "user-name",
        "User_Name123",
        "special!@#$%chars",
        "test"
    ]
    
    for username in test_cases_unit:
        unit_code = generate_unit_code(username)
        print(f"'{username}' -> '{unit_code}'")
    
    print("\n=== TEST VALIDATE USERNAME ===")
    test_cases_validate = [
        "validuser",
        "valid_user123",
        "ab",  # quá ngắn
        "user.name",  # có dấu chấm
        "user-name",  # có dấu gạch ngang
        "UPPERCASE",
        "lowercase",
        "user_123",
        "123user",
        "a" * 51,  # quá dài
        ""
    ]
    
    for username in test_cases_validate:
        is_valid = validate_user_input(username, 'username')
        print(f"'{username}' -> Valid: {is_valid}")
    
    print("\n=== TEST COMPLETE WORKFLOW ===")
    print("Mô phỏng quy trình đăng ký:")
    
    input_usernames = [
        "User.Name",
        "test-user",
        "ADMIN_USER",
        "<EMAIL>"
    ]
    
    for original in input_usernames:
        print(f"\nInput: '{original}'")
        
        # Bước 1: Chuẩn hóa username
        normalized = normalize_username(original)
        print(f"  Normalized: '{normalized}'")
        
        # Bước 2: Validate
        is_valid = validate_user_input(normalized, 'username')
        print(f"  Valid: {is_valid}")
        
        if is_valid:
            # Bước 3: Tạo unit code
            unit_code = generate_unit_code(normalized)
            print(f"  Unit Code: '{unit_code}'")
        else:
            print(f"  ❌ Username không hợp lệ!")

if __name__ == "__main__":
    test_functions()
