#!/usr/bin/env python3
"""
Create SubscriptionHistory table on server to match local structure
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def create_subscription_history_table():
    """Create SubscriptionHistory table to match local structure"""
    
    print("🚀 Creating SubscriptionHistory table on server...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check if table already exists
        print("\n🔧 Step 1: Checking if SubscriptionHistory table exists...")
        
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'SubscriptionHistory'
            );
        ''')
        
        table_exists = cursor.fetchone()[0]
        
        if table_exists:
            print("✅ SubscriptionHistory table already exists")
            
            # Check structure
            cursor.execute('''
                SELECT column_name, data_type
                FROM information_schema.columns 
                WHERE table_name = 'SubscriptionHistory' AND table_schema = 'public'
                ORDER BY ordinal_position;
            ''')
            existing_cols = cursor.fetchall()
            print(f"📋 Existing columns: {[col[0] for col in existing_cols]}")
            return True
        
        # Step 2: Create sequence
        print("\n🔧 Step 2: Creating sequence...")
        
        cursor.execute('''
            CREATE SEQUENCE IF NOT EXISTS "SubscriptionHistory_history_id_seq"
            INCREMENT 1
            MINVALUE 1
            MAXVALUE 2147483647
            START 1
            CACHE 1;
        ''')
        
        print("  ✅ Created sequence: SubscriptionHistory_history_id_seq")
        
        # Step 3: Create table
        print("\n🔧 Step 3: Creating SubscriptionHistory table...")
        
        cursor.execute('''
            CREATE TABLE "public"."SubscriptionHistory" (
                "history_id" int4 NOT NULL DEFAULT nextval('"SubscriptionHistory_history_id_seq"'::regclass),
                "user_id" int4 NOT NULL,
                "action_type" varchar COLLATE "pg_catalog"."default" NOT NULL,
                "old_package_id" int4,
                "new_package_id" int4,
                "old_subscription_id" int4,
                "new_subscription_id" int4,
                "amount_paid" int4 NOT NULL DEFAULT 0,
                "refund_amount" int4 NOT NULL DEFAULT 0,
                "description" text COLLATE "pg_catalog"."default",
                "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        
        print("  ✅ Created SubscriptionHistory table")
        
        # Step 4: Set table owner
        print("\n🔧 Step 4: Setting table owner...")
        
        cursor.execute('ALTER TABLE "public"."SubscriptionHistory" OWNER TO "sapmmo";')
        print("  ✅ Set table owner to sapmmo")
        
        # Step 5: Create primary key
        print("\n🔧 Step 5: Creating primary key...")
        
        cursor.execute('''
            ALTER TABLE "public"."SubscriptionHistory" 
            ADD CONSTRAINT "SubscriptionHistory_pkey" PRIMARY KEY ("history_id");
        ''')
        
        print("  ✅ Created primary key: history_id")
        
        # Step 6: Create indexes
        print("\n🔧 Step 6: Creating indexes...")
        
        indexes = [
            ('idx_subscription_history_user_id', 'user_id'),
            ('idx_subscription_history_action_type', 'action_type'),
            ('idx_subscription_history_created_at', 'created_at'),
            ('idx_subscription_history_package_ids', 'old_package_id, new_package_id')
        ]
        
        for index_name, columns in indexes:
            try:
                cursor.execute(f'''
                    CREATE INDEX IF NOT EXISTS {index_name} 
                    ON "public"."SubscriptionHistory"({columns});
                ''')
                print(f"  ✅ Created index: {index_name}")
            except Exception as e:
                print(f"  ⚠️  Index {index_name}: {e}")
        
        # Step 7: Add foreign key constraints (if referenced tables exist)
        print("\n🔧 Step 7: Adding foreign key constraints...")
        
        # Check if referenced tables exist
        referenced_tables = ['Users', 'AccountPackages', 'Subscriptions']
        existing_tables = {}
        
        for table in referenced_tables:
            cursor.execute(f'''
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_name = '{table}'
                );
            ''')
            existing_tables[table] = cursor.fetchone()[0]
            print(f"  📋 {table} table exists: {existing_tables[table]}")
        
        # Add foreign keys for existing tables
        foreign_keys = [
            ('fk_subscription_history_user_id', 'user_id', 'Users', 'user_id'),
            ('fk_subscription_history_old_package', 'old_package_id', 'AccountPackages', 'package_id'),
            ('fk_subscription_history_new_package', 'new_package_id', 'AccountPackages', 'package_id'),
            ('fk_subscription_history_old_subscription', 'old_subscription_id', 'Subscriptions', 'subscription_id'),
            ('fk_subscription_history_new_subscription', 'new_subscription_id', 'Subscriptions', 'subscription_id')
        ]
        
        for fk_name, column, ref_table, ref_column in foreign_keys:
            if existing_tables.get(ref_table, False):
                try:
                    cursor.execute(f'''
                        ALTER TABLE "public"."SubscriptionHistory" 
                        ADD CONSTRAINT "{fk_name}" 
                        FOREIGN KEY ("{column}") REFERENCES "public"."{ref_table}" ("{ref_column}") 
                        ON DELETE SET NULL ON UPDATE CASCADE;
                    ''')
                    print(f"  ✅ Added FK: {column} → {ref_table}.{ref_column}")
                except Exception as e:
                    print(f"  ⚠️  FK {fk_name}: {e}")
            else:
                print(f"  ⏭️  Skipped FK {fk_name}: {ref_table} table not found")
        
        # Step 8: Add column comments
        print("\n🔧 Step 8: Adding column comments...")
        
        comments = [
            ('history_id', 'Primary key - auto increment'),
            ('user_id', 'ID của user thực hiện action'),
            ('action_type', 'Loại action: purchase, upgrade, downgrade, cancel, refund'),
            ('old_package_id', 'ID gói cũ (nếu có)'),
            ('new_package_id', 'ID gói mới (nếu có)'),
            ('old_subscription_id', 'ID subscription cũ (nếu có)'),
            ('new_subscription_id', 'ID subscription mới (nếu có)'),
            ('amount_paid', 'Số tiền đã trả (VND)'),
            ('refund_amount', 'Số tiền hoàn lại (VND)'),
            ('description', 'Mô tả chi tiết action'),
            ('created_at', 'Thời gian tạo record')
        ]
        
        for column, comment in comments:
            try:
                cursor.execute(f'''
                    COMMENT ON COLUMN "public"."SubscriptionHistory"."{column}" IS '{comment}';
                ''')
            except Exception as e:
                print(f"  ⚠️  Comment for {column}: {e}")
        
        print("  ✅ Added column comments")
        
        # Step 9: Test table
        print("\n🔧 Step 9: Testing table...")
        
        try:
            # Test INSERT
            cursor.execute('SAVEPOINT test_insert;')
            
            cursor.execute('''
                INSERT INTO "SubscriptionHistory" (user_id, action_type, description)
                VALUES (1, 'test', 'Test record')
                RETURNING history_id;
            ''')
            
            test_id = cursor.fetchone()[0]
            print(f"  ✅ Test INSERT successful, got history_id: {test_id}")
            
            # Test SELECT
            cursor.execute('SELECT COUNT(*) FROM "SubscriptionHistory";')
            count = cursor.fetchone()[0]
            print(f"  ✅ Test SELECT successful, record count: {count}")
            
            # Rollback test
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
            print("  ✅ Test data rolled back")
            
        except Exception as e:
            print(f"  ❌ Table test failed: {e}")
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
        
        # Step 10: Final verification
        print("\n🔧 Step 10: Final verification...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'SubscriptionHistory' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📊 Final SubscriptionHistory structure ({len(final_columns)} columns):")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            print(f"  - {col[0]}: {col[1]} {nullable}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 SubscriptionHistory table created successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test AFF package purchase")
        print("   3. Verify subscription history logging")
        
        return True
        
    except Exception as e:
        print(f"❌ Table creation failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = create_subscription_history_table()
    sys.exit(0 if success else 1)
