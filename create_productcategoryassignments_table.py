#!/usr/bin/env python3
"""
Create ProductCategoryAssignments table for product-category relationships
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def create_productcategoryassignments_table():
    """Create ProductCategoryAssignments table"""
    
    print("🚀 Creating ProductCategoryAssignments table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check if table exists
        print("\n🔧 Step 1: Checking if ProductCategoryAssignments table exists...")
        
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'ProductCategoryAssignments'
            );
        ''')
        
        table_exists = cursor.fetchone()[0]
        print(f"📋 ProductCategoryAssignments table exists: {table_exists}")
        
        if table_exists:
            print("✅ Table already exists, skipping creation")
            return True
        
        # Step 2: Create sequence
        print("\n🔧 Step 2: Creating sequence...")
        
        cursor.execute('''
            CREATE SEQUENCE IF NOT EXISTS "ProductCategoryAssignments_id_seq"
            INCREMENT 1
            MINVALUE 1
            MAXVALUE 2147483647
            START 1
            CACHE 1;
        ''')
        print("  ✅ Created sequence: ProductCategoryAssignments_id_seq")
        
        # Step 3: Create table
        print("\n🔧 Step 3: Creating ProductCategoryAssignments table...")
        
        cursor.execute('''
            CREATE TABLE "ProductCategoryAssignments" (
                "id" int4 NOT NULL DEFAULT nextval('"ProductCategoryAssignments_id_seq"'::regclass),
                "product_id" int4 NOT NULL,
                "category_id" int4 NOT NULL,
                "is_primary" bool DEFAULT false,
                "assigned_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                "assigned_by" int4,
                "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        print("  ✅ Created ProductCategoryAssignments table")
        
        # Step 4: Add comments
        print("\n🔧 Step 4: Adding column comments...")
        
        comments = [
            ("product_id", "ID của sản phẩm"),
            ("category_id", "ID của danh mục"),
            ("is_primary", "Có phải danh mục chính không"),
            ("assigned_at", "Thời gian assign"),
            ("assigned_by", "User thực hiện assign")
        ]
        
        for column, comment in comments:
            cursor.execute(f'''
                COMMENT ON COLUMN "ProductCategoryAssignments"."{column}" IS '{comment}';
            ''')
            print(f"    ✅ Added comment for {column}")
        
        cursor.execute('''
            COMMENT ON TABLE "ProductCategoryAssignments" IS 'Quan hệ nhiều-nhiều giữa Products và ProductCategories';
        ''')
        print("  ✅ Added table comment")
        
        # Step 5: Create indexes
        print("\n🔧 Step 5: Creating indexes...")
        
        indexes = [
            ('idx_productcategoryassignments_product_id', 'product_id'),
            ('idx_productcategoryassignments_category_id', 'category_id'),
            ('idx_productcategoryassignments_primary', 'is_primary'),
            ('idx_productcategoryassignments_product_category', 'product_id, category_id'),
            ('idx_productcategoryassignments_assigned_at', 'assigned_at')
        ]
        
        for index_name, columns in indexes:
            cursor.execute(f'''
                CREATE INDEX "{index_name}" ON "ProductCategoryAssignments" ({columns});
            ''')
            print(f"  ✅ Created index: {index_name}")
        
        # Step 6: Add constraints
        print("\n🔧 Step 6: Adding constraints...")
        
        # Primary key
        cursor.execute('''
            ALTER TABLE "ProductCategoryAssignments" ADD CONSTRAINT "ProductCategoryAssignments_pkey" PRIMARY KEY ("id");
        ''')
        print("  ✅ Added primary key constraint")
        
        # Unique constraint to prevent duplicate assignments
        cursor.execute('''
            ALTER TABLE "ProductCategoryAssignments" ADD CONSTRAINT "ProductCategoryAssignments_unique" 
            UNIQUE ("product_id", "category_id");
        ''')
        print("  ✅ Added unique constraint on (product_id, category_id)")
        
        # Step 7: Add foreign keys (check if referenced tables exist)
        print("\n🔧 Step 7: Adding foreign keys...")
        
        # Check if Products table exists
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Products'
            );
        ''')
        products_exists = cursor.fetchone()[0]
        
        if products_exists:
            cursor.execute('''
                ALTER TABLE "ProductCategoryAssignments" ADD CONSTRAINT "ProductCategoryAssignments_product_id_fkey" 
                FOREIGN KEY ("product_id") REFERENCES "Products" ("product_id") ON DELETE CASCADE ON UPDATE NO ACTION;
            ''')
            print("  ✅ Added foreign key to Products table")
        else:
            print("  ⚠️  Products table not found, skipping foreign key")
        
        # Check if ProductCategories table exists
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'ProductCategories'
            );
        ''')
        categories_exists = cursor.fetchone()[0]
        
        if categories_exists:
            cursor.execute('''
                ALTER TABLE "ProductCategoryAssignments" ADD CONSTRAINT "ProductCategoryAssignments_category_id_fkey" 
                FOREIGN KEY ("category_id") REFERENCES "ProductCategories" ("category_id") ON DELETE CASCADE ON UPDATE NO ACTION;
            ''')
            print("  ✅ Added foreign key to ProductCategories table")
        else:
            print("  ⚠️  ProductCategories table not found, skipping foreign key")
        
        # Check if Users table exists
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Users'
            );
        ''')
        users_exists = cursor.fetchone()[0]
        
        if users_exists:
            cursor.execute('''
                ALTER TABLE "ProductCategoryAssignments" ADD CONSTRAINT "ProductCategoryAssignments_assigned_by_fkey" 
                FOREIGN KEY ("assigned_by") REFERENCES "Users" ("user_id") ON DELETE SET NULL ON UPDATE NO ACTION;
            ''')
            print("  ✅ Added foreign key to Users table")
        else:
            print("  ⚠️  Users table not found, skipping foreign key")
        
        # Step 8: Verify final structure
        print("\n🔧 Step 8: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductCategoryAssignments' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Final ProductCategoryAssignments columns ({len(final_columns)} total):")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 9: Test DELETE query
        print("\n🔧 Step 9: Testing DELETE query...")
        
        try:
            # Test the problematic DELETE query structure
            cursor.execute('''
                DELETE FROM "ProductCategoryAssignments" WHERE product_id = 999999;
            ''')
            print("  ✅ DELETE query test successful")
            
        except Exception as e:
            print(f"  ❌ DELETE query test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 ProductCategoryAssignments table created successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test product deletion functionality")
        print("   3. Test product-category assignments")
        
        return True
        
    except Exception as e:
        print(f"❌ Creation failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = create_productcategoryassignments_table()
    sys.exit(0 if success else 1)
