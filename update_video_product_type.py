#!/usr/bin/env python3
"""
Update Video product type to have requires_video_links metadata
"""

import psycopg2
import psycopg2.extras
import json

def get_db_connection():
    """Kết nối database PostgreSQL local"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def update_video_product_type():
    """Update Video product type metadata"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Check if Video product type exists
        cursor.execute('SELECT type_id, name, metadata FROM "ProductTypes" WHERE name = %s', ('Video',))
        video_type = cursor.fetchone()
        
        if not video_type:
            print("📝 Product type 'Video' không tồn tại, tạo mới...")

            # Create new Video product type
            metadata = {
                'requires_video_links': True,
                'description': 'Sản phẩm video sử dụng Google Drive links',
                'icon': 'cil-video'
            }

            cursor.execute('''
                INSERT INTO "ProductTypes" (name, description, metadata, is_active)
                VALUES (%s, %s, %s, %s)
                RETURNING type_id
            ''', ('Video', 'Sản phẩm video qua Google Drive links', json.dumps(metadata), True))

            type_id = cursor.fetchone()[0]
            conn.commit()

            print(f"✅ Created new Video product type: ID {type_id}")
            print(f"   📝 Metadata: {metadata}")

            cursor.close()
            conn.close()
            return True
        
        print(f"✅ Found Video product type: ID {video_type['type_id']}")
        
        # Get current metadata
        current_metadata = video_type['metadata'] or {}
        print(f"📋 Current metadata: {current_metadata}")
        
        # Update metadata to include requires_video_links
        new_metadata = current_metadata.copy()
        new_metadata['requires_video_links'] = True
        new_metadata['description'] = 'Sản phẩm video sử dụng Google Drive links'
        
        # Update the product type
        cursor.execute('''
            UPDATE "ProductTypes" 
            SET metadata = %s, updated_at = CURRENT_TIMESTAMP
            WHERE type_id = %s
        ''', (json.dumps(new_metadata), video_type['type_id']))
        
        conn.commit()
        
        print(f"✅ Updated Video product type metadata:")
        print(f"   📝 New metadata: {new_metadata}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error updating product type: {e}")
        conn.rollback()
        return False

def verify_update():
    """Verify the update was successful"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        cursor.execute('SELECT name, metadata FROM "ProductTypes" WHERE name = %s', ('Video',))
        video_type = cursor.fetchone()
        
        if video_type:
            metadata = video_type['metadata'] or {}
            requires_video_links = metadata.get('requires_video_links', False)
            
            print(f"\n🔍 Verification:")
            print(f"   Product Type: {video_type['name']}")
            print(f"   requires_video_links: {requires_video_links}")
            print(f"   Full metadata: {metadata}")
            
            if requires_video_links:
                print("✅ Update successful!")
                return True
            else:
                print("❌ Update failed - requires_video_links not set")
                return False
        else:
            print("❌ Video product type not found")
            return False
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error verifying update: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Updating Video Product Type Metadata")
    print("=" * 50)
    
    # Update product type
    if update_video_product_type():
        # Verify update
        verify_update()
        print("\n🎉 Video product type is now ready for video links!")
        print("📋 Next steps:")
        print("  1. Create products with type 'Video'")
        print("  2. Select video links when creating products")
        print("  3. Test purchase flow")
    else:
        print("\n❌ Failed to update product type")

if __name__ == "__main__":
    main()
