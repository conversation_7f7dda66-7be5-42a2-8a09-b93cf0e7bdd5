#!/usr/bin/env python3
"""
Migration script to add revenue_enabled column to Accounts table
"""

import psycopg2
import psycopg2.extras
import sys

def add_revenue_enabled_column():
    """Add revenue_enabled column to Accounts table"""
    
    try:
        # Connect to database
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password=""
        )
        
        cursor = conn.cursor()
        
        print("🚀 Adding revenue_enabled column to Accounts table...")
        
        # Check if column already exists
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' 
            AND column_name = 'revenue_enabled'
        """)
        
        if cursor.fetchone():
            print("✅ Column revenue_enabled already exists in Accounts table")
        else:
            # Add the column
            cursor.execute("""
                ALTER TABLE "Accounts" 
                ADD COLUMN revenue_enabled BOOLEAN DEFAULT FALSE
            """)
            
            print("✅ Added revenue_enabled column to Accounts table")
            
            # Update existing accounts - set revenue_enabled to TRUE for accounts with cookies
            cursor.execute("""
                UPDATE "Accounts" 
                SET revenue_enabled = TRUE 
                WHERE cookie_data IS NOT NULL 
                AND cookie_data != '' 
                AND status IN ('Live', 'Có giỏ')
            """)
            
            updated_count = cursor.rowcount
            print(f"✅ Updated {updated_count} existing accounts with revenue_enabled = TRUE")
        
        # Commit changes
        conn.commit()
        print("✅ Migration completed successfully!")
        
    except psycopg2.Error as e:
        print(f"❌ Database error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    add_revenue_enabled_column()
