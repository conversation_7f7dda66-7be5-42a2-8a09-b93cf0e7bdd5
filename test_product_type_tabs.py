#!/usr/bin/env python3
"""
Test tab "Bối <PERSON>" có xuất hiện trong user orders không
"""

import psycopg2
import psycopg2.extras
import json

def get_db_connection():
    """Kết nối database PostgreSQL local"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def test_product_type_tabs_api():
    """Test API /api/marketplace/user/product-types"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🧪 Testing Product Type Tabs API")
        print("=" * 50)
        
        # Simulate API logic
        cursor.execute('''
            SELECT DISTINCT pt.type_id, pt.name, p.product_type
            FROM "ProductTypes" pt
            INNER JOIN "Products" p ON pt.type_id = p.product_type_id
            WHERE pt.is_active = true AND p.is_active = true
            ORDER BY pt.type_id
        ''')
        
        product_types_data = cursor.fetchall()
        
        print(f"📋 Raw data from database:")
        for row in product_types_data:
            print(f"   ProductType ID: {row['type_id']}, Name: '{row['name']}', product_type: '{row['product_type']}'")
        
        # Build response like API
        product_types = []
        type_names = {}
        seen_types = set()
        
        all_types = []
        for type_id, display_name, product_type_code in product_types_data:
            if product_type_code not in seen_types:
                all_types.append((product_type_code, display_name))
                seen_types.add(product_type_code)
        
        # Add account first if exists
        for product_type_code, display_name in all_types:
            if product_type_code == 'account':
                product_types.insert(0, product_type_code)
                type_names[product_type_code] = display_name
                break
        
        # Add other types
        for product_type_code, display_name in all_types:
            if product_type_code != 'account':
                product_types.append(product_type_code)
                type_names[product_type_code] = display_name
        
        print(f"\n📋 API Response simulation:")
        print(f"   product_types: {product_types}")
        print(f"   type_names: {json.dumps(type_names, ensure_ascii=False, indent=2)}")
        
        # Check if "Bối Cảnh Live" tab will appear
        boi_canh_live_found = "Bối Cảnh Live" in product_types
        
        if boi_canh_live_found:
            print(f"\n✅ SUCCESS - Tab 'Bối Cảnh Live' sẽ xuất hiện!")
            print(f"   Tab ID: 'bối-cảnh-live'")
            print(f"   Display name: '{type_names.get('Bối Cảnh Live', 'Bối Cảnh Live')}'")
        else:
            print(f"\n❌ FAIL - Tab 'Bối Cảnh Live' không xuất hiện")
            print(f"   Có thể do:")
            print(f"   - Sản phẩm 'Background WIN' chưa được cập nhật product_type")
            print(f"   - ProductType 'Bối Cảnh Live' không active")
            print(f"   - Sản phẩm không active")
        
        cursor.close()
        conn.close()
        
        return boi_canh_live_found
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_background_win_product_status():
    """Test trạng thái sản phẩm Background WIN"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n🧪 Testing Background WIN Product Status")
        print("=" * 50)
        
        # Check Background WIN product
        cursor.execute('''
            SELECT p.product_id, p.name, p.product_type, p.is_active,
                   pt.name as type_name, pt.is_active as type_active
            FROM "Products" p
            LEFT JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
            WHERE p.name = 'Background WIN'
        ''')
        
        product = cursor.fetchone()
        
        if not product:
            print("❌ Không tìm thấy sản phẩm 'Background WIN'")
            return False
        
        print(f"📋 Product details:")
        print(f"   Product ID: {product['product_id']}")
        print(f"   Name: {product['name']}")
        print(f"   product_type: '{product['product_type']}'")
        print(f"   Product active: {product['is_active']}")
        print(f"   ProductType name: '{product['type_name']}'")
        print(f"   ProductType active: {product['type_active']}")
        
        # Check if it meets API criteria
        meets_criteria = (
            product['is_active'] and 
            product['type_active'] and 
            product['product_type'] == 'Bối Cảnh Live'
        )
        
        if meets_criteria:
            print(f"\n✅ SUCCESS - Sản phẩm đáp ứng điều kiện cho tab")
        else:
            print(f"\n❌ FAIL - Sản phẩm không đáp ứng điều kiện:")
            if not product['is_active']:
                print(f"   - Product không active")
            if not product['type_active']:
                print(f"   - ProductType không active")
            if product['product_type'] != 'Bối Cảnh Live':
                print(f"   - product_type không đúng: '{product['product_type']}' != 'Bối Cảnh Live'")
        
        cursor.close()
        conn.close()
        
        return meets_criteria
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def test_user_order_simulation():
    """Simulate user có đơn hàng với sản phẩm Background WIN"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n🧪 Testing User Order Simulation")
        print("=" * 50)
        
        # Check if there are any completed orders with Background WIN
        cursor.execute('''
            SELECT o.order_id, o.order_number, o.status, p.name, p.product_type
            FROM "Orders" o
            JOIN "OrderItems" oi ON o.order_id = oi.order_id
            JOIN "Products" p ON oi.product_id = p.product_id
            WHERE p.name = 'Background WIN' AND o.status = 'completed'
            LIMIT 5
        ''')
        
        orders = cursor.fetchall()
        
        if orders:
            print(f"✅ Found {len(orders)} completed orders with Background WIN:")
            for order in orders:
                print(f"   Order #{order['order_number']}: {order['status']} - {order['name']} ({order['product_type']})")
        else:
            print(f"⚠️ No completed orders found with Background WIN")
            print(f"   Tab sẽ không xuất hiện vì không có đơn hàng completed")
        
        # Check counts for "Bối Cảnh Live" product type
        cursor.execute('''
            SELECT COUNT(DISTINCT o.order_id) as count
            FROM "Orders" o
            JOIN "OrderItems" oi ON o.order_id = oi.order_id
            JOIN "Products" p ON oi.product_id = p.product_id
            WHERE o.status = 'completed' AND p.product_type = 'Bối Cảnh Live'
        ''')
        
        count_result = cursor.fetchone()
        count = count_result['count'] if count_result else 0
        
        print(f"\n📊 Count for 'Bối Cảnh Live' product type: {count}")
        
        if count > 0:
            print(f"✅ Tab 'Bối Cảnh Live' sẽ hiển thị với count ({count})")
        else:
            print(f"❌ Tab 'Bối Cảnh Live' sẽ hiển thị với count (0)")
        
        cursor.close()
        conn.close()
        
        return len(orders) > 0
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def main():
    """Main function"""
    print("🧪 Testing Product Type Tabs for 'Bối Cảnh Live'")
    print("=" * 60)
    
    # Test 1: Product status
    product_status = test_background_win_product_status()
    
    # Test 2: API response
    api_response = test_product_type_tabs_api()
    
    # Test 3: User orders
    user_orders = test_user_order_simulation()
    
    print(f"\n✅ Test Summary:")
    print(f"   📦 Product Status: {'✅ PASS' if product_status else '❌ FAIL'}")
    print(f"   🔌 API Response: {'✅ PASS' if api_response else '❌ FAIL'}")
    print(f"   👤 User Orders: {'✅ PASS' if user_orders else '⚠️ NO ORDERS'}")
    
    if product_status and api_response:
        print(f"\n🎉 Tab 'Bối Cảnh Live' sẽ xuất hiện trong user orders!")
        print(f"\n🎯 Expected behavior:")
        print(f"   1. Vào /marketplace/orders")
        print(f"   2. Thấy tab 'Bối Cảnh Live' trong danh sách tabs")
        print(f"   3. Click tab → hiển thị đơn hàng có sản phẩm Background WIN")
        print(f"   4. Tab ID: 'bối-cảnh-live'")
        
        if not user_orders:
            print(f"\n⚠️ Lưu ý: Cần có đơn hàng completed với sản phẩm Background WIN để tab hiển thị count > 0")
    else:
        print(f"\n❌ Tab 'Bối Cảnh Live' chưa thể xuất hiện. Cần sửa:")
        if not product_status:
            print(f"   - Kiểm tra trạng thái sản phẩm và ProductType")
        if not api_response:
            print(f"   - Kiểm tra logic API product-types")

if __name__ == "__main__":
    main()
