#!/usr/bin/env python3
"""
Rollback: Max Quantity & Dynamic Product Types Migration
Date: 2025-09-04
Description: Rollback changes made by migrate_max_quantity_and_dynamic_types.py
"""

import psycopg2
import psycopg2.extras
import sys
import traceback
from datetime import datetime

# Import database config
try:
    from db_config import PG_CONFIG
    print("✅ Imported database config from db_config.py")

    def get_db_connection():
        """Kết nối database PostgreSQL từ config"""
        try:
            conn = psycopg2.connect(**PG_CONFIG)
            return conn
        except Exception as e:
            print(f"❌ Lỗi kết nối database: {e}")
            return None

except ImportError:
    print("⚠️  db_config.py not found, using fallback connection")

    def get_db_connection():
        """Fallback database connection"""
        try:
            conn = psycopg2.connect(
                host="localhost",
                database="sapmmo",
                user="alandoan",
                password="",
                port="5432"
            )
            return conn
        except Exception as e:
            print(f"❌ Lỗi kết nối database: {e}")
            return None

def check_column_exists(cursor, table_name, column_name):
    """Kiểm tra xem column có tồn tại không"""
    cursor.execute("""
        SELECT COUNT(*) 
        FROM information_schema.columns 
        WHERE table_name = %s AND column_name = %s
    """, (table_name, column_name))
    return cursor.fetchone()[0] > 0

def remove_max_quantity_column(cursor):
    """Xóa cột max_quantity khỏi bảng Products"""
    print("🔧 Removing max_quantity column from Products table...")
    
    if not check_column_exists(cursor, 'Products', 'max_quantity'):
        print("   ⚠️  max_quantity column doesn't exist")
        return True
    
    try:
        cursor.execute('''
            ALTER TABLE "Products" 
            DROP COLUMN IF EXISTS max_quantity
        ''')
        print("   ✅ Removed max_quantity column successfully")
        return True
    except Exception as e:
        print(f"   ❌ Error removing max_quantity column: {e}")
        return False

def revert_product_type_changes(cursor):
    """Revert product_type changes cho Bối Cảnh Live products"""
    print("🔧 Reverting product_type changes for Bối Cảnh Live products...")
    
    try:
        cursor.execute('''
            UPDATE "Products" 
            SET product_type = 'win_product'
            FROM "ProductTypes" pt
            WHERE "Products".product_type_id = pt.type_id
              AND pt.name = 'Bối Cảnh Live'
              AND "Products".product_type = 'Bối Cảnh Live'
        ''')
        
        updated_count = cursor.rowcount
        print(f"   ✅ Reverted {updated_count} products back to win_product")
        return True
    except Exception as e:
        print(f"   ❌ Error reverting product_type: {e}")
        return False

def remove_metadata_changes(cursor):
    """Xóa metadata changes liên quan đến max_quantity"""
    print("🔧 Removing metadata changes...")
    
    try:
        cursor.execute('''
            UPDATE "Products" 
            SET metadata = metadata - 'single_purchase_only' - 'max_quantity'
            WHERE metadata ? 'single_purchase_only'
        ''')
        
        updated_count = cursor.rowcount
        print(f"   ✅ Removed metadata changes from {updated_count} products")
        return True
    except Exception as e:
        print(f"   ❌ Error removing metadata changes: {e}")
        return False

def drop_performance_index(cursor):
    """Xóa performance index"""
    print("🔧 Dropping performance index...")
    
    try:
        cursor.execute('''
            DROP INDEX IF EXISTS idx_products_max_quantity
        ''')
        print("   ✅ Dropped performance index")
        return True
    except Exception as e:
        print(f"   ❌ Error dropping index: {e}")
        return False

def verify_rollback(cursor):
    """Xác minh rollback đã thành công"""
    print("🔍 Verifying rollback...")
    
    try:
        # Kiểm tra column đã bị xóa
        if check_column_exists(cursor, 'Products', 'max_quantity'):
            print("   ❌ max_quantity column still exists")
            return False
        
        print("   ✅ max_quantity column removed")
        
        # Kiểm tra product_type đã được revert
        cursor.execute('''
            SELECT COUNT(*) 
            FROM "Products" p
            JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
            WHERE pt.name = 'Bối Cảnh Live' 
              AND p.product_type = 'Bối Cảnh Live'
        ''')
        
        boi_canh_count = cursor.fetchone()[0]
        if boi_canh_count > 0:
            print(f"   ⚠️  {boi_canh_count} products still have product_type = 'Bối Cảnh Live'")
        else:
            print("   ✅ All Bối Cảnh Live products reverted to win_product")
        
        # Kiểm tra metadata
        cursor.execute('''
            SELECT COUNT(*) 
            FROM "Products" 
            WHERE metadata ? 'single_purchase_only'
        ''')
        
        metadata_count = cursor.fetchone()[0]
        if metadata_count > 0:
            print(f"   ⚠️  {metadata_count} products still have single_purchase_only metadata")
        else:
            print("   ✅ All single_purchase_only metadata removed")
        
        # Thống kê sau rollback
        cursor.execute('''
            SELECT 
                product_type,
                COUNT(*) as total_products
            FROM "Products" 
            WHERE is_active = true
            GROUP BY product_type
            ORDER BY total_products DESC
        ''')
        
        print("\n   📊 Products summary after rollback:")
        for row in cursor.fetchall():
            print(f"      {row[0]}: {row[1]} products")
        
        return True
    except Exception as e:
        print(f"   ❌ Error during verification: {e}")
        return False

def get_rollback_summary(cursor):
    """Lấy tổng kết rollback"""
    try:
        cursor.execute('SELECT COUNT(*) FROM "Products" WHERE is_active = true')
        total_products = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM "Products" WHERE product_type = \'win_product\'')
        win_products = cursor.fetchone()[0]
        
        return {
            'total_products': total_products,
            'win_products': win_products
        }
    except Exception as e:
        print(f"❌ Error getting rollback summary: {e}")
        return None

def main():
    """Main rollback function"""
    print("🔄 Starting Marketplace Updates Rollback")
    print("=" * 60)
    print(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Rolling back: Max Quantity & Dynamic Product Types")
    print("=" * 60)
    
    # Xác nhận rollback
    print("⚠️  WARNING: This will rollback all changes made by the migration!")
    print("   - Remove max_quantity column")
    print("   - Revert product_type changes")
    print("   - Remove metadata changes")
    print("   - Drop performance index")
    print()
    
    confirm = input("Are you sure you want to proceed with rollback? (type 'YES' to confirm): ")
    if confirm != 'YES':
        print("❌ Rollback cancelled")
        sys.exit(0)
    
    # Kết nối database
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database. Aborting rollback.")
        sys.exit(1)
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Bắt đầu transaction
        print("🔄 Starting rollback transaction...")
        
        # Thực hiện các rollback steps
        steps = [
            ("Drop performance index", drop_performance_index),
            ("Remove metadata changes", remove_metadata_changes),
            ("Revert product_type changes", revert_product_type_changes),
            ("Remove max_quantity column", remove_max_quantity_column),
            ("Verify rollback", verify_rollback)
        ]
        
        success = True
        for step_name, step_func in steps:
            print(f"\n📋 Step: {step_name}")
            if not step_func(cursor):
                success = False
                break
        
        if success:
            # Commit transaction
            conn.commit()
            print("\n✅ Rollback transaction committed successfully")
            
            # Lấy tổng kết
            summary = get_rollback_summary(cursor)
            if summary:
                print("\n🎉 ROLLBACK COMPLETED SUCCESSFULLY!")
                print("=" * 50)
                print(f"Total active products: {summary['total_products']}")
                print(f"Win products: {summary['win_products']}")
                print("\n✅ Changes rolled back:")
                print("   - max_quantity column removed")
                print("   - Product types reverted to win_product")
                print("   - Metadata changes removed")
                print("   - Performance index dropped")
                print("\n🔄 Database restored to previous state")
        else:
            # Rollback transaction
            conn.rollback()
            print("\n❌ Rollback failed. Transaction rolled back.")
            sys.exit(1)
        
    except Exception as e:
        print(f"\n❌ Unexpected error during rollback: {e}")
        print("Traceback:")
        traceback.print_exc()
        conn.rollback()
        sys.exit(1)
    
    finally:
        cursor.close()
        conn.close()
        print("\n🔌 Database connection closed")

if __name__ == "__main__":
    main()
