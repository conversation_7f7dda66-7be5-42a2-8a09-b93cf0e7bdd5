#!/usr/bin/env python3
"""
Fix AFF Packages issues on server:
1. Create missing tables (SubscriptionHistory, AFFPackages)
2. Fix foreign key constraints
3. Sync with local structure
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_server_aff_packages():
    """Fix all AFF packages related issues on server"""
    
    print("🚀 Fixing AFF Packages issues on server...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check existing tables
        print("\n🔧 Step 1: Checking existing tables...")
        
        tables_to_check = ['AFFPackages', 'UserSubscriptions', 'SubscriptionHistory']
        existing_tables = {}
        
        for table in tables_to_check:
            cursor.execute(f'''
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' AND table_name = '{table}'
                );
            ''')
            exists = cursor.fetchone()[0]
            existing_tables[table] = exists
            print(f"  {table}: {'✅ EXISTS' if exists else '❌ MISSING'}")
        
        # Step 2: Create AFFPackages table if missing
        if not existing_tables['AFFPackages']:
            print("\n🔧 Step 2: Creating AFFPackages table...")
            
            cursor.execute('''
                CREATE TABLE "public"."AFFPackages" (
                    "package_id" SERIAL PRIMARY KEY,
                    "package_name" varchar(100) NOT NULL,
                    "description" text,
                    "account_limit" int4 NOT NULL DEFAULT 10,
                    "monthly_price" int4 NOT NULL DEFAULT 300000,
                    "yearly_discount_percent" int4 NOT NULL DEFAULT 20,
                    "is_recommended" bool NOT NULL DEFAULT false,
                    "is_active" bool NOT NULL DEFAULT true,
                    "created_by" int4,
                    "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
                    "updated_at" timestamp DEFAULT CURRENT_TIMESTAMP,
                    "is_deleted" bool NOT NULL DEFAULT false
                );
            ''')
            
            print("  ✅ Created AFFPackages table")
            
            # Insert default packages
            print("  🔧 Inserting default AFF packages...")
            
            default_packages = [
                ('Starter', 'Gói cơ bản cho người mới bắt đầu', 10, 300000, 20, False),
                ('Professional', 'Gói chuyên nghiệp cho doanh nghiệp nhỏ', 30, 800000, 25, True),
                ('Premium', 'Gói cao cấp cho doanh nghiệp lớn', 50, 1500000, 30, False),
                ('Enterprise', 'Gói doanh nghiệp không giới hạn', 999999, 3000000, 35, False)
            ]
            
            for name, desc, limit, price, discount, recommended in default_packages:
                cursor.execute('''
                    INSERT INTO "AFFPackages" (package_name, description, account_limit, monthly_price, yearly_discount_percent, is_recommended, created_by)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                ''', (name, desc, limit, price, discount, recommended, 1))
            
            print(f"  ✅ Created {len(default_packages)} default packages")
        else:
            print("\n✅ AFFPackages table already exists")
        
        # Step 3: Create SubscriptionHistory table with correct foreign keys
        if not existing_tables['SubscriptionHistory']:
            print("\n🔧 Step 3: Creating SubscriptionHistory table...")
            
            # Create sequence first
            cursor.execute('''
                CREATE SEQUENCE IF NOT EXISTS "SubscriptionHistory_history_id_seq"
                INCREMENT 1
                MINVALUE 1
                MAXVALUE **********
                START 1
                CACHE 1;
            ''')
            
            # Create table WITHOUT foreign key constraints first
            cursor.execute('''
                CREATE TABLE "public"."SubscriptionHistory" (
                    "history_id" int4 NOT NULL DEFAULT nextval('"SubscriptionHistory_history_id_seq"'::regclass),
                    "user_id" int4 NOT NULL,
                    "action_type" varchar NOT NULL,
                    "old_package_id" int4,
                    "new_package_id" int4,
                    "old_subscription_id" int4,
                    "new_subscription_id" int4,
                    "amount_paid" int4 NOT NULL DEFAULT 0,
                    "refund_amount" int4 NOT NULL DEFAULT 0,
                    "description" text,
                    "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
                    CONSTRAINT "SubscriptionHistory_pkey" PRIMARY KEY ("history_id")
                );
            ''')
            
            print("  ✅ Created SubscriptionHistory table")
            
            # Add foreign key to Users table (safe)
            try:
                cursor.execute('''
                    ALTER TABLE "public"."SubscriptionHistory" 
                    ADD CONSTRAINT "fk_subscription_history_user_id" 
                    FOREIGN KEY ("user_id") REFERENCES "public"."Users" ("user_id") 
                    ON DELETE CASCADE ON UPDATE CASCADE;
                ''')
                print("  ✅ Added FK: user_id → Users.user_id")
            except Exception as e:
                print(f"  ⚠️  FK user_id: {e}")
            
            # Add foreign key to AFFPackages (now safe since we created it)
            try:
                cursor.execute('''
                    ALTER TABLE "public"."SubscriptionHistory" 
                    ADD CONSTRAINT "fk_subscription_history_old_package" 
                    FOREIGN KEY ("old_package_id") REFERENCES "public"."AFFPackages" ("package_id") 
                    ON DELETE SET NULL ON UPDATE CASCADE;
                ''')
                print("  ✅ Added FK: old_package_id → AFFPackages.package_id")
            except Exception as e:
                print(f"  ⚠️  FK old_package_id: {e}")
            
            try:
                cursor.execute('''
                    ALTER TABLE "public"."SubscriptionHistory" 
                    ADD CONSTRAINT "fk_subscription_history_new_package" 
                    FOREIGN KEY ("new_package_id") REFERENCES "public"."AFFPackages" ("package_id") 
                    ON DELETE SET NULL ON UPDATE CASCADE;
                ''')
                print("  ✅ Added FK: new_package_id → AFFPackages.package_id")
            except Exception as e:
                print(f"  ⚠️  FK new_package_id: {e}")
            
            # Add indexes
            indexes = [
                ('idx_subscription_history_user_id', 'user_id'),
                ('idx_subscription_history_action_type', 'action_type'),
                ('idx_subscription_history_created_at', 'created_at')
            ]
            
            for index_name, column in indexes:
                try:
                    cursor.execute(f'''
                        CREATE INDEX IF NOT EXISTS {index_name} 
                        ON "public"."SubscriptionHistory"({column});
                    ''')
                    print(f"  ✅ Created index: {index_name}")
                except Exception as e:
                    print(f"  ⚠️  Index {index_name}: {e}")
        
        else:
            print("\n🔧 Step 3: Fixing existing SubscriptionHistory foreign keys...")
            
            # Drop wrong foreign key constraints if they exist
            wrong_constraints = [
                'fk_subscription_history_new_package',
                'fk_subscription_history_old_package'
            ]
            
            for constraint in wrong_constraints:
                try:
                    cursor.execute(f'''
                        ALTER TABLE "public"."SubscriptionHistory" 
                        DROP CONSTRAINT IF EXISTS "{constraint}";
                    ''')
                    print(f"  ✅ Dropped constraint: {constraint}")
                except Exception as e:
                    print(f"  ⚠️  Drop constraint {constraint}: {e}")
            
            # Add correct foreign keys to AFFPackages
            try:
                cursor.execute('''
                    ALTER TABLE "public"."SubscriptionHistory" 
                    ADD CONSTRAINT "fk_subscription_history_old_package_aff" 
                    FOREIGN KEY ("old_package_id") REFERENCES "public"."AFFPackages" ("package_id") 
                    ON DELETE SET NULL ON UPDATE CASCADE;
                ''')
                print("  ✅ Added FK: old_package_id → AFFPackages.package_id")
            except Exception as e:
                print(f"  ⚠️  FK old_package_id: {e}")
            
            try:
                cursor.execute('''
                    ALTER TABLE "public"."SubscriptionHistory" 
                    ADD CONSTRAINT "fk_subscription_history_new_package_aff" 
                    FOREIGN KEY ("new_package_id") REFERENCES "public"."AFFPackages" ("package_id") 
                    ON DELETE SET NULL ON UPDATE CASCADE;
                ''')
                print("  ✅ Added FK: new_package_id → AFFPackages.package_id")
            except Exception as e:
                print(f"  ⚠️  FK new_package_id: {e}")
        
        # Step 4: Check UserSubscriptions table
        if not existing_tables['UserSubscriptions']:
            print("\n🔧 Step 4: Creating UserSubscriptions table...")
            
            cursor.execute('''
                CREATE TABLE "public"."UserSubscriptions" (
                    "subscription_id" SERIAL PRIMARY KEY,
                    "user_id" int4 NOT NULL,
                    "package_id" int4 NOT NULL,
                    "subscription_type" varchar(10) NOT NULL CHECK (subscription_type IN ('monthly', 'yearly')),
                    "status" varchar(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'cancelled', 'expired')),
                    "start_date" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    "end_date" timestamp NOT NULL,
                    "amount_paid" int4 NOT NULL DEFAULT 0,
                    "created_at" timestamp DEFAULT CURRENT_TIMESTAMP,
                    "updated_at" timestamp DEFAULT CURRENT_TIMESTAMP
                );
            ''')
            
            # Add foreign keys
            cursor.execute('''
                ALTER TABLE "public"."UserSubscriptions" 
                ADD CONSTRAINT "fk_user_subscriptions_user_id" 
                FOREIGN KEY ("user_id") REFERENCES "public"."Users" ("user_id") 
                ON DELETE CASCADE ON UPDATE CASCADE;
            ''')
            
            cursor.execute('''
                ALTER TABLE "public"."UserSubscriptions" 
                ADD CONSTRAINT "fk_user_subscriptions_package_id" 
                FOREIGN KEY ("package_id") REFERENCES "public"."AFFPackages" ("package_id") 
                ON DELETE CASCADE ON UPDATE CASCADE;
            ''')
            
            # Add indexes
            cursor.execute('''
                CREATE INDEX idx_user_subscriptions_user_id ON "public"."UserSubscriptions"(user_id);
            ''')
            cursor.execute('''
                CREATE INDEX idx_user_subscriptions_status ON "public"."UserSubscriptions"(status);
            ''')
            cursor.execute('''
                CREATE INDEX idx_user_subscriptions_end_date ON "public"."UserSubscriptions"(end_date);
            ''')
            
            print("  ✅ Created UserSubscriptions table with foreign keys and indexes")
        else:
            print("\n✅ UserSubscriptions table already exists")
        
        # Step 5: Test the fix
        print("\n🔧 Step 5: Testing the fix...")
        
        # Check AFFPackages
        cursor.execute('SELECT COUNT(*) FROM "AFFPackages";')
        aff_count = cursor.fetchone()[0]
        print(f"  📊 AFFPackages records: {aff_count}")
        
        if aff_count > 0:
            cursor.execute('SELECT package_id, package_name FROM "AFFPackages" LIMIT 3;')
            packages = cursor.fetchall()
            print(f"  📋 Sample packages:")
            for pkg in packages:
                print(f"    - ID {pkg[0]}: {pkg[1]}")
        
        # Test SubscriptionHistory insert
        try:
            cursor.execute('SAVEPOINT test_subscription_history;')
            
            # Get first AFF package ID
            cursor.execute('SELECT package_id FROM "AFFPackages" LIMIT 1;')
            test_package_id = cursor.fetchone()[0]
            
            cursor.execute('''
                INSERT INTO "SubscriptionHistory" (user_id, action_type, new_package_id, amount_paid, description)
                VALUES (1, 'test', %s, 0, 'Test subscription history')
                RETURNING history_id;
            ''', (test_package_id,))
            
            test_id = cursor.fetchone()[0]
            print(f"  ✅ Test SubscriptionHistory INSERT successful, got history_id: {test_id}")
            
            # Rollback test
            cursor.execute('ROLLBACK TO SAVEPOINT test_subscription_history;')
            print("  ✅ Test data rolled back")
            
        except Exception as e:
            print(f"  ❌ SubscriptionHistory test failed: {e}")
            cursor.execute('ROLLBACK TO SAVEPOINT test_subscription_history;')
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 AFF Packages fix completed successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test AFF package purchase")
        print("   3. Check logs: sudo journalctl -u sapmmo -f")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_server_aff_packages()
    sys.exit(0 if success else 1)
