#!/usr/bin/env python3
"""
Compare database structure between localhost and server
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def check_database_structure():
    """Check current database structure"""
    
    print("🔍 Checking database structure on server...")
    print("=" * 60)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        print(f"Database: {PG_CONFIG['database']} on {PG_CONFIG['host']}")
        
        # Check if Discounts table exists
        print("\n🔍 Checking Discounts table...")
        cursor.execute('''
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'Discounts';
        ''')
        
        discounts_table = cursor.fetchone()
        if discounts_table:
            print("✅ Discounts table exists")
            
            # Get Discounts table structure
            cursor.execute('''
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'Discounts' AND table_schema = 'public'
                ORDER BY ordinal_position;
            ''')
            
            columns = cursor.fetchall()
            print(f"📋 Discounts table has {len(columns)} columns:")
            for col in columns:
                nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
                default = f" DEFAULT {col[3]}" if col[3] else ""
                print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        else:
            print("❌ Discounts table does not exist!")
            print("🔧 Creating Discounts table with correct structure...")
            
            # Create Discounts table with the exact structure that code expects
            cursor.execute('''
                CREATE TABLE "Discounts" (
                    discount_id SERIAL PRIMARY KEY,
                    code VARCHAR(50) UNIQUE NOT NULL,
                    description TEXT,
                    type VARCHAR(20) NOT NULL DEFAULT 'percentage',
                    value DECIMAL(10,2) NOT NULL,
                    min_amount DECIMAL(15,2) DEFAULT 0,
                    max_amount DECIMAL(15,2),
                    limit_usage INTEGER DEFAULT NULL,
                    used_count INTEGER DEFAULT 0,
                    limit_per_user INTEGER DEFAULT 1,
                    is_active BOOLEAN DEFAULT TRUE,
                    valid_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    valid_to TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            ''')
            
            print("✅ Discounts table created with correct structure")
            
            # Create indexes
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_discounts_code ON "Discounts"(code);')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_discounts_active ON "Discounts"(is_active);')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_discounts_type ON "Discounts"(type);')
            
            print("✅ Indexes created")
            
            # Insert sample data
            sample_discounts = [
                ('WELCOME10', 'Chào mừng khách hàng mới - Giảm 10%', 'percentage', 10.00, 100000, 50000, 100, 1),
                ('SAVE50K', 'Giảm 50,000 MP cho đơn hàng từ 500K', 'fixed', 50000, 500000, None, 50, 1),
                ('VIP20', 'VIP 20% - Giảm 20% cho khách VIP', 'percentage', 20.00, 200000, 100000, 20, 2)
            ]
            
            for code, desc, dtype, value, min_amt, max_amt, limit_usage, limit_per_user in sample_discounts:
                cursor.execute('''
                    INSERT INTO "Discounts" (code, description, type, value, min_amount, max_amount, limit_usage, limit_per_user)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s);
                ''', (code, desc, dtype, value, min_amt, max_amt, limit_usage, limit_per_user))
            
            print("✅ Sample discount codes inserted")
        
        # Check all marketplace tables
        print("\n🔍 Checking all marketplace tables...")
        marketplace_tables = [
            'ProductCategories', 'Products', 'ProductFiles', 'AccountPackages',
            'PackageAccounts', 'Orders', 'OrderItems', 'AFFPackages', 
            'UserSubscriptions', 'ProductTypes', 'WarrantyRequests',
            'MarketplaceTransactions', 'Config', 'Discounts'
        ]
        
        for table in marketplace_tables:
            cursor.execute(f'''
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = '{table}';
            ''')
            
            exists = cursor.fetchone()[0]
            if exists:
                cursor.execute(f'SELECT COUNT(*) FROM "{table}";')
                count = cursor.fetchone()[0]
                print(f"✅ {table}: {count} records")
            else:
                print(f"❌ {table}: missing")
        
        # Test the exact query that's failing
        print("\n🔍 Testing the failing query...")
        try:
            cursor.execute('''
                SELECT d.discount_id, d.code, d.description, d.type, d.value,
                       d.min_amount, d.max_amount, d.limit_usage, d.used_count, d.is_active
                FROM "Discounts" d
                WHERE d.is_active = TRUE
                ORDER BY d.created_at DESC
                LIMIT 5;
            ''')
            
            results = cursor.fetchall()
            print(f"✅ Query successful - found {len(results)} active discounts")
            
            for row in results:
                print(f"  - {row[1]}: {row[2]} ({row[3]} {row[4]})")
                
        except Exception as e:
            print(f"❌ Query failed: {e}")
        
        # Commit changes
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Database structure check completed!")
        return True
        
    except Exception as e:
        print(f"❌ Check failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = check_database_structure()
    sys.exit(0 if success else 1)
