#!/usr/bin/env python3
"""
Fix Users table structure for marketplace
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_users_table():
    """Fix Users table structure"""
    
    print("🔧 Fixing Users table structure...")
    print("=" * 50)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Check Users table structure
        print("\n🔍 Checking Users table structure...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        columns = cursor.fetchall()
        print(f"📋 Users table has {len(columns)} columns:")
        for col in columns:
            print(f"  - {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})")
        
        # Check if password_hash column exists
        column_names = [col[0] for col in columns]
        
        if 'password_hash' not in column_names:
            print("\n❌ password_hash column missing")
            print("🔧 Adding password_hash column...")
            
            # Add password_hash column
            cursor.execute('''
                ALTER TABLE "Users" 
                ADD COLUMN password_hash VARCHAR(255);
            ''')
            
            # If there's a 'password' column, copy data and hash it
            if 'password' in column_names:
                print("🔧 Found 'password' column, migrating to password_hash...")
                
                # Simple migration - copy password to password_hash for now
                # In production, you should hash the passwords properly
                cursor.execute('''
                    UPDATE "Users" 
                    SET password_hash = password 
                    WHERE password IS NOT NULL;
                ''')
                
                print("✅ Migrated password data to password_hash")
            else:
                print("⚠️  No 'password' column found, setting default password_hash")
                # Set a default password hash for existing users
                cursor.execute('''
                    UPDATE "Users" 
                    SET password_hash = 'default_hash' 
                    WHERE password_hash IS NULL;
                ''')
            
            print("✅ password_hash column added successfully")
        else:
            print("✅ password_hash column already exists")
        
        # Check other required columns for marketplace
        required_columns = {
            'mp_balance': 'INTEGER DEFAULT 0',
            'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'updated_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'is_active': 'BOOLEAN DEFAULT TRUE'
        }
        
        for col_name, col_def in required_columns.items():
            if col_name not in column_names:
                print(f"🔧 Adding {col_name} column...")
                cursor.execute(f'ALTER TABLE "Users" ADD COLUMN {col_name} {col_def};')
                print(f"✅ {col_name} column added")
            else:
                print(f"✅ {col_name} column already exists")
        
        # Ensure Users table has primary key
        cursor.execute('''
            SELECT constraint_name 
            FROM information_schema.table_constraints 
            WHERE table_name = 'Users' 
            AND constraint_type = 'PRIMARY KEY';
        ''')
        
        pk_constraints = cursor.fetchall()
        if not pk_constraints:
            print("🔧 Adding primary key to Users table...")
            try:
                cursor.execute('ALTER TABLE "Users" ADD PRIMARY KEY (user_id);')
                print("✅ Primary key added to Users table")
            except Exception as e:
                print(f"⚠️  Could not add primary key: {e}")
        else:
            print("✅ Users table has primary key")
        
        # Commit changes
        conn.commit()
        
        # Validate Users table
        print("\n🔍 Final Users table validation...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Users table now has {len(final_columns)} columns:")
        for col in final_columns:
            print(f"  - {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})")
        
        # Check sample data
        cursor.execute('SELECT COUNT(*) FROM "Users";')
        user_count = cursor.fetchone()[0]
        print(f"\n📊 Users table has {user_count} users")
        
        if user_count > 0:
            cursor.execute('''
                SELECT user_id, username, role, 
                       CASE WHEN password_hash IS NOT NULL THEN 'YES' ELSE 'NO' END as has_password_hash,
                       COALESCE(mp_balance, 0) as mp_balance
                FROM "Users" 
                LIMIT 3;
            ''')
            
            sample_users = cursor.fetchall()
            print("📋 Sample users:")
            for user in sample_users:
                print(f"  - ID {user[0]}: {user[1]} ({user[2]}) - Password: {user[3]} - MP: {user[4]}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Users table fixed successfully!")
        print("📝 Next steps:")
        print("   1. Restart the sapmmo service")
        print("   2. Test marketplace functionality")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_users_table()
    sys.exit(0 if success else 1)
