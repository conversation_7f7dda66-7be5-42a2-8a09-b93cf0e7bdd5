#!/usr/bin/env python3
"""
Test category filtering fix
"""

import psycopg2
import psycopg2.extras
import json

def get_db_connection():
    """Kết nối database PostgreSQL local"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def test_api_response_structure():
    """Test API response structure mới"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🧪 Testing API Response Structure")
        print("=" * 50)
        
        # Simulate API query (updated)
        cursor.execute('''
            SELECT p.product_id, p.name, p.short_description, p.description, p.price, p.stock,
                   p.unlimited_stock, p.product_type, p.image_url, p.is_featured,
                   p.category_id, c.name as category_name, c.icon as category_icon, p.status,
                   (SELECT COUNT(*) FROM "OrderItems" oi
                    JOIN "Orders" o ON oi.order_id = o.order_id
                    WHERE oi.product_id = p.product_id AND o.status = 'completed') as sold_count,
                   p.is_deleted, pt.name as type_name, p.max_quantity, p.created_at
            FROM "Products" p
            LEFT JOIN "ProductCategories" c ON p.category_id = c.category_id
            LEFT JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
            WHERE p.status = 'active' AND (p.is_deleted = FALSE OR p.is_deleted IS NULL) AND c.is_active = true
            ORDER BY p.is_featured DESC, p.created_at DESC
            LIMIT 5
        ''')
        
        products = []
        for row in cursor.fetchall():
            products.append({
                'product_id': row[0],
                'name': row[1],
                'short_description': row[2],
                'description': row[3],
                'price': float(row[4]),
                'stock': row[5],
                'unlimited_stock': row[6],
                'product_type': row[7],
                'image_url': row[8] or '/static/marketplace/thumbnails/default.png',
                'is_featured': row[9],
                'category_id': row[10],  # ✅ Now included!
                'category_name': row[11],
                'category_icon': row[12],
                'status': row[13] or 'active',
                'sold_count': row[14] or 0,
                'is_deleted': row[15] or False,
                'type_name': row[16],
                'max_quantity': row[17],
                'created_at': row[18].isoformat() if row[18] else None
            })
        
        print(f"📋 Sample API response (first 3 products):")
        for i, product in enumerate(products[:3]):
            print(f"\n{i+1}. {product['name']}")
            print(f"   product_id: {product['product_id']}")
            print(f"   category_id: {product['category_id']} ✅")
            print(f"   category_name: {product['category_name']}")
            print(f"   product_type: {product['product_type']}")
            print(f"   type_name: {product['type_name']}")
            print(f"   price: {product['price']:,}")
            print(f"   max_quantity: {product['max_quantity']}")
            print(f"   created_at: {product['created_at']}")
        
        # Test filtering simulation
        print(f"\n🔍 Testing filtering simulation:")
        
        # Get unique categories
        unique_categories = {}
        for product in products:
            cat_id = product['category_id']
            if cat_id not in unique_categories:
                unique_categories[cat_id] = {
                    'category_id': cat_id,
                    'category_name': product['category_name'],
                    'products': []
                }
            unique_categories[cat_id]['products'].append(product['name'])
        
        for cat_id, cat_info in unique_categories.items():
            print(f"\nCategory {cat_id} ({cat_info['category_name']}):")
            for product_name in cat_info['products']:
                print(f"   - {product_name}")
        
        # Test filtering logic
        def test_category_filter(products, category_filter):
            """Test category filtering logic"""
            filtered = []
            for product in products:
                if category_filter and str(product['category_id']) != str(category_filter):
                    continue
                filtered.append(product)
            return filtered
        
        # Test with category 2 (Win Products)
        category_2_products = test_category_filter(products, 2)
        print(f"\n📊 Filter by category 2: {len(category_2_products)} products")
        for product in category_2_products:
            print(f"   - {product['name']} (category_id: {product['category_id']})")
        
        cursor.close()
        conn.close()
        
        # Validate structure
        has_category_id = all('category_id' in p for p in products)
        has_created_at = all('created_at' in p for p in products)
        
        if has_category_id and has_created_at:
            print(f"\n✅ API response structure is correct!")
            return True
        else:
            print(f"\n❌ API response missing required fields")
            return False
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_frontend_filtering_logic():
    """Test frontend filtering logic với data mới"""
    print("\n🧪 Testing Frontend Filtering Logic")
    print("=" * 50)
    
    # Sample data với structure mới
    sample_products = [
        {
            'product_id': 1,
            'name': 'Background WIN',
            'category_id': 2,  # Win Products
            'category_name': 'Win Products',
            'product_type': 'Bối Cảnh Live',
            'price': 50000,
            'created_at': '2025-09-04T10:00:00'
        },
        {
            'product_id': 2,
            'name': 'TikTok Account Premium',
            'category_id': 1,  # Tài Khoản TIKTOK US
            'category_name': 'Tài Khoản TIKTOK US',
            'product_type': 'account',
            'price': 550000,
            'created_at': '2025-09-04T09:00:00'
        },
        {
            'product_id': 3,
            'name': 'Video Course',
            'category_id': 2,  # Win Products
            'category_name': 'Win Products',
            'product_type': 'course',
            'price': 200000,
            'created_at': '2025-09-04T11:00:00'
        }
    ]
    
    def filter_products(products, category_filter='', search_term='', sort_filter='newest'):
        """Simulate frontend filtering"""
        filtered = products.copy()
        
        # Search filter
        if search_term:
            filtered = [p for p in filtered if 
                       search_term.lower() in p['name'].lower()]
        
        # Category filter (fixed logic)
        if category_filter:
            filtered = [p for p in filtered if 
                       str(p['category_id']) == str(category_filter)]
        
        # Sort
        if sort_filter == 'price_low':
            filtered.sort(key=lambda x: x['price'])
        elif sort_filter == 'price_high':
            filtered.sort(key=lambda x: x['price'], reverse=True)
        elif sort_filter == 'newest':
            filtered.sort(key=lambda x: x['created_at'], reverse=True)
        
        return filtered
    
    # Test scenarios
    scenarios = [
        {
            'name': 'No filters',
            'params': {},
            'expected_count': 3
        },
        {
            'name': 'Category 2 (Win Products)',
            'params': {'category_filter': 2},
            'expected_count': 2
        },
        {
            'name': 'Category 1 (Tài Khoản)',
            'params': {'category_filter': 1},
            'expected_count': 1
        },
        {
            'name': 'Search "background"',
            'params': {'search_term': 'background'},
            'expected_count': 1
        },
        {
            'name': 'Category 2 + Sort by price high',
            'params': {'category_filter': 2, 'sort_filter': 'price_high'},
            'expected_count': 2
        }
    ]
    
    print(f"📋 Testing filtering scenarios:")
    
    all_passed = True
    for scenario in scenarios:
        result = filter_products(sample_products, **scenario['params'])
        success = len(result) == scenario['expected_count']
        status = "✅ PASS" if success else "❌ FAIL"
        
        if not success:
            all_passed = False
        
        print(f"\n{scenario['name']}: {status}")
        print(f"   Expected: {scenario['expected_count']}, Got: {len(result)}")
        
        if result:
            for product in result:
                print(f"   - {product['name']} (category_id: {product['category_id']}, price: {product['price']:,})")
    
    return all_passed

def main():
    """Main function"""
    print("🧪 Testing Category Filtering Fix")
    print("=" * 60)
    
    # Test 1: API response structure
    api_success = test_api_response_structure()
    
    # Test 2: Frontend filtering logic
    frontend_success = test_frontend_filtering_logic()
    
    print(f"\n✅ Test Summary:")
    print(f"   🔌 API Response: {'✅ PASS' if api_success else '❌ FAIL'}")
    print(f"   🖥️ Frontend Logic: {'✅ PASS' if frontend_success else '❌ FAIL'}")
    
    if api_success and frontend_success:
        print(f"\n🎉 Category filtering should work now!")
        print(f"\n🔧 What was fixed:")
        print(f"   ✅ Added category_id to API response")
        print(f"   ✅ Added created_at for proper newest sort")
        print(f"   ✅ Fixed type comparison: String(category_id) === String(filter)")
        print(f"   ✅ Updated API query to include p.category_id")
        print(f"   ✅ Updated response mapping with correct indices")
        
        print(f"\n🎯 Test on UI:")
        print(f"   1. Vào /marketplace")
        print(f"   2. Chọn 'Win Products' từ dropdown → Should show products")
        print(f"   3. Click category card 'Win Products' → Should filter + scroll")
        print(f"   4. Test sort options → Should work correctly")
        print(f"   5. Open browser console → Should see correct category_id values")
    else:
        print(f"\n❌ Some tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
