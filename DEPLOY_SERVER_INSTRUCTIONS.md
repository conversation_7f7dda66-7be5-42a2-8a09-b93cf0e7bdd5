# Hướng dẫn Deploy Server - Fix Password Column

## 🎯 Mục tiêu
Fix lỗi `column "password_hash" of relation "Users" does not exist` trên server bằng cách thống nhất sử dụng cột `password`.

## 📋 Các bước thực hiện

### Bước 1: Upload files lên server
Upload các files sau lên server:
- `fix_server_password_column.py` - Migration script
- `mip_system.py` - Code đã được cập nhật

### Bước 2: Chạy migration trên server
```bash
# SSH vào server
ssh user@your-server

# Chuyển đến thư mục project
cd /var/www/mip

# Chạy migration script
python3 fix_server_password_column.py
```

### Bước 3: Restart services
```bash
# Restart gunicorn service
sudo systemctl restart gunicorn

# Hoặc restart nginx nếu cần
sudo systemctl restart nginx
```

## 🔧 Chi tiết migration

### Database changes:
- **T<PERSON><PERSON>ớc**: C<PERSON>t `password_hash` 
- **Sau**: C<PERSON>t `password`

### Code changes:
- Route `/register`: `password_hash` → `password`
- Route `/add_user`: `password_hash` → `password` 
- Route `/edit_user`: `password_hash` → `password`
- Route `/api/auth/login`: `password_hash` → `password`

## ✅ Verification

Sau khi deploy, test các chức năng:

### 1. Test Registration
```bash
# Truy cập trang đăng ký
https://your-domain.com/register

# Thử đăng ký với:
Username: Test.User-123
Email: <EMAIL>
Phone: 0123456789
Password: password123
```

**Expected result:**
- ✅ Đăng ký thành công
- ✅ Redirect về trang login với username pre-filled: `testuser123`
- ✅ Hiển thị thông báo success màu xanh

### 2. Test Login
```bash
# Sau khi đăng ký, thử login với:
Username: testuser123 (đã được pre-filled)
Password: password123
```

**Expected result:**
- ✅ Login thành công
- ✅ Redirect về dashboard

### 3. Test Database
```sql
-- Kiểm tra user vừa tạo
SELECT username, role, unit_code, email, phone 
FROM "Users" 
WHERE username = 'testuser123';
```

**Expected result:**
- ✅ Username: `testuser123` (normalized)
- ✅ Unit Code: `SAPMMOtestuser123XXXX` (clean, no special chars)
- ✅ Email và phone đúng format

## 🚨 Troubleshooting

### Lỗi: "column password does not exist"
```bash
# Kiểm tra cột hiện có
psql -d sapmmo -c "SELECT column_name FROM information_schema.columns WHERE table_name = 'Users' AND column_name LIKE '%password%';"

# Nếu vẫn có password_hash, chạy lại migration
python3 fix_server_password_column.py
```

### Lỗi: "duplicate key value violates unique constraint"
```bash
# Kiểm tra username trùng lặp
psql -d sapmmo -c "SELECT username, COUNT(*) FROM \"Users\" GROUP BY username HAVING COUNT(*) > 1;"

# Fix duplicates nếu có
python3 fix_duplicate_usernames.py
```

### Lỗi: Service không restart
```bash
# Kiểm tra status
sudo systemctl status gunicorn

# Xem logs
sudo journalctl -u gunicorn -f

# Force restart
sudo systemctl stop gunicorn
sudo systemctl start gunicorn
```

## 📁 File structure sau khi deploy

```
/var/www/mip/
├── mip_system.py                    # ✅ Updated code
├── fix_server_password_column.py   # 🔧 Migration script
├── database.db                     # 📊 Database (if SQLite)
└── ...other files
```

## 🎉 Success indicators

Khi mọi thứ hoạt động đúng:

1. **Registration page**: Có thể đăng ký user mới
2. **Success redirect**: Sau đăng ký redirect về login với username pre-filled
3. **Alert colors**: Success message màu xanh, error message màu đỏ
4. **Username normalization**: `User.Name-123` → `testuser123`
5. **Unit code clean**: `SAPMMOtestuser123XXXX` (no special chars)
6. **Login works**: Có thể login với username đã normalized

## 📞 Support

Nếu gặp vấn đề:
1. Kiểm tra logs: `sudo journalctl -u gunicorn -f`
2. Kiểm tra database columns
3. Verify file permissions
4. Restart services

---

**🔥 Important Notes:**
- Backup database trước khi chạy migration
- Test trên staging environment trước
- Migration script có built-in rollback protection
- Code đã được backup tự động với timestamp
