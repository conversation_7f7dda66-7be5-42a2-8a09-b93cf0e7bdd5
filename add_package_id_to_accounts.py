#!/usr/bin/env python3
"""
Add package_id column to Accounts table for marketplace functionality
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_package_id_to_accounts():
    """Add package_id column to Accounts table"""
    
    print("🚀 Adding package_id column to Accounts table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current Accounts table structure
        print("\n🔧 Step 1: Checking current Accounts table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Current columns count: {len(existing_column_names)}")
        
        # Check if package_id already exists
        if 'package_id' in existing_column_names:
            print("✅ package_id column already exists")
            return True
        
        # Step 2: Add package_id column
        print("\n🔧 Step 2: Adding package_id column...")
        
        cursor.execute('''
            ALTER TABLE "Accounts" 
            ADD COLUMN package_id int4;
        ''')
        
        print("  ✅ Added package_id column (int4)")
        
        # Step 3: Add comment
        print("\n🔧 Step 3: Adding column comment...")
        
        cursor.execute('''
            COMMENT ON COLUMN "Accounts"."package_id" IS 'ID của package marketplace mà account này thuộc về';
        ''')
        
        print("  ✅ Added comment for package_id column")
        
        # Step 4: Create index for performance
        print("\n🔧 Step 4: Creating index...")
        
        try:
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_accounts_package_id 
                ON "Accounts"(package_id) 
                WHERE package_id IS NOT NULL;
            ''')
            print("  ✅ Created index: idx_accounts_package_id")
        except Exception as e:
            print(f"  ⚠️  Index creation: {e}")
        
        # Step 5: Add foreign key if Products table exists
        print("\n🔧 Step 5: Adding foreign key constraint...")
        
        # Check if Products table exists
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Products'
            );
        ''')
        products_exists = cursor.fetchone()[0]
        
        if products_exists:
            try:
                cursor.execute('''
                    ALTER TABLE "Accounts" ADD CONSTRAINT "Accounts_package_id_fkey" 
                    FOREIGN KEY ("package_id") REFERENCES "Products" ("product_id") ON DELETE SET NULL ON UPDATE NO ACTION;
                ''')
                print("  ✅ Added foreign key to Products table")
            except Exception as e:
                print(f"  ⚠️  Foreign key creation: {e}")
        else:
            print("  ⚠️  Products table not found, skipping foreign key")
        
        # Step 6: Verify final structure
        print("\n🔧 Step 6: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            AND column_name IN ('package_id', 'marketplace_status', 'is_sold')
            ORDER BY column_name;
        ''')
        
        marketplace_columns = cursor.fetchall()
        print(f"📋 Marketplace-related columns:")
        for col in marketplace_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 7: Test UPDATE query that was failing
        print("\n🔧 Step 7: Testing UPDATE query...")
        
        try:
            # Test the problematic UPDATE query structure (without actually updating)
            cursor.execute('''
                SELECT account_id FROM "Accounts" 
                WHERE marketplace_status IS NOT NULL OR package_id IS NOT NULL 
                LIMIT 1;
            ''')
            
            print("  ✅ UPDATE query structure test successful")
            
        except Exception as e:
            print(f"  ❌ UPDATE query test failed: {e}")
        
        # Step 8: Show sample UPDATE that should now work
        print("\n🔧 Step 8: Sample UPDATE query that should work...")
        
        sample_query = '''
            UPDATE "Accounts" 
            SET marketplace_status = 'reserved', package_id = %s 
            WHERE account_id = %s;
        '''
        
        print(f"📝 Sample query:")
        print(f"    {sample_query}")
        print("  ✅ This query should now work without column errors")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 package_id column added successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test product creation with account type")
        print("   3. Test account assignment to packages")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_package_id_to_accounts()
    sys.exit(0 if success else 1)
