#!/usr/bin/env python3
"""
Simple marketplace migration - fix schema and add basic data
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def simple_migration():
    """Simple marketplace migration"""
    
    print("🚀 Starting simple marketplace migration...")
    print("=" * 50)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Fix Products table schema
        print("\n🔧 Step 1: Fixing Products table schema...")
        
        schema_fixes = [
            'ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT \'active\';',
            'ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN DEFAULT FALSE;',
            'ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS deleted_at TIMESTAMP NULL;',
            'ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS deleted_by INTEGER;',
            'ALTER TABLE "Products" ADD COLUMN IF NOT EXISTS product_type_id INTEGER;',
        ]
        
        for fix in schema_fixes:
            try:
                cursor.execute(fix)
                column_name = fix.split('ADD COLUMN')[1].split()[2] if 'ADD COLUMN' in fix else 'Schema'
                print(f"  ✅ Added column: {column_name}")
            except Exception as e:
                print(f"  ⚠️  {fix}: {e}")
        
        # Update existing products
        cursor.execute('UPDATE "Products" SET status = \'active\' WHERE status IS NULL OR status = \'\';')
        print(f"  ✅ Updated existing products to active status")
        
        # Step 2: Create ProductTypes table
        print("\n🔧 Step 2: Creating ProductTypes table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "ProductTypes" (
                type_id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                icon VARCHAR(50),
                is_default BOOLEAN DEFAULT FALSE,
                is_active BOOLEAN DEFAULT TRUE,
                metadata JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        print("  ✅ ProductTypes table created")
        
        # Insert default product types (simple approach)
        default_types = [
            ('Account', 'Tài khoản TikTok', 'cil-user', True),
            ('Win Product', 'Sản phẩm win', 'cil-star', True),
            ('Course', 'Khóa học', 'cil-book', True),
            ('AFF Package', 'Gói AFF', 'cil-chart-line', True),
        ]
        
        for name, desc, icon, is_default in default_types:
            cursor.execute('SELECT type_id FROM "ProductTypes" WHERE name = %s', (name,))
            if not cursor.fetchone():
                cursor.execute('''
                    INSERT INTO "ProductTypes" (name, description, icon, is_default)
                    VALUES (%s, %s, %s, %s)
                ''', (name, desc, icon, is_default))
                print(f"  ✅ Added product type: {name}")
        
        # Step 3: Create CategoryProductTypes table
        print("\n🔧 Step 3: Creating CategoryProductTypes table...")
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "CategoryProductTypes" (
                id SERIAL PRIMARY KEY,
                category_id INTEGER,
                type_id INTEGER,
                is_primary BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        print("  ✅ CategoryProductTypes table created")
        
        # Step 4: Add default categories (simple approach)
        print("\n🔧 Step 4: Adding default categories...")
        
        default_categories = [
            ('Account Packages', 'Gói tài khoản TikTok', 'cil-user', 1),
            ('Win Products', 'Sản phẩm win', 'cil-star', 2),
            ('Courses', 'Khóa học', 'cil-book', 3),
            ('AFF Packages', 'Gói AFF', 'cil-chart-line', 4),
        ]
        
        for name, desc, icon, sort_order in default_categories:
            cursor.execute('SELECT category_id FROM "ProductCategories" WHERE name = %s', (name,))
            if not cursor.fetchone():
                cursor.execute('''
                    INSERT INTO "ProductCategories" (name, description, icon, sort_order)
                    VALUES (%s, %s, %s, %s)
                ''', (name, desc, icon, sort_order))
                print(f"  ✅ Added category: {name}")
        
        # Step 5: Add sample products (chỉ Account packages, không thêm AFF vì đã có data)
        print("\n🔧 Step 5: Adding sample Account products...")

        # Get category IDs
        cursor.execute('SELECT category_id FROM "ProductCategories" WHERE name = %s', ('Account Packages',))
        account_cat = cursor.fetchone()

        if account_cat:
            account_cat_id = account_cat[0]

            sample_products = [
                (account_cat_id, 'Gói Account TikTok Starter', 'Gói 10 tài khoản TikTok cơ bản với follower từ 1K-5K', 500000, 'account'),
                (account_cat_id, 'Gói Account TikTok Premium', 'Gói 5 tài khoản TikTok chất lượng cao với follower từ 10K-50K', 2000000, 'account'),
            ]

            for cat_id, name, desc, price, prod_type in sample_products:
                cursor.execute('SELECT product_id FROM "Products" WHERE name = %s', (name,))
                if not cursor.fetchone():
                    cursor.execute('''
                        INSERT INTO "Products" (category_id, name, description, price, product_type, status, is_active)
                        VALUES (%s, %s, %s, %s, %s, 'active', true)
                    ''', (cat_id, name, desc, price, prod_type))
                    print(f"  ✅ Added product: {name}")
        else:
            print("  ⚠️  Account Packages category not found, skipping sample products")
        
        # Step 6: Create basic indexes
        print("\n🔧 Step 6: Creating indexes...")
        
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_products_category ON "Products"(category_id);',
            'CREATE INDEX IF NOT EXISTS idx_products_status ON "Products"(status);',
            'CREATE INDEX IF NOT EXISTS idx_products_active ON "Products"(is_active);',
        ]
        
        for index in indexes:
            try:
                cursor.execute(index)
                print(f"  ✅ Created index")
            except Exception as e:
                print(f"  ⚠️  Index: {e}")
        
        # Commit all changes
        conn.commit()
        
        # Step 7: Verify
        print("\n🔍 Verification...")
        
        cursor.execute('SELECT COUNT(*) FROM "ProductCategories";')
        cat_count = cursor.fetchone()[0]
        print(f"  📊 Categories: {cat_count}")
        
        cursor.execute('SELECT COUNT(*) FROM "ProductTypes";')
        type_count = cursor.fetchone()[0]
        print(f"  📊 Product Types: {type_count}")
        
        cursor.execute('SELECT COUNT(*) FROM "Products";')
        prod_count = cursor.fetchone()[0]
        print(f"  📊 Products: {prod_count}")
        
        # Test the problematic query
        print("\n🧪 Testing categories API query...")
        cursor.execute('''
            SELECT c.category_id, c.name, c.description, c.icon, c.sort_order,
                   c.is_active, c.created_at,
                   (SELECT COUNT(*) FROM "Products" WHERE category_id = c.category_id AND (is_deleted = FALSE OR is_deleted IS NULL)) as product_count
            FROM "ProductCategories" c
            ORDER BY c.sort_order, c.name
            LIMIT 3;
        ''')
        
        test_results = cursor.fetchall()
        print(f"  ✅ API query test successful - {len(test_results)} categories")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Simple marketplace migration completed!")
        print("📝 Next steps:")
        print("   1. Restart: sudo systemctl restart sapmmo")
        print("   2. Test: /admin/marketplace/categories")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = simple_migration()
    sys.exit(0 if success else 1)
