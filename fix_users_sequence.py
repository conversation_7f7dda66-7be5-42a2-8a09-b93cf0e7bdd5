#!/usr/bin/env python3
"""
Fix Users table sequence for auto-increment user_id
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_users_sequence():
    """Fix Users table sequence"""
    
    print("🚀 Fixing Users table sequence...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current Users table structure
        print("\n🔧 Step 1: Checking current Users table...")
        
        cursor.execute('SELECT COUNT(*), MAX(user_id) FROM "Users";')
        count, max_id = cursor.fetchone()
        print(f"📊 Users count: {count}, Max user_id: {max_id}")
        
        # Step 2: Find sequence name
        print("\n🔧 Step 2: Finding sequence name...")
        
        cursor.execute('''
            SELECT pg_get_serial_sequence('"Users"', 'user_id');
        ''')
        sequence_name = cursor.fetchone()[0]
        print(f"📋 Sequence name: {sequence_name}")
        
        if not sequence_name:
            print("❌ No sequence found, creating new one...")
            
            # Create new sequence
            cursor.execute('''
                CREATE SEQUENCE IF NOT EXISTS "Users_user_id_seq"
                INCREMENT 1
                MINVALUE 1
                MAXVALUE 2147483647
                START 1
                CACHE 1;
            ''')
            
            # Set sequence ownership
            cursor.execute('''
                ALTER SEQUENCE "Users_user_id_seq" OWNED BY "Users".user_id;
            ''')
            
            # Set default value
            cursor.execute('''
                ALTER TABLE "Users" ALTER COLUMN user_id SET DEFAULT nextval('"Users_user_id_seq"'::regclass);
            ''')
            
            sequence_name = '"Users_user_id_seq"'
            print(f"✅ Created new sequence: {sequence_name}")
        
        # Step 3: Check current sequence value
        print("\n🔧 Step 3: Checking current sequence value...")
        
        cursor.execute(f'SELECT last_value, is_called FROM {sequence_name};')
        last_value, is_called = cursor.fetchone()
        print(f"📊 Sequence last_value: {last_value}, is_called: {is_called}")
        
        # Step 4: Fix sequence value
        print("\n🔧 Step 4: Fixing sequence value...")
        
        if max_id is None:
            max_id = 0
        
        new_value = max_id + 1
        cursor.execute(f"SELECT setval('{sequence_name}', {new_value}, false);")
        print(f"✅ Set sequence to {new_value}")
        
        # Step 5: Verify sequence fix
        print("\n🔧 Step 5: Verifying sequence fix...")
        
        cursor.execute(f'SELECT last_value, is_called FROM {sequence_name};')
        last_value, is_called = cursor.fetchone()
        print(f"📊 New sequence last_value: {last_value}, is_called: {is_called}")
        
        # Step 6: Test sequence
        print("\n🔧 Step 6: Testing sequence...")
        
        cursor.execute(f"SELECT nextval('{sequence_name}');")
        next_id = cursor.fetchone()[0]
        print(f"🔍 Next ID from sequence: {next_id}")
        
        # Reset sequence back (we just tested)
        cursor.execute(f"SELECT setval('{sequence_name}', {new_value}, false);")
        
        # Step 7: Check table constraints
        print("\n🔧 Step 7: Checking table constraints...")
        
        cursor.execute('''
            SELECT column_name, column_default, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND column_name = 'user_id';
        ''')
        
        col_info = cursor.fetchone()
        if col_info:
            print(f"📋 user_id column: default={col_info[1]}, nullable={col_info[2]}")
        
        # Step 8: Test INSERT without specifying user_id
        print("\n🔧 Step 8: Testing INSERT without user_id...")
        
        try:
            # Test insert (but rollback)
            cursor.execute('SAVEPOINT test_insert;')
            
            cursor.execute('''
                INSERT INTO "Users" (username, password_hash, role, unit_code, phone, email)
                VALUES ('test_user_seq', 'test_hash', 'member', 'TEST001', '+84123456789', '<EMAIL>')
                RETURNING user_id;
            ''')
            
            test_user_id = cursor.fetchone()[0]
            print(f"✅ Test INSERT successful, got user_id: {test_user_id}")
            
            # Rollback test
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
            print("✅ Test INSERT rolled back")
            
        except Exception as e:
            print(f"❌ Test INSERT failed: {e}")
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
        
        # Step 9: Show table structure
        print("\n🔧 Step 9: Final table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, column_default, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' 
            ORDER BY ordinal_position;
        ''')
        
        columns = cursor.fetchall()
        print("📋 Users table columns:")
        for col in columns:
            nullable = "NULL" if col[3] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[2]}" if col[2] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Users sequence fixed successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test user registration")
        print("   3. Test admin create user")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_users_sequence()
    sys.exit(0 if success else 1)
