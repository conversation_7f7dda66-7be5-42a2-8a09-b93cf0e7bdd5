#!/usr/bin/env python3
"""
Update server code to use 'password' column instead of 'password_hash'
"""

import re
import os
import shutil
from datetime import datetime

def backup_file(file_path):
    """Backup file tr<PERSON><PERSON><PERSON> khi sửa"""
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"📁 Backup created: {backup_path}")
    return backup_path

def find_mip_system_file():
    """Tìm file mip_system.py"""
    possible_paths = [
        "mip_system.py",
        "/var/www/sapmmo/mip_system.py",
        "/var/www/mip/mip_system.py",
        "./mip_system.py"
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            print(f"✅ Found mip_system.py at: {path}")
            return path
    
    print("❌ mip_system.py not found in common locations")
    print("📋 Searched paths:")
    for path in possible_paths:
        print(f"  - {path}")
    return None

def update_mip_system_file(file_path):
    """Update file mip_system.py"""
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    print(f"🔧 Updating {file_path}...")
    
    # Backup file
    backup_path = backup_file(file_path)
    
    try:
        # Read file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Track changes
        changes_made = 0
        original_content = content
        
        # 1. Fix route /register - INSERT statement
        pattern = r'INSERT INTO "Users" \(username, password_hash, role,'
        if re.search(pattern, content):
            content = re.sub(pattern, 'INSERT INTO "Users" (username, password, role,', content)
            changes_made += 1
            print("✅ Fixed /register INSERT statement")
        
        # 2. Fix route /add_user - INSERT statements
        pattern = r'INSERT INTO "Users" \(username, password_hash, role, team_id, unit_code'
        if re.search(pattern, content):
            content = re.sub(pattern, 'INSERT INTO "Users" (username, password, role, team_id, unit_code', content)
            changes_made += 1
            print("✅ Fixed /add_user INSERT statement")
        
        # 3. Fix route /add_user - INSERT with phone
        pattern = r'INSERT INTO "Users" \(username, password_hash, role, team_id, unit_code, phone\)'
        if re.search(pattern, content):
            content = re.sub(pattern, 'INSERT INTO "Users" (username, password, role, team_id, unit_code, phone)', content)
            changes_made += 1
            print("✅ Fixed /add_user INSERT with phone statement")
        
        # 4. Fix route /edit_user - UPDATE statements
        pattern = r'UPDATE "Users" SET username=%s, password_hash=%s, role=%s'
        if re.search(pattern, content):
            content = re.sub(pattern, 'UPDATE "Users" SET username=%s, password=%s, role=%s', content)
            changes_made += 1
            print("✅ Fixed /edit_user UPDATE statement")
        
        # 5. Fix route /api/auth/login - SELECT statement
        pattern = r'SELECT user_id, username, password_hash, role, team_id, unit_code'
        if re.search(pattern, content):
            content = re.sub(pattern, 'SELECT user_id, username, password, role, team_id, unit_code', content)
            changes_made += 1
            print("✅ Fixed /api/auth/login SELECT statement")
        
        # 6. Fix any remaining password_hash in SQL contexts
        # More specific patterns to avoid changing function names
        sql_patterns = [
            (r'(\bINSERT\s+INTO\s+"Users"\s*\([^)]*?)password_hash', r'\1password'),
            (r'(\bUPDATE\s+"Users"\s+SET\s+[^W]*?)password_hash(\s*=)', r'\1password\2'),
            (r'(\bSELECT\s+[^F]*?)password_hash(\s*[,\s])', r'\1password\2')
        ]
        
        for pattern, replacement in sql_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                content = re.sub(pattern, replacement, content, flags=re.IGNORECASE)
                changes_made += 1
                print("✅ Fixed additional SQL password_hash reference")
        
        # Write updated content
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Updated {file_path} with {changes_made} changes")
            return True
        else:
            print("ℹ️  No changes needed in mip_system.py")
            # Remove backup if no changes
            os.remove(backup_path)
            return True
            
    except Exception as e:
        print(f"❌ Error updating {file_path}: {e}")
        # Restore backup
        if os.path.exists(backup_path):
            shutil.copy2(backup_path, file_path)
            print(f"🔄 Restored from backup")
        return False

def verify_changes(file_path):
    """Verify changes were applied correctly"""
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    print(f"\n🔍 Verifying changes in {file_path}...")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for remaining password_hash references in SQL
        sql_password_hash_pattern = r'(INSERT|UPDATE|SELECT)[^;]*password_hash'
        sql_matches = re.findall(sql_password_hash_pattern, content, re.IGNORECASE)
        
        if sql_matches:
            print(f"⚠️  Found {len(sql_matches)} remaining SQL 'password_hash' references")
            print("🔧 These need manual review:")
            
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if re.search(sql_password_hash_pattern, line, re.IGNORECASE):
                    print(f"   Line {i+1}: {line.strip()}")
        else:
            print("✅ No SQL 'password_hash' references found")
        
        # Check for password column usage in SQL
        sql_password_pattern = r'(INSERT|UPDATE|SELECT)[^;]*\bpassword\b(?!_hash)'
        password_matches = re.findall(sql_password_pattern, content, re.IGNORECASE)
        print(f"✅ Found {len(password_matches)} SQL 'password' column references")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying changes: {e}")
        return False

def test_syntax(file_path):
    """Test Python syntax"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        compile(content, file_path, 'exec')
        print("✅ Python syntax is valid")
        return True
        
    except SyntaxError as e:
        print(f"❌ Python syntax error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing syntax: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Updating server code to use 'password' column")
    print("🎯 Goal: Make code consistent with database schema")
    print("=" * 60)
    
    # Find mip_system.py
    file_path = find_mip_system_file()
    if not file_path:
        print("\n❌ Cannot find mip_system.py")
        print("Please run this script from the correct directory")
        return
    
    # Update mip_system.py
    success = update_mip_system_file(file_path)
    
    if success:
        # Verify changes
        verify_success = verify_changes(file_path)
        
        # Test syntax
        syntax_ok = test_syntax(file_path)
        
        if verify_success and syntax_ok:
            print("\n✅ Code update completed successfully!")
            
            print("\n📋 Summary:")
            print("✅ Database migration: password_hash → password")
            print("✅ Code updated: SQL statements fixed")
            print("✅ Syntax validated: No errors")
            
            print("\n🚀 Next steps:")
            print("1. Restart gunicorn: sudo systemctl restart gunicorn")
            print("2. Test registration: /register")
            print("3. Test login: /login")
            print("4. Check logs: sudo journalctl -u gunicorn -f")
            
        else:
            print("\n⚠️  Code updated but verification had issues")
    else:
        print("\n❌ Code update failed!")
    
    print("\n" + "=" * 60)
    print("📁 Backup files created for safety")
    print("🔄 You can restore from backup if needed")

if __name__ == "__main__":
    main()
