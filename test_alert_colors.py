#!/usr/bin/env python3
"""
Test alert colors in login page
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_success_alert():
    """Test success alert styling"""
    
    print("🧪 Testing success alert styling...")
    
    try:
        from mip_system import app
        
        with app.test_client() as client:
            # Test success message
            test_username = "testuser123"
            success_msg = "Đăng ký thành công! Vui lòng đăng nhập."
            
            response = client.get(f'/login?username={test_username}&success={success_msg}')
            
            if response.status_code == 200:
                html = response.get_data(as_text=True)
                
                print("🔍 Checking success alert elements:")
                
                # Check for alert-success class
                if 'alert alert-success' in html:
                    print("✅ Success alert has correct classes: 'alert alert-success'")
                else:
                    print("❌ Success alert missing correct classes")
                    return False
                
                # Check success message content
                if success_msg in html:
                    print(f"✅ Success message present: '{success_msg}'")
                else:
                    print("❌ Success message missing")
                    return False
                
                # Check CSS for success styling
                if 'alert-success' in html and 'rgba(25, 135, 84, 0.1)' in html:
                    print("✅ Success alert has green background CSS")
                else:
                    print("⚠️  Success alert CSS might not be applied (check manually)")
                
                # Show relevant HTML snippet
                print("\n📝 Success alert HTML:")
                lines = html.split('\n')
                for i, line in enumerate(lines):
                    if 'alert-success' in line:
                        for j in range(max(0, i-1), min(len(lines), i+4)):
                            prefix = ">>> " if j == i else "    "
                            print(f"{prefix}{lines[j].strip()}")
                        break
                
                return True
            else:
                print(f"❌ Failed to get login page: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_error_alert():
    """Test error alert styling"""
    
    print("\n🧪 Testing error alert styling...")
    
    try:
        from mip_system import app
        
        with app.test_client() as client:
            # Test with invalid login to trigger error
            response = client.post('/login', data={
                'username': 'nonexistent',
                'password': 'wrongpass'
            })
            
            if response.status_code == 200:
                html = response.get_data(as_text=True)
                
                print("🔍 Checking error alert elements:")
                
                # Check for alert-danger class
                if 'alert alert-danger' in html:
                    print("✅ Error alert has correct classes: 'alert alert-danger'")
                else:
                    print("❌ Error alert missing correct classes")
                    return False
                
                # Check error message content
                if 'Invalid username or password' in html:
                    print("✅ Error message present")
                else:
                    print("❌ Error message missing")
                    return False
                
                # Show relevant HTML snippet
                print("\n📝 Error alert HTML:")
                lines = html.split('\n')
                for i, line in enumerate(lines):
                    if 'alert-danger' in line:
                        for j in range(max(0, i-1), min(len(lines), i+4)):
                            prefix = ">>> " if j == i else "    "
                            print(f"{prefix}{lines[j].strip()}")
                        break
                
                return True
            else:
                print(f"❌ Unexpected response: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_css_styles():
    """Test CSS styles in HTML"""
    
    print("\n🧪 Testing CSS styles...")
    
    try:
        from mip_system import app
        
        with app.test_client() as client:
            response = client.get('/login')
            
            if response.status_code == 200:
                html = response.get_data(as_text=True)
                
                print("🔍 Checking CSS styles:")
                
                # Check for success alert CSS
                success_css_checks = [
                    'alert-success',
                    'rgba(25, 135, 84, 0.1)',  # Green background
                    '#0f5132',  # Green text
                    '#198754'   # Green border
                ]
                
                for css in success_css_checks:
                    if css in html:
                        print(f"✅ Success CSS found: {css}")
                    else:
                        print(f"❌ Success CSS missing: {css}")
                        return False
                
                # Check for error alert CSS
                error_css_checks = [
                    'alert-danger',
                    'rgba(220, 53, 69, 0.1)',  # Red background
                    '#721c24',  # Red text
                    '#dc3545'   # Red border
                ]
                
                for css in error_css_checks:
                    if css in html:
                        print(f"✅ Error CSS found: {css}")
                    else:
                        print(f"❌ Error CSS missing: {css}")
                        return False
                
                return True
            else:
                print(f"❌ Failed to get login page: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def show_color_summary():
    """Show color summary"""
    
    print("\n🎨 Alert Color Summary:")
    print("=" * 50)
    print("✅ SUCCESS ALERT (Green):")
    print("   - Background: rgba(25, 135, 84, 0.1) - Light green")
    print("   - Text: #0f5132 - Dark green")
    print("   - Border: #198754 - Medium green")
    print("   - Class: alert alert-success")
    print()
    print("❌ ERROR ALERT (Red):")
    print("   - Background: rgba(220, 53, 69, 0.1) - Light red")
    print("   - Text: #721c24 - Dark red")
    print("   - Border: #dc3545 - Medium red")
    print("   - Class: alert alert-danger")

def main():
    """Main test function"""
    print("🎨 Testing Alert Colors")
    print("🔧 Checking success (green) and error (red) alerts")
    print("=" * 60)
    
    # Test 1: Success alert
    success1 = test_success_alert()
    
    # Test 2: Error alert
    success2 = test_error_alert()
    
    # Test 3: CSS styles
    success3 = test_css_styles()
    
    # Show color summary
    show_color_summary()
    
    print("\n" + "=" * 60)
    print("📋 Test Results:")
    print(f"  - Success alert (green): {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"  - Error alert (red): {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"  - CSS styles: {'✅ PASS' if success3 else '❌ FAIL'}")
    
    if success1 and success2 and success3:
        print("\n🎉 All tests passed! Alert colors are correct!")
        print("✅ Success messages will show in GREEN")
        print("❌ Error messages will show in RED")
    else:
        print("\n❌ Some tests failed!")

if __name__ == "__main__":
    main()
