#!/usr/bin/env python3
"""
Deploy Video System Files
Tạo package các files cần deploy lên server
"""

import os
import shutil
import json
from datetime import datetime

def create_deployment_package():
    """Tạo package deployment"""
    print("📦 Creating Video System Deployment Package")
    print("=" * 60)
    
    # Tạo thư mục deployment
    deploy_dir = "video_system_deployment"
    if os.path.exists(deploy_dir):
        shutil.rmtree(deploy_dir)
    os.makedirs(deploy_dir)
    
    print(f"✅ Created deployment directory: {deploy_dir}")
    
    # Files cần deploy
    files_to_deploy = {
        # Database migration
        "database/": [
            "deploy_video_system.py",
            "db_config.py"
        ],
        
        # Backend files
        "backend/": [
            "mip_system.py"  # Updated with video APIs
        ],
        
        # Templates
        "templates/marketplace/admin/": [
            "video_links.html"
        ],
        
        "templates/marketplace/": [
            "user_orders.html",
            "order_detail.html"
        ],
        
        # Documentation
        "docs/": [
            "VIDEO_SYSTEM_IMPLEMENTATION_REPORT.md",
            "VIDEO_UI_FINAL_IMPLEMENTATION.md",
            "REMOVE_ALL_TAB_SUMMARY.md"
        ]
    }
    
    # Copy files
    for target_dir, files in files_to_deploy.items():
        full_target_dir = os.path.join(deploy_dir, target_dir)
        os.makedirs(full_target_dir, exist_ok=True)
        
        for file in files:
            if os.path.exists(file):
                shutil.copy2(file, full_target_dir)
                print(f"   ✅ Copied: {file} → {target_dir}")
            else:
                print(f"   ⚠️ Missing: {file}")
    
    return deploy_dir

def create_deployment_instructions(deploy_dir):
    """Tạo hướng dẫn deployment"""
    instructions = """# 🚀 Video System Deployment Instructions

## 📋 Deployment Steps

### 1. Database Migration
```bash
# Upload database files to server
scp -r database/ user@server:/path/to/project/

# Run migration on server
cd /path/to/project/database/
python3 deploy_video_system.py
```

### 2. Backend Deployment
```bash
# Backup current mip_system.py
cp mip_system.py mip_system.py.backup

# Upload new backend files
scp backend/mip_system.py user@server:/path/to/project/

# Restart application server
sudo systemctl restart gunicorn
# or
sudo supervisorctl restart mip_system
```

### 3. Template Deployment
```bash
# Upload templates
scp -r templates/ user@server:/path/to/project/

# No restart needed for templates
```

### 4. Verification
```bash
# Check application logs
tail -f /var/log/mip_system/error.log

# Test endpoints
curl http://your-domain.com/admin/marketplace/video-links
curl http://your-domain.com/api/admin/marketplace/video-links
```

## 🔧 New API Endpoints

### Admin Video Links Management:
- `GET /admin/marketplace/video-links` - Admin interface
- `GET /api/admin/marketplace/video-links` - List with pagination
- `POST /api/admin/marketplace/video-links` - Create new link
- `PUT /api/admin/marketplace/video-links/<id>` - Update link
- `DELETE /api/admin/marketplace/video-links/<id>` - Delete link (with protection)
- `POST /api/admin/marketplace/video-links/bulk-delete` - Bulk delete
- `GET /api/admin/marketplace/video-types` - Get video types

### Enhanced Existing APIs:
- `POST /api/admin/marketplace/products` - Now supports video links
- `POST /api/marketplace/checkout` - Now assigns video links
- `GET /api/marketplace/orders/<id>` - Now returns video links
- `GET /api/marketplace/user/video-orders` - Video orders only

## 🎯 Features Deployed

### ✅ Admin Features:
- Video links management with table format
- Pagination (20/50/100 per page)
- Bulk selection and delete
- Search and filtering
- Create/edit video links
- Delete protection for sold links

### ✅ Product Creation:
- Video product type support
- Video links selection interface
- Auto stock calculation
- Product-video links mapping

### ✅ Purchase Flow:
- Video product checkout
- Auto video links assignment
- Stock management
- User video links tracking

### ✅ User Interface:
- Order detail with video links table
- Google Drive access buttons
- Video links information display
- Responsive design

## 🛡️ Security Features

### Delete Protection:
- Cannot delete sold video links
- Cannot delete links used in products
- Bulk delete with error handling
- Clear error messages

### Data Integrity:
- Foreign key constraints
- Unique constraints
- Proper indexes
- Transaction safety

## 📊 Database Schema

### New Tables:
1. **VideoLinks** - Store video link information
2. **ProductVideoLinks** - Map products to video links
3. **UserVideoLinks** - Track user purchases

### Indexes:
- Performance optimized queries
- Fast filtering and searching
- Efficient pagination

## 🧪 Testing

### Test Admin Interface:
1. Go to `/admin/marketplace/video-links`
2. Create new video links
3. Test pagination and filtering
4. Test bulk operations

### Test Product Creation:
1. Create product with type "Video"
2. Select video links
3. Verify stock calculation

### Test Purchase Flow:
1. User purchases video product
2. Check order detail shows video links
3. Verify Google Drive access

## 🔍 Troubleshooting

### Common Issues:
1. **Database connection**: Check db_config.py
2. **Missing tables**: Run deploy_video_system.py
3. **API errors**: Check server logs
4. **Template errors**: Verify file permissions

### Rollback Plan:
1. Restore mip_system.py.backup
2. Drop video tables if needed
3. Restart application server

## 📞 Support

If you encounter issues:
1. Check deployment logs
2. Verify database schema
3. Test API endpoints manually
4. Check browser console for frontend errors
"""

    instructions_file = os.path.join(deploy_dir, "DEPLOYMENT_INSTRUCTIONS.md")
    with open(instructions_file, 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print(f"✅ Created deployment instructions: {instructions_file}")

def create_deployment_manifest(deploy_dir):
    """Tạo manifest file"""
    manifest = {
        "deployment_info": {
            "system": "Video System",
            "version": "1.0.0",
            "created_at": datetime.now().isoformat(),
            "description": "Complete video links management system with admin interface, product integration, and user purchase flow"
        },
        "database_changes": {
            "new_tables": [
                "VideoLinks",
                "ProductVideoLinks", 
                "UserVideoLinks"
            ],
            "new_indexes": [
                "idx_videolinks_status",
                "idx_videolinks_video_type",
                "idx_videolinks_created_at",
                "idx_videolinks_name",
                "idx_productvideolinks_product_id",
                "idx_productvideolinks_link_id",
                "idx_uservideolinks_user_id",
                "idx_uservideolinks_order_id",
                "idx_uservideolinks_link_id",
                "idx_uservideolinks_purchased_at"
            ],
            "new_product_types": [
                "videos"
            ]
        },
        "api_endpoints": {
            "new_endpoints": [
                "GET /admin/marketplace/video-links",
                "GET /api/admin/marketplace/video-links",
                "POST /api/admin/marketplace/video-links",
                "PUT /api/admin/marketplace/video-links/<id>",
                "DELETE /api/admin/marketplace/video-links/<id>",
                "POST /api/admin/marketplace/video-links/bulk-delete",
                "GET /api/admin/marketplace/video-types",
                "GET /api/marketplace/user/video-orders"
            ],
            "modified_endpoints": [
                "POST /api/admin/marketplace/products",
                "POST /api/marketplace/checkout",
                "GET /api/marketplace/orders/<id>"
            ]
        },
        "files_modified": [
            "mip_system.py",
            "templates/marketplace/admin/video_links.html",
            "templates/marketplace/user_orders.html",
            "templates/marketplace/order_detail.html"
        ],
        "features": [
            "Video links management with pagination",
            "Bulk operations with delete protection",
            "Video product type support",
            "Purchase flow with auto assignment",
            "User interface with video links display",
            "Responsive table design",
            "Search and filtering",
            "Performance optimized queries"
        ]
    }
    
    manifest_file = os.path.join(deploy_dir, "deployment_manifest.json")
    with open(manifest_file, 'w', encoding='utf-8') as f:
        json.dump(manifest, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Created deployment manifest: {manifest_file}")

def main():
    """Main function"""
    # Create deployment package
    deploy_dir = create_deployment_package()
    
    # Create instructions
    create_deployment_instructions(deploy_dir)
    
    # Create manifest
    create_deployment_manifest(deploy_dir)
    
    print(f"\n🎉 Deployment package created successfully!")
    print(f"📁 Package location: {deploy_dir}/")
    print(f"\n📋 Package contents:")
    
    # List package contents
    for root, dirs, files in os.walk(deploy_dir):
        level = root.replace(deploy_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            print(f"{subindent}{file}")
    
    print(f"\n🚀 Ready for deployment!")
    print(f"📖 Read DEPLOYMENT_INSTRUCTIONS.md for detailed steps")

if __name__ == "__main__":
    main()
