#!/bin/bash

# =============================================================================
# MARKETPLACE DEPLOYMENT SCRIPT
# Script để update database và tạo folders cho tính năng marketplace
# =============================================================================

set -e  # Exit on any error

echo "🚀 Starting Marketplace Deployment Update..."
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Database configuration - Read from db_config.py
APP_DIR="/var/www/sapmmo"
DB_CONFIG_FILE="$APP_DIR/db_config.py"

if [ ! -f "$DB_CONFIG_FILE" ]; then
    echo -e "${RED}❌ Database config file not found: $DB_CONFIG_FILE${NC}"
    echo "Please ensure db_config.py exists in the application directory"
    exit 1
fi

# Extract database config from Python file
DB_HOST=$(python3 -c "import sys; sys.path.append('$APP_DIR'); from db_config import PG_CONFIG; print(PG_CONFIG['host'])")
DB_NAME=$(python3 -c "import sys; sys.path.append('$APP_DIR'); from db_config import PG_CONFIG; print(PG_CONFIG['database'])")
DB_USER=$(python3 -c "import sys; sys.path.append('$APP_DIR'); from db_config import PG_CONFIG; print(PG_CONFIG['user'])")
DB_PASSWORD=$(python3 -c "import sys; sys.path.append('$APP_DIR'); from db_config import PG_CONFIG; print(PG_CONFIG['password'])")

STATIC_DIR="$APP_DIR/static"
UPLOADS_DIR="$STATIC_DIR/uploads"

echo -e "${BLUE}📋 Configuration:${NC}"
echo "   Database: $DB_NAME@$DB_HOST"
echo "   App Directory: $APP_DIR"
echo "   Static Directory: $STATIC_DIR"
echo ""

# =============================================================================
# 1. CREATE REQUIRED DIRECTORIES
# =============================================================================

echo -e "${YELLOW}📁 Creating required directories...${NC}"

# Create uploads directories
mkdir -p "$UPLOADS_DIR/coffee-icons"
mkdir -p "$UPLOADS_DIR/products"
mkdir -p "$UPLOADS_DIR/categories"

# Set proper permissions
chown -R www-data:www-data "$UPLOADS_DIR"
chmod -R 755 "$UPLOADS_DIR"

echo -e "${GREEN}✅ Directories created successfully${NC}"
echo "   - $UPLOADS_DIR/coffee-icons"
echo "   - $UPLOADS_DIR/products"
echo "   - $UPLOADS_DIR/categories"
echo ""

# =============================================================================
# 2. DATABASE MIGRATIONS
# =============================================================================

echo -e "${YELLOW}🗄️  Running database migrations...${NC}"

# Function to run SQL command
run_sql() {
    local sql="$1"
    local description="$2"
    
    echo -e "${BLUE}   Running: $description${NC}"
    
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "$sql" > /dev/null 2>&1; then
        echo -e "${GREEN}   ✅ Success: $description${NC}"
    else
        echo -e "${RED}   ❌ Failed: $description${NC}"
        echo -e "${YELLOW}   ⚠️  This might be expected if already exists${NC}"
    fi
}

# 2.1 Create ProductCategories table
run_sql "
CREATE TABLE IF NOT EXISTS \"ProductCategories\" (
    category_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);" "Create ProductCategories table"

# 2.2 Create Products table (full marketplace schema)
run_sql "
CREATE TABLE IF NOT EXISTS \"Products\" (
    product_id SERIAL PRIMARY KEY,
    category_id INTEGER REFERENCES \"ProductCategories\"(category_id),
    name VARCHAR(200) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    price DECIMAL(15,2) NOT NULL,
    payment_type VARCHAR(20) DEFAULT 'one_time',
    subscription_duration_months INTEGER DEFAULT 1,
    subscription_discount_yearly DECIMAL(5,2) DEFAULT 0,
    stock INTEGER DEFAULT 0,
    unlimited_stock BOOLEAN DEFAULT FALSE,
    product_type VARCHAR(30) NOT NULL,
    metadata JSONB,
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);" "Create Products table"

# 2.3 Create ProductFiles table
run_sql "
CREATE TABLE IF NOT EXISTS \"ProductFiles\" (
    file_id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES \"Products\"(product_id),
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50),
    file_size BIGINT,
    is_primary BOOLEAN DEFAULT FALSE,
    download_count INTEGER DEFAULT 0,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);" "Create ProductFiles table"

# 2.4 Create AccountPackages table
run_sql "
CREATE TABLE IF NOT EXISTS \"AccountPackages\" (
    package_id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES \"Products\"(product_id),
    package_name VARCHAR(200) NOT NULL,
    criteria TEXT,
    warranty_days INTEGER DEFAULT 3,
    assigned_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);" "Create AccountPackages table"

# 2.5 Create PackageAccounts table
run_sql "
CREATE TABLE IF NOT EXISTS \"PackageAccounts\" (
    id SERIAL PRIMARY KEY,
    package_id INTEGER REFERENCES \"AccountPackages\"(package_id),
    account_id INTEGER REFERENCES \"Accounts\"(account_id),
    is_sold BOOLEAN DEFAULT FALSE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sold_at TIMESTAMP,
    order_item_id INTEGER
);" "Create PackageAccounts table"

# 2.6 Create Orders table (full schema)
run_sql "
CREATE TABLE IF NOT EXISTS \"Orders\" (
    order_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES \"Users\"(user_id),
    order_number VARCHAR(50) UNIQUE NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    discount_code VARCHAR(50),
    final_amount DECIMAL(15,2) NOT NULL,
    mp_amount INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    order_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,
    cancelled_at TIMESTAMP
);" "Create Orders table"

# 2.7 Create OrderItems table (full schema)
run_sql "
CREATE TABLE IF NOT EXISTS \"OrderItems\" (
    item_id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES \"Orders\"(order_id),
    product_id INTEGER REFERENCES \"Products\"(product_id),
    quantity INTEGER DEFAULT 1,
    unit_price DECIMAL(15,2) NOT NULL,
    total_price DECIMAL(15,2) NOT NULL,
    item_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);" "Create OrderItems table"

# 2.8 Create AFFPackages table (full schema)
run_sql "
CREATE TABLE IF NOT EXISTS \"AFFPackages\" (
    package_id SERIAL PRIMARY KEY,
    package_name VARCHAR(100) NOT NULL,
    description TEXT,
    account_limit INTEGER NOT NULL DEFAULT 10,
    monthly_price INTEGER NOT NULL DEFAULT 300000,
    yearly_discount_percent INTEGER NOT NULL DEFAULT 20,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE
);" "Create AFFPackages table"

# 2.9 Create UserSubscriptions table (full schema)
run_sql "
CREATE TABLE IF NOT EXISTS \"UserSubscriptions\" (
    subscription_id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    package_id INTEGER,
    product_id INTEGER,
    subscription_type VARCHAR(20),
    start_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_date TIMESTAMP,
    original_price INTEGER,
    paid_price INTEGER,
    status VARCHAR(50) DEFAULT 'active',
    account_limit INTEGER DEFAULT 0,
    auto_renew BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);" "Create UserSubscriptions table"

# 2.10 Create additional marketplace tables
run_sql "
CREATE TABLE IF NOT EXISTS \"ProductTypes\" (
    type_id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(50) DEFAULT 'cil-tag',
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);" "Create ProductTypes table"

# 2.11 Create WarrantyRequests table
run_sql "
CREATE TABLE IF NOT EXISTS \"WarrantyRequests\" (
    warranty_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES \"Users\"(user_id),
    order_item_id INTEGER REFERENCES \"OrderItems\"(item_id),
    account_id INTEGER REFERENCES \"Accounts\"(account_id),
    reason VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending',
    replacement_account_id INTEGER REFERENCES \"Accounts\"(account_id),
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP
);" "Create WarrantyRequests table"

# 2.12 Create MarketplaceTransactions table
run_sql "
CREATE TABLE IF NOT EXISTS \"MarketplaceTransactions\" (
    transaction_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES \"Users\"(user_id),
    order_id INTEGER REFERENCES \"Orders\"(order_id),
    mp_amount INTEGER NOT NULL,
    transaction_type VARCHAR(20) DEFAULT 'PURCHASE',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);" "Create MarketplaceTransactions table"

# 2.13 Add revenue_enabled column to Accounts table
run_sql "
ALTER TABLE \"Accounts\"
ADD COLUMN IF NOT EXISTS revenue_enabled BOOLEAN DEFAULT FALSE;" "Add revenue_enabled column to Accounts"

# 2.14 Create Config table if not exists
run_sql "
CREATE TABLE IF NOT EXISTS \"Config\" (
    config_id SERIAL PRIMARY KEY,
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);" "Create Config table"

# 2.15 Create performance indexes
run_sql "
CREATE INDEX IF NOT EXISTS idx_products_category ON \"Products\"(category_id);
CREATE INDEX IF NOT EXISTS idx_products_type ON \"Products\"(product_type);
CREATE INDEX IF NOT EXISTS idx_products_active ON \"Products\"(is_active);
CREATE INDEX IF NOT EXISTS idx_orders_user ON \"Orders\"(user_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON \"Orders\"(status);
CREATE INDEX IF NOT EXISTS idx_order_items_order ON \"OrderItems\"(order_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user ON \"UserSubscriptions\"(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON \"UserSubscriptions\"(status);
CREATE INDEX IF NOT EXISTS idx_warranty_user ON \"WarrantyRequests\"(user_id);
CREATE INDEX IF NOT EXISTS idx_warranty_status ON \"WarrantyRequests\"(status);
CREATE INDEX IF NOT EXISTS idx_aff_packages_active ON \"AFFPackages\"(is_active);
" "Create performance indexes"

# 2.16 Insert default coffee cup configs
run_sql "
INSERT INTO \"Config\" (config_key, config_value)
VALUES
    ('enable_coffee_display', 'false'),
    ('coffee_cup_value', '50000'),
    ('coffee_cup_icon', '☕'),
    ('coffee_icon_type', 'emoji'),
    ('coffee_icon_width', '20'),
    ('coffee_icon_height', '20')
ON CONFLICT (config_key) DO NOTHING;" "Insert default coffee cup configs"

# 2.17 Insert default product categories
run_sql "
INSERT INTO \"ProductCategories\" (name, description, icon, sort_order)
VALUES
    ('Account Packages', 'Gói tài khoản TikTok với các tiêu chí khác nhau', 'cil-user', 1),
    ('Win Products', 'Sản phẩm win đã được test và verify', 'cil-star', 2),
    ('Courses', 'Khóa học và tài liệu đào tạo', 'cil-book', 3),
    ('AFF Packages', 'Gói quản lý tài khoản AFF theo tháng', 'cil-chart-line', 4),
    ('TikTok Playbook', 'Công thức và chiến lược phát triển TikTok', 'cil-chart-line', 5)
ON CONFLICT DO NOTHING;" "Insert default product categories"

# 2.18 Insert default product types
run_sql "
INSERT INTO \"ProductTypes\" (name, description, icon, is_default, metadata)
VALUES
    ('Account', 'Gói tài khoản TikTok với các tiêu chí khác nhau', 'cil-user', TRUE, '{\"requires_accounts\": true, \"warranty_enabled\": true}'),
    ('Win Product', 'Sản phẩm win đã được test và verify', 'cil-star', FALSE, '{\"requires_files\": true, \"downloadable\": true}'),
    ('Course Basic', 'Khóa học cơ bản', 'cil-book', FALSE, '{\"course_type\": \"basic\", \"duration_weeks\": 4}'),
    ('Course Advanced', 'Khóa học nâng cao', 'cil-graduation-cap', FALSE, '{\"course_type\": \"advanced\", \"duration_weeks\": 8}'),
    ('TikTok Formula', 'Công thức nuôi kênh TikTok', 'cil-chart-line', FALSE, '{\"formula_type\": \"channel_growth\", \"includes_templates\": true}'),
    ('AI Video Formula', 'Công thức làm video AI', 'cil-video', FALSE, '{\"formula_type\": \"ai_video\", \"includes_tools\": true}'),
    ('AFF Package', 'Gói quản lý tài khoản AFF theo tháng', 'cil-chart-pie', FALSE, '{\"subscription_based\": true, \"account_limit\": true}')
ON CONFLICT (name) DO NOTHING;" "Insert default product types"

# 2.19 Insert default AFF packages
run_sql "
INSERT INTO \"AFFPackages\" (package_name, description, account_limit, monthly_price, yearly_discount_percent, created_by)
VALUES
    ('Starter', 'Gói cơ bản cho người mới bắt đầu', 10, 300000, 20, 1),
    ('Professional', 'Gói chuyên nghiệp cho doanh nghiệp nhỏ', 30, 800000, 25, 1),
    ('Premium', 'Gói cao cấp cho doanh nghiệp lớn', 50, 1500000, 30, 1),
    ('Enterprise', 'Gói doanh nghiệp không giới hạn', 999999, 3000000, 35, 1)
ON CONFLICT DO NOTHING;" "Insert default AFF packages"

# =============================================================================
# 3. VALIDATE INSTALLATION
# =============================================================================

echo -e "${YELLOW}🔍 Validating installation...${NC}"

# Check if tables exist
check_table() {
    local table_name="$1"
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1 FROM \"$table_name\" LIMIT 1;" > /dev/null 2>&1; then
        echo -e "${GREEN}   ✅ Table $table_name exists${NC}"
    else
        echo -e "${RED}   ❌ Table $table_name missing${NC}"
    fi
}

check_table "ProductCategories"
check_table "Products"
check_table "ProductFiles"
check_table "AccountPackages"
check_table "PackageAccounts"
check_table "Orders"
check_table "OrderItems"
check_table "AFFPackages"
check_table "UserSubscriptions"
check_table "ProductTypes"
check_table "WarrantyRequests"
check_table "MarketplaceTransactions"
check_table "Config"

# Check if revenue_enabled column exists
if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "SELECT revenue_enabled FROM \"Accounts\" LIMIT 1;" > /dev/null 2>&1; then
    echo -e "${GREEN}   ✅ Column revenue_enabled exists in Accounts${NC}"
else
    echo -e "${RED}   ❌ Column revenue_enabled missing in Accounts${NC}"
fi

# Check directories
check_dir() {
    local dir_path="$1"
    if [ -d "$dir_path" ]; then
        echo -e "${GREEN}   ✅ Directory $dir_path exists${NC}"
    else
        echo -e "${RED}   ❌ Directory $dir_path missing${NC}"
    fi
}

check_dir "$UPLOADS_DIR/coffee-icons"
check_dir "$UPLOADS_DIR/products"
check_dir "$UPLOADS_DIR/categories"

echo ""

# =============================================================================
# 4. RESTART SERVICES
# =============================================================================

echo -e "${YELLOW}🔄 Restarting services...${NC}"

# Restart gunicorn service
if systemctl is-active --quiet sapmmo; then
    echo -e "${BLUE}   Restarting SAPMMO service...${NC}"
    systemctl restart sapmmo
    echo -e "${GREEN}   ✅ SAPMMO service restarted${NC}"
else
    echo -e "${YELLOW}   ⚠️  SAPMMO service not found or not running${NC}"
fi

# Restart nginx
if systemctl is-active --quiet nginx; then
    echo -e "${BLUE}   Restarting Nginx...${NC}"
    systemctl restart nginx
    echo -e "${GREEN}   ✅ Nginx restarted${NC}"
else
    echo -e "${YELLOW}   ⚠️  Nginx service not found or not running${NC}"
fi

echo ""

# =============================================================================
# 5. COMPLETION
# =============================================================================

echo -e "${GREEN}🎉 Marketplace deployment completed successfully!${NC}"
echo "================================================"
echo ""
echo -e "${BLUE}📋 Summary of changes:${NC}"
echo "   • Created 13 marketplace database tables:"
echo "     - ProductCategories, Products, ProductFiles, AccountPackages, PackageAccounts"
echo "     - Orders, OrderItems, AFFPackages, UserSubscriptions, ProductTypes"
echo "     - WarrantyRequests, MarketplaceTransactions, Config"
echo "   • Added revenue_enabled column to Accounts table"
echo "   • Created upload directories for coffee-icons, products, categories"
echo "   • Inserted default configurations, categories, product types, and AFF packages"
echo "   • Created performance indexes for better query speed"
echo "   • Restarted application services"
echo ""
echo -e "${YELLOW}📝 Next steps:${NC}"
echo "   1. Push your code changes to the server"
echo "   2. Test the marketplace functionality"
echo "   3. Configure coffee cup display settings in /config"
echo "   4. Add products and categories as needed"
echo ""
echo -e "${GREEN}✅ Deployment script completed at $(date)${NC}"
