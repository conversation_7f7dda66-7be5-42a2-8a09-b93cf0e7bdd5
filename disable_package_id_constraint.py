#!/usr/bin/env python3
"""
Temporarily disable package_id foreign key constraint to allow testing
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def disable_package_id_constraint():
    """Temporarily disable package_id foreign key constraint"""
    
    print("🚀 Temporarily disabling package_id foreign key constraint...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current constraints
        print("\n🔧 Step 1: Checking current constraints...")
        
        cursor.execute('''
            SELECT constraint_name, constraint_type 
            FROM information_schema.table_constraints 
            WHERE table_name = 'Accounts' AND constraint_name LIKE '%package_id%';
        ''')
        constraints = cursor.fetchall()
        
        for constraint in constraints:
            print(f"📋 Found constraint: {constraint[0]} ({constraint[1]})")
        
        # Step 2: Drop foreign key constraint
        print("\n🔧 Step 2: Dropping foreign key constraint...")
        
        try:
            cursor.execute('''
                ALTER TABLE "Accounts" DROP CONSTRAINT IF EXISTS "Accounts_package_id_fkey";
            ''')
            print("  ✅ Dropped foreign key constraint: Accounts_package_id_fkey")
        except Exception as e:
            print(f"  ⚠️  Drop constraint: {e}")
        
        # Step 3: Clean up any invalid package_id values
        print("\n🔧 Step 3: Cleaning up invalid package_id values...")
        
        cursor.execute('''
            UPDATE "Accounts" 
            SET package_id = NULL, marketplace_status = 'available'
            WHERE package_id IS NOT NULL 
            AND package_id NOT IN (SELECT product_id FROM "Products");
        ''')
        
        cleaned_count = cursor.rowcount
        print(f"  ✅ Cleaned {cleaned_count} accounts with invalid package_id")
        
        # Step 4: Add a check constraint instead (optional validation)
        print("\n🔧 Step 4: Adding check constraint for package_id...")
        
        try:
            cursor.execute('''
                ALTER TABLE "Accounts" DROP CONSTRAINT IF EXISTS "chk_package_id_positive";
                ALTER TABLE "Accounts" ADD CONSTRAINT "chk_package_id_positive" 
                CHECK (package_id IS NULL OR package_id > 0);
            ''')
            print("  ✅ Added check constraint: package_id must be positive or NULL")
        except Exception as e:
            print(f"  ⚠️  Check constraint: {e}")
        
        # Step 5: Verify final state
        print("\n🔧 Step 5: Verifying final state...")
        
        # Check remaining constraints
        cursor.execute('''
            SELECT constraint_name, constraint_type 
            FROM information_schema.table_constraints 
            WHERE table_name = 'Accounts' AND constraint_name LIKE '%package_id%';
        ''')
        final_constraints = cursor.fetchall()
        
        if final_constraints:
            for constraint in final_constraints:
                print(f"  📋 Remaining constraint: {constraint[0]} ({constraint[1]})")
        else:
            print("  ✅ No package_id foreign key constraints remaining")
        
        # Test UPDATE query
        print("\n🔧 Step 6: Testing UPDATE query...")
        
        try:
            # Test with a non-existent package_id (should work now)
            cursor.execute('SAVEPOINT test_update;')
            cursor.execute('''
                UPDATE "Accounts" 
                SET marketplace_status = 'reserved', package_id = 999999
                WHERE account_id = (SELECT account_id FROM "Accounts" LIMIT 1);
            ''')
            cursor.execute('ROLLBACK TO SAVEPOINT test_update;')
            
            print("  ✅ UPDATE query with non-existent package_id works (no foreign key constraint)")
        except Exception as e:
            print(f"  ❌ UPDATE query test failed: {e}")
        
        # Step 7: Show current accounts with package_id
        print("\n🔧 Step 7: Current accounts with package_id...")
        
        cursor.execute('''
            SELECT COUNT(*) as total,
                   COUNT(CASE WHEN package_id IS NOT NULL THEN 1 END) as with_package,
                   COUNT(CASE WHEN marketplace_status = 'reserved' THEN 1 END) as reserved
            FROM "Accounts";
        ''')
        
        stats = cursor.fetchone()
        print(f"📊 Accounts stats:")
        print(f"  - Total: {stats[0]}")
        print(f"  - With package_id: {stats[1]}")
        print(f"  - Reserved status: {stats[2]}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Foreign key constraint disabled successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test product creation with account type")
        print("   3. Monitor for any data integrity issues")
        print("   4. Consider re-enabling constraint after fixing workflow")
        
        print("\n⚠️  WARNING:")
        print("   - Foreign key constraint is disabled")
        print("   - Data integrity is not enforced at database level")
        print("   - Application must ensure valid package_id values")
        
        return True
        
    except Exception as e:
        print(f"❌ Disable failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = disable_package_id_constraint()
    sys.exit(0 if success else 1)
