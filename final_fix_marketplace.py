#!/usr/bin/env python3
"""
Final fix for marketplace deployment
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def final_fix():
    """Final fix for marketplace deployment"""
    
    print("🔧 Final marketplace deployment fix...")
    print("=" * 50)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Fix Config table - add unique constraint
        print("\n🔧 Fixing Config table...")
        try:
            # Check if unique constraint exists
            cursor.execute('''
                SELECT constraint_name 
                FROM information_schema.table_constraints 
                WHERE table_name = 'Config' 
                AND constraint_type = 'UNIQUE'
                AND constraint_name LIKE '%config_key%';
            ''')
            
            unique_constraints = cursor.fetchall()
            if not unique_constraints:
                print("🔧 Adding unique constraint to config_key...")
                cursor.execute('ALTER TABLE "Config" ADD CONSTRAINT config_key_unique UNIQUE (config_key);')
                print("✅ Unique constraint added")
            else:
                print("✅ Unique constraint already exists")
                
        except Exception as e:
            print(f"⚠️  Config table issue: {e}")
        
        # Insert coffee configs (without ON CONFLICT first)
        print("\n🔧 Inserting coffee configs...")
        coffee_configs = [
            ('enable_coffee_display', 'false'),
            ('coffee_cup_value', '50000'),
            ('coffee_cup_icon', '☕'),
            ('coffee_icon_type', 'emoji'),
            ('coffee_icon_width', '20'),
            ('coffee_icon_height', '20')
        ]
        
        for key, value in coffee_configs:
            try:
                # Check if config exists first
                cursor.execute('SELECT config_id FROM "Config" WHERE config_key = %s;', (key,))
                existing = cursor.fetchone()
                
                if not existing:
                    cursor.execute('''
                        INSERT INTO "Config" (config_key, config_value) 
                        VALUES (%s, %s);
                    ''', (key, value))
                    print(f"✅ Inserted {key}")
                else:
                    print(f"⚠️  {key} already exists")
                    
            except Exception as e:
                print(f"⚠️  Config {key} issue: {e}")
        
        # Commit changes
        conn.commit()
        
        # Final comprehensive validation
        print("\n🔍 Final validation...")
        
        # Check all tables
        tables = [
            "ProductCategories", "Products", "ProductFiles", "AccountPackages", 
            "PackageAccounts", "Orders", "OrderItems", "AFFPackages", 
            "UserSubscriptions", "ProductTypes", "WarrantyRequests", 
            "MarketplaceTransactions", "Config"
        ]
        
        all_good = True
        for table in tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{table}";')
                count = cursor.fetchone()[0]
                print(f"✅ {table}: {count} records")
            except Exception as e:
                print(f"❌ {table}: {e}")
                all_good = False
        
        # Check revenue_enabled column
        try:
            cursor.execute('SELECT COUNT(*) FROM "Accounts" WHERE revenue_enabled IS NOT NULL;')
            total_with_column = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM "Accounts" WHERE revenue_enabled = TRUE;')
            enabled_count = cursor.fetchone()[0]
            print(f"✅ Accounts.revenue_enabled: {enabled_count}/{total_with_column} enabled")
        except Exception as e:
            print(f"❌ revenue_enabled column: {e}")
            all_good = False
        
        # Check coffee configs
        print(f"\n🔍 Coffee configs:")
        for key, _ in coffee_configs:
            try:
                cursor.execute('SELECT config_value FROM "Config" WHERE config_key = %s;', (key,))
                result = cursor.fetchone()
                if result:
                    print(f"✅ {key}: {result[0]}")
                else:
                    print(f"❌ {key}: missing")
                    all_good = False
            except Exception as e:
                print(f"❌ {key}: {e}")
                all_good = False
        
        # Check primary keys
        print(f"\n🔍 Primary keys:")
        key_tables = ["Accounts", "ProductCategories", "Products", "Orders", "Config"]
        for table in key_tables:
            try:
                cursor.execute(f'''
                    SELECT constraint_name 
                    FROM information_schema.table_constraints 
                    WHERE table_name = '{table}' 
                    AND constraint_type = 'PRIMARY KEY';
                ''')
                pk = cursor.fetchone()
                if pk:
                    print(f"✅ {table}: {pk[0]}")
                else:
                    print(f"❌ {table}: no primary key")
                    all_good = False
            except Exception as e:
                print(f"❌ {table}: {e}")
                all_good = False
        
        # Check indexes
        print(f"\n🔍 Key indexes:")
        important_indexes = [
            "idx_products_category", "idx_products_active", 
            "idx_orders_user", "idx_orders_status"
        ]
        for idx in important_indexes:
            try:
                cursor.execute(f'''
                    SELECT indexname 
                    FROM pg_indexes 
                    WHERE indexname = '{idx}';
                ''')
                result = cursor.fetchone()
                if result:
                    print(f"✅ {idx}")
                else:
                    print(f"⚠️  {idx}: missing")
            except Exception as e:
                print(f"❌ {idx}: {e}")
        
        cursor.close()
        conn.close()
        
        if all_good:
            print(f"\n🎉 Marketplace deployment completed successfully!")
            print("📝 Next steps:")
            print("   1. Test marketplace functionality at /marketplace")
            print("   2. Test AFF packages at /marketplace/aff-packages") 
            print("   3. Configure coffee display at /config")
            print("   4. Test revenue toggle in /accounts")
        else:
            print(f"\n⚠️  Deployment completed with some issues")
            print("   Please check the errors above")
        
        return all_good
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = final_fix()
    sys.exit(0 if success else 1)
