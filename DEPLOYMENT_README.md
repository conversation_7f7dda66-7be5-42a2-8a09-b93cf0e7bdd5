# Marketplace Deployment Guide

## 📋 Tổng quan

Script deployment này sẽ cập nhật database và tạo các folders cần thiết cho tính năng marketplace mới.

## 🗂️ Các thay đổi chính

### Database Tables được thêm:
- **Products** - Quản lý sản phẩm marketplace
- **Categories** - Phân loại sản phẩm  
- **Orders** - Đơn hàng
- **OrderItems** - Chi tiết đơn hàng
- **AFFPackages** - Gói affiliate marketing
- **UserSubscriptions** - Subscription của user
- **Config** - <PERSON><PERSON><PERSON> hình hệ thống

### Columns được thêm:
- **Accounts.revenue_enabled** (BOOLEAN) - Cho phép toggle accounts để tính doanh thu

### Folders được tạo:
- `/var/www/sapmmo/static/uploads/coffee-icons/` - Lưu icon ly cafe
- `/var/www/sapmmo/static/uploads/products/` - <PERSON><PERSON><PERSON> hình sản phẩm
- `/var/www/sapmmo/static/uploads/categories/` - Lưu hình categories

### Configs được thêm:
- Coffee cup display settings (enable, value, icon, width, height)
- Default categories (Gói AFF, Tools, Services)

## 🚀 Cách deployment

### Yêu cầu trước khi chạy:
- File `db_config.py` phải tồn tại trong `/var/www/sapmmo/`
- Database PostgreSQL đã được setup
- Python3 đã được cài đặt

### Option 1: Bash Script (Recommended)

```bash
# 1. Upload script lên server
scp deploy_marketplace_update.sh user@server:/tmp/

# 2. SSH vào server
ssh user@server

# 3. Chạy script với quyền root
sudo chmod +x /tmp/deploy_marketplace_update.sh
sudo /tmp/deploy_marketplace_update.sh
```

### Option 2: Python Script

```bash
# 1. Upload script lên server
scp migrate_marketplace.py user@server:/tmp/

# 2. SSH vào server
ssh user@server

# 3. Chạy migration
sudo python3 /tmp/migrate_marketplace.py
```

## ✅ Validation

Script sẽ tự động validate:
- ✅ Tất cả tables đã được tạo
- ✅ Column revenue_enabled đã được thêm
- ✅ Directories đã được tạo với permissions đúng
- ✅ Services đã được restart

## 🔧 Troubleshooting

### Lỗi database connection:
```bash
# Kiểm tra PostgreSQL service
sudo systemctl status postgresql

# Kiểm tra database tồn tại
sudo -u postgres psql -l | grep sapmmo

# Kiểm tra db_config.py
cat /var/www/sapmmo/db_config.py

# Test kết nối database
cd /var/www/sapmmo
python3 -c "from db_config import PG_CONFIG; print('DB Config:', PG_CONFIG)"
```

### Lỗi permissions:
```bash
# Fix permissions cho upload directories
sudo chown -R www-data:www-data /var/www/sapmmo/static/uploads/
sudo chmod -R 755 /var/www/sapmmo/static/uploads/
```

### Lỗi service restart:
```bash
# Kiểm tra service status
sudo systemctl status sapmmo
sudo systemctl status nginx

# Restart manual nếu cần
sudo systemctl restart sapmmo
sudo systemctl restart nginx
```

## 📝 Sau khi deployment

1. **Push code changes** lên server
2. **Test marketplace functionality**:
   - Truy cập `/marketplace` 
   - Truy cập `/marketplace/aff-packages`
   - Test coffee cup display trong `/config`
3. **Configure settings**:
   - Vào `/config` để setup coffee cup display
   - Thêm products và categories qua admin panel

## 🎯 Tính năng mới

### Coffee Cup Display:
- Hiển thị giá sản phẩm dưới dạng ly cafe thay vì MP
- Tùy chỉnh icon (emoji hoặc hình ảnh)
- Tùy chỉnh kích thước icon
- Chỉ hiển thị ở marketplace, user balance vẫn hiển thị MP

### Revenue Check Toggle:
- User/Leader có thể chọn accounts nào được tính vào doanh thu
- Giới hạn theo gói subscription đã mua
- Chỉ accounts có cookie mới được phép toggle
- Dashboard/Revenue chỉ tính accounts đã được bật

### Account Management:
- User có thể edit accounts đã mua từ marketplace
- Logic quyền: team accounts + purchased accounts
- Marketplace integration với account ownership

## 🔍 Kiểm tra sau deployment

```bash
# Kiểm tra tables
sudo -u postgres psql sapmmo -c "\dt"

# Kiểm tra revenue_enabled column
sudo -u postgres psql sapmmo -c "\d \"Accounts\""

# Kiểm tra upload directories
ls -la /var/www/sapmmo/static/uploads/

# Kiểm tra service logs
sudo journalctl -u sapmmo -f
```

## 📞 Support

Nếu có vấn đề trong quá trình deployment, hãy check:
1. Database connection
2. File permissions  
3. Service status
4. Application logs

Script đã được test trên localhost và sẽ hoạt động tương tự trên Ubuntu server.
