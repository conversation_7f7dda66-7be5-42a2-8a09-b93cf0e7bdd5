#!/usr/bin/env python3
"""
Fix Discounts table column names to match code expectations
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_discounts_columns():
    """Fix Discounts table column names"""
    
    print("🔧 Fixing Discounts table column names...")
    print("=" * 50)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Check current Discounts table structure
        print("\n🔍 Checking current Discounts table structure...")
        cursor.execute('''
            SELECT column_name, data_type
            FROM information_schema.columns 
            WHERE table_name = 'Discounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        
        print(f"📋 Current columns: {column_names}")
        
        # Fix column names to match code expectations
        column_fixes = [
            ('discount_value', 'value'),  # Rename discount_value to value
            ('min_order_amount', 'min_amount'),  # Rename min_order_amount to min_amount  
            ('max_discount_amount', 'max_amount'),  # Rename max_discount_amount to max_amount
            ('discount_type', 'type'),  # Rename discount_type to type
            ('usage_limit', 'limit_usage'),  # Rename usage_limit to limit_usage
            ('user_limit', 'limit_per_user')  # Rename user_limit to limit_per_user
        ]
        
        for old_name, new_name in column_fixes:
            if old_name in column_names and new_name not in column_names:
                print(f"🔧 Renaming {old_name} to {new_name}...")
                try:
                    cursor.execute(f'ALTER TABLE "Discounts" RENAME COLUMN {old_name} TO {new_name};')
                    print(f"✅ Renamed {old_name} to {new_name}")
                except Exception as e:
                    print(f"⚠️  Could not rename {old_name}: {e}")
            elif new_name in column_names:
                print(f"✅ Column {new_name} already exists")
            else:
                print(f"⚠️  Column {old_name} not found")
        
        # Add any missing columns that code expects
        missing_columns = {
            'value': 'DECIMAL(10,2) NOT NULL DEFAULT 0',
            'type': 'VARCHAR(20) NOT NULL DEFAULT \'percentage\'',
            'min_amount': 'DECIMAL(15,2) DEFAULT 0',
            'max_amount': 'DECIMAL(15,2)',
            'limit_usage': 'INTEGER DEFAULT NULL',
            'limit_per_user': 'INTEGER DEFAULT 1'
        }
        
        # Re-check columns after rename
        cursor.execute('''
            SELECT column_name
            FROM information_schema.columns 
            WHERE table_name = 'Discounts' AND table_schema = 'public';
        ''')
        
        current_columns = [col[0] for col in cursor.fetchall()]
        
        for col_name, col_def in missing_columns.items():
            if col_name not in current_columns:
                print(f"🔧 Adding missing column {col_name}...")
                try:
                    cursor.execute(f'ALTER TABLE "Discounts" ADD COLUMN {col_name} {col_def};')
                    print(f"✅ Added column {col_name}")
                except Exception as e:
                    print(f"⚠️  Could not add {col_name}: {e}")
        
        # Commit changes
        conn.commit()
        
        # Validate final structure
        print("\n🔍 Final Discounts table structure...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Discounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Final table has {len(final_columns)} columns:")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Test a simple query that the code expects
        print("\n🔍 Testing expected query...")
        try:
            cursor.execute('''
                SELECT discount_id, code, description, type, value, 
                       min_amount, max_amount, limit_usage, used_count, is_active
                FROM "Discounts" 
                LIMIT 1;
            ''')
            print("✅ Query test successful - code should work now")
        except Exception as e:
            print(f"❌ Query test failed: {e}")
            
            # Show what columns are actually available
            cursor.execute('SELECT column_name FROM information_schema.columns WHERE table_name = \'Discounts\';')
            available_cols = [col[0] for col in cursor.fetchall()]
            print(f"Available columns: {available_cols}")
        
        # Check sample data
        cursor.execute('SELECT COUNT(*) FROM "Discounts";')
        count = cursor.fetchone()[0]
        print(f"\n📊 Discounts table has {count} records")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Discounts table structure fixed!")
        print("📝 Next steps:")
        print("   1. Restart sapmmo service")
        print("   2. Test /admin/marketplace/discounts")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_discounts_columns()
    sys.exit(0 if success else 1)
