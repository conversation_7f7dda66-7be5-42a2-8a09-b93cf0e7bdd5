#!/usr/bin/env python3
"""
Create Discounts table for marketplace
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def create_discounts_table():
    """Create Discounts table"""
    
    print("🔧 Creating Discounts table...")
    print("=" * 50)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Create Discounts table
        print("\n🔧 Creating Discounts table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "Discounts" (
                discount_id SERIAL PRIMARY KEY,
                code VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(200),
                description TEXT,
                discount_type VARCHAR(20) NOT NULL DEFAULT 'percentage',
                discount_value DECIMAL(10,2) NOT NULL,
                min_order_amount DECIMAL(15,2) DEFAULT 0,
                max_discount_amount DECIMAL(15,2),
                usage_limit INTEGER DEFAULT NULL,
                used_count INTEGER DEFAULT 0,
                user_limit INTEGER DEFAULT 1,
                is_active BOOLEAN DEFAULT TRUE,
                valid_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                valid_to TIMESTAMP,
                applicable_categories TEXT,
                applicable_products TEXT,
                created_by INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        print("✅ Discounts table created successfully")
        
        # Create indexes for performance
        print("\n🔧 Creating indexes...")
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_discounts_code ON "Discounts"(code);',
            'CREATE INDEX IF NOT EXISTS idx_discounts_active ON "Discounts"(is_active);',
            'CREATE INDEX IF NOT EXISTS idx_discounts_valid_from ON "Discounts"(valid_from);',
            'CREATE INDEX IF NOT EXISTS idx_discounts_valid_to ON "Discounts"(valid_to);',
            'CREATE INDEX IF NOT EXISTS idx_discounts_type ON "Discounts"(discount_type);'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        print("✅ Indexes created successfully")
        
        # Insert some sample discount codes
        print("\n🔧 Creating sample discount codes...")
        sample_discounts = [
            {
                'code': 'WELCOME10',
                'name': 'Chào mừng khách hàng mới',
                'description': 'Giảm 10% cho đơn hàng đầu tiên',
                'discount_type': 'percentage',
                'discount_value': 10.00,
                'min_order_amount': 100000,
                'max_discount_amount': 50000,
                'usage_limit': 100,
                'user_limit': 1
            },
            {
                'code': 'SAVE50K',
                'name': 'Giảm 50K',
                'description': 'Giảm 50,000 MP cho đơn hàng từ 500K',
                'discount_type': 'fixed',
                'discount_value': 50000,
                'min_order_amount': 500000,
                'usage_limit': 50,
                'user_limit': 1
            },
            {
                'code': 'VIP20',
                'name': 'VIP 20%',
                'description': 'Giảm 20% cho khách VIP',
                'discount_type': 'percentage',
                'discount_value': 20.00,
                'min_order_amount': 200000,
                'max_discount_amount': 100000,
                'usage_limit': 20,
                'user_limit': 2
            }
        ]
        
        for discount in sample_discounts:
            try:
                cursor.execute('''
                    INSERT INTO "Discounts" (
                        code, name, description, discount_type, discount_value,
                        min_order_amount, max_discount_amount, usage_limit, user_limit,
                        created_by
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    ON CONFLICT (code) DO NOTHING;
                ''', (
                    discount['code'], discount['name'], discount['description'],
                    discount['discount_type'], discount['discount_value'],
                    discount['min_order_amount'], discount.get('max_discount_amount'),
                    discount['usage_limit'], discount['user_limit'], 1
                ))
                print(f"✅ Created discount: {discount['code']}")
            except Exception as e:
                print(f"⚠️  Discount {discount['code']}: {e}")
        
        # Create DiscountUsage table for tracking user usage
        print("\n🔧 Creating DiscountUsage table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "DiscountUsage" (
                usage_id SERIAL PRIMARY KEY,
                discount_id INTEGER REFERENCES "Discounts"(discount_id),
                user_id INTEGER,
                order_id INTEGER,
                discount_amount DECIMAL(15,2) NOT NULL,
                used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        ''')
        print("✅ DiscountUsage table created")
        
        # Create indexes for DiscountUsage
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_discount_usage_discount ON "DiscountUsage"(discount_id);')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_discount_usage_user ON "DiscountUsage"(user_id);')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_discount_usage_order ON "DiscountUsage"(order_id);')
        
        # Commit all changes
        conn.commit()
        
        # Validate tables
        print("\n🔍 Validating Discounts system...")
        
        # Check Discounts table
        cursor.execute('SELECT COUNT(*) FROM "Discounts";')
        discount_count = cursor.fetchone()[0]
        print(f"✅ Discounts table: {discount_count} records")
        
        # Check DiscountUsage table
        cursor.execute('SELECT COUNT(*) FROM "DiscountUsage";')
        usage_count = cursor.fetchone()[0]
        print(f"✅ DiscountUsage table: {usage_count} records")
        
        # Show sample discounts
        if discount_count > 0:
            cursor.execute('''
                SELECT code, name, discount_type, discount_value, 
                       min_order_amount, usage_limit, used_count, is_active
                FROM "Discounts" 
                ORDER BY created_at 
                LIMIT 5;
            ''')
            
            discounts = cursor.fetchall()
            print("\n📋 Sample discounts:")
            for disc in discounts:
                status = "🟢 Active" if disc[7] else "🔴 Inactive"
                if disc[2] == 'percentage':
                    value = f"{disc[3]}%"
                else:
                    value = f"{int(disc[3]):,} MP"
                
                print(f"  - {disc[0]}: {disc[1]} ({value}) - Min: {int(disc[4]):,} MP - Used: {disc[6]}/{disc[5]} {status}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Discounts system created successfully!")
        print("📝 Next steps:")
        print("   1. Restart sapmmo service")
        print("   2. Test /admin/marketplace/discounts")
        print("   3. Test discount codes in marketplace checkout")
        
        return True
        
    except Exception as e:
        print(f"❌ Creation failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = create_discounts_table()
    sys.exit(0 if success else 1)
