<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Cart Button Effects</title>
    <link href="https://cdn.jsdelivr.net/npm/@coreui/coreui@4.2.6/dist/css/coreui.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@coreui/icons@2.1.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            padding: 50px;
        }
        
        .cart-floating {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
            background: linear-gradient(135deg, #00C6AE 0%, #00a693 100%);
            color: white;
            border: none;
            border-radius: 60px;
            padding: 18px 25px;
            box-shadow: 0 8px 25px rgba(0,198,174,0.4);
            transition: all 0.3s ease;
            font-size: 1.1rem;
            font-weight: 600;
            min-width: 120px;
            cursor: pointer;
        }
        .cart-floating:hover {
            background: linear-gradient(135deg, #00a693 0%, #008a7a 100%);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(0,198,174,0.5);
        }
        .cart-floating:active {
            transform: translateY(-1px) scale(1.02);
        }
        .cart-floating .cart-count {
            background: #ff4757;
            color: white;
            border-radius: 50%;
            padding: 4px 8px;
            font-size: 0.8rem;
            font-weight: bold;
            margin-left: 8px;
            min-width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }
        .cart-floating.shake {
            animation: shake 0.6s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
            20%, 40%, 60%, 80% { transform: translateX(3px); }
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        @keyframes bounceIn {
            0% { 
                opacity: 0;
                transform: scale(0.3) translateY(20px);
            }
            50% { 
                opacity: 1;
                transform: scale(1.05) translateY(-5px);
            }
            70% { 
                transform: scale(0.95) translateY(0);
            }
            100% { 
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }
        
        .cart-floating.bounce-in {
            animation: bounceIn 0.6s ease-out;
        }
        
        .cart-floating i {
            font-size: 1.2rem;
            margin-right: 5px;
        }
        
        .cart-floating .cart-text {
            font-size: 0.9rem;
            margin: 0 5px;
        }
        
        .demo-controls {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .demo-button {
            margin: 10px;
            padding: 12px 24px;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .demo-button.primary {
            background: #00C6AE;
            color: white;
        }
        
        .demo-button.primary:hover {
            background: #00a693;
            transform: translateY(-2px);
        }
        
        .demo-button.secondary {
            background: #6c757d;
            color: white;
        }
        
        .demo-button.secondary:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }
        
        .demo-button.danger {
            background: #dc3545;
            color: white;
        }
        
        .demo-button.danger:hover {
            background: #c82333;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">🛒 Demo Cart Button Effects</h1>
        
        <div class="demo-controls">
            <h3>🎮 Controls</h3>
            <p>Test các hiệu ứng của cart floating button:</p>
            
            <button class="demo-button primary" onclick="showCart()">
                <i class="cil-eye"></i> Show Cart
            </button>
            
            <button class="demo-button primary" onclick="addItem()">
                <i class="cil-plus"></i> Add Item (Shake Effect)
            </button>
            
            <button class="demo-button secondary" onclick="triggerBounce()">
                <i class="cil-media-play"></i> Bounce Effect
            </button>
            
            <button class="demo-button danger" onclick="hideCart()">
                <i class="cil-x"></i> Hide Cart
            </button>
            
            <button class="demo-button secondary" onclick="resetCart()">
                <i class="cil-reload"></i> Reset
            </button>
        </div>
        
        <div class="demo-controls">
            <h3>✨ Features</h3>
            <ul>
                <li><strong>Gradient Background:</strong> Đẹp mắt với gradient #00C6AE → #00a693</li>
                <li><strong>Hover Effect:</strong> Nâng lên + scale + shadow tăng</li>
                <li><strong>Shake Animation:</strong> Lắc lắc khi thêm sản phẩm</li>
                <li><strong>Bounce In:</strong> Hiệu ứng xuất hiện lần đầu</li>
                <li><strong>Pulse Count:</strong> Badge số lượng nhấp nháy</li>
                <li><strong>Responsive:</strong> Tự động ẩn/hiện theo số lượng</li>
            </ul>
        </div>
        
        <div class="demo-controls">
            <h3>📱 Responsive</h3>
            <p>Button sẽ tự động điều chỉnh kích thước trên mobile và tablet.</p>
            <p>Position: <code>bottom: 30px, right: 30px</code> để không che nội dung.</p>
        </div>
    </div>

    <!-- Floating Cart Button -->
    <button class="cart-floating" onclick="openCart()" style="display: none;" id="cartButton">
        <i class="cil-basket"></i>
        <span class="cart-text">Giỏ hàng</span>
        <span class="cart-count" id="cartCount">0</span>
    </button>

    <script>
        let cartCount = 0;
        
        function showCart() {
            const cartButton = document.getElementById('cartButton');
            cartButton.style.display = 'block';
            cartButton.classList.add('bounce-in');
            setTimeout(() => {
                cartButton.classList.remove('bounce-in');
            }, 600);
        }
        
        function hideCart() {
            document.getElementById('cartButton').style.display = 'none';
            cartCount = 0;
            document.getElementById('cartCount').textContent = cartCount;
        }
        
        function addItem() {
            cartCount++;
            document.getElementById('cartCount').textContent = cartCount;
            
            const cartButton = document.getElementById('cartButton');
            const wasHidden = cartButton.style.display === 'none';
            
            if (wasHidden) {
                showCart();
            }
            
            // Trigger shake effect
            cartButton.classList.add('shake');
            setTimeout(() => {
                cartButton.classList.remove('shake');
            }, 600);
        }
        
        function triggerBounce() {
            const cartButton = document.getElementById('cartButton');
            cartButton.classList.add('bounce-in');
            setTimeout(() => {
                cartButton.classList.remove('bounce-in');
            }, 600);
        }
        
        function resetCart() {
            cartCount = 0;
            document.getElementById('cartCount').textContent = cartCount;
            hideCart();
        }
        
        function openCart() {
            alert('🛒 Mở giỏ hàng!\n\nTrong thực tế sẽ mở modal giỏ hàng.');
        }
        
        // Auto demo
        setTimeout(() => {
            console.log('🎬 Auto demo starting...');
            showCart();
            
            setTimeout(() => {
                addItem();
            }, 1000);
            
            setTimeout(() => {
                addItem();
            }, 2000);
            
            setTimeout(() => {
                addItem();
            }, 3000);
        }, 1000);
    </script>
</body>
</html>
