#!/usr/bin/env python3
"""
Fix package_id foreign key constraint issue
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_package_id_constraint():
    """Fix package_id foreign key constraint"""
    
    print("🚀 Fixing package_id foreign key constraint...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current data
        print("\n🔧 Step 1: Checking current data...")
        
        # Check Products
        cursor.execute('SELECT COUNT(*), MIN(product_id), MAX(product_id) FROM "Products";')
        product_stats = cursor.fetchone()
        print(f"📊 Products: {product_stats[0]} records, ID range: {product_stats[1]}-{product_stats[2]}")
        
        # Check Accounts with package_id
        cursor.execute('SELECT COUNT(*) FROM "Accounts" WHERE package_id IS NOT NULL;')
        accounts_with_package = cursor.fetchone()[0]
        print(f"📊 Accounts with package_id: {accounts_with_package}")
        
        # Check invalid package_id references
        cursor.execute('''
            SELECT DISTINCT a.package_id 
            FROM "Accounts" a 
            LEFT JOIN "Products" p ON a.package_id = p.product_id 
            WHERE a.package_id IS NOT NULL AND p.product_id IS NULL;
        ''')
        invalid_refs = cursor.fetchall()
        
        if invalid_refs:
            print(f"❌ Found invalid package_id references: {[r[0] for r in invalid_refs]}")
        else:
            print("✅ No invalid package_id references found")
        
        # Step 2: Drop existing foreign key constraint
        print("\n🔧 Step 2: Dropping existing foreign key constraint...")
        
        try:
            cursor.execute('''
                ALTER TABLE "Accounts" DROP CONSTRAINT IF EXISTS "Accounts_package_id_fkey";
            ''')
            print("  ✅ Dropped existing foreign key constraint")
        except Exception as e:
            print(f"  ⚠️  Drop constraint: {e}")
        
        # Step 3: Clean up invalid references
        if invalid_refs:
            print("\n🔧 Step 3: Cleaning up invalid package_id references...")
            
            for invalid_id in [r[0] for r in invalid_refs]:
                cursor.execute('''
                    UPDATE "Accounts" 
                    SET package_id = NULL, marketplace_status = 'available'
                    WHERE package_id = %s;
                ''', (invalid_id,))
                updated = cursor.rowcount
                print(f"  ✅ Cleaned {updated} accounts with invalid package_id: {invalid_id}")
        
        # Step 4: Recreate foreign key constraint with better handling
        print("\n🔧 Step 4: Recreating foreign key constraint...")
        
        try:
            cursor.execute('''
                ALTER TABLE "Accounts" ADD CONSTRAINT "Accounts_package_id_fkey" 
                FOREIGN KEY ("package_id") REFERENCES "Products" ("product_id") 
                ON DELETE SET NULL ON UPDATE CASCADE;
            ''')
            print("  ✅ Recreated foreign key constraint with ON DELETE SET NULL")
        except Exception as e:
            print(f"  ❌ Failed to recreate constraint: {e}")
            print("  💡 Will proceed without foreign key constraint for now")
        
        # Step 5: Alternative - Create constraint as DEFERRABLE
        if invalid_refs:
            print("\n🔧 Step 5: Creating DEFERRABLE constraint as alternative...")
            
            try:
                cursor.execute('''
                    ALTER TABLE "Accounts" DROP CONSTRAINT IF EXISTS "Accounts_package_id_fkey";
                    ALTER TABLE "Accounts" ADD CONSTRAINT "Accounts_package_id_fkey" 
                    FOREIGN KEY ("package_id") REFERENCES "Products" ("product_id") 
                    ON DELETE SET NULL ON UPDATE CASCADE
                    DEFERRABLE INITIALLY DEFERRED;
                ''')
                print("  ✅ Created DEFERRABLE foreign key constraint")
            except Exception as e:
                print(f"  ⚠️  DEFERRABLE constraint: {e}")
        
        # Step 6: Verify final state
        print("\n🔧 Step 6: Verifying final state...")
        
        # Check constraint exists
        cursor.execute('''
            SELECT constraint_name, constraint_type 
            FROM information_schema.table_constraints 
            WHERE table_name = 'Accounts' AND constraint_name LIKE '%package_id%';
        ''')
        constraints = cursor.fetchall()
        
        if constraints:
            for constraint in constraints:
                print(f"  ✅ Constraint: {constraint[0]} ({constraint[1]})")
        else:
            print("  ⚠️  No package_id constraints found")
        
        # Test UPDATE query
        print("\n🔧 Step 7: Testing UPDATE query...")
        
        try:
            # Find a valid product_id
            cursor.execute('SELECT product_id FROM "Products" LIMIT 1;')
            valid_product = cursor.fetchone()
            
            if valid_product:
                valid_id = valid_product[0]
                print(f"  📝 Testing with valid product_id: {valid_id}")
                
                # Test the UPDATE (but rollback)
                cursor.execute('SAVEPOINT test_update;')
                cursor.execute('''
                    UPDATE "Accounts" 
                    SET marketplace_status = 'reserved', package_id = %s 
                    WHERE account_id = (SELECT account_id FROM "Accounts" LIMIT 1);
                ''', (valid_id,))
                cursor.execute('ROLLBACK TO SAVEPOINT test_update;')
                
                print("  ✅ UPDATE query test successful")
            else:
                print("  ⚠️  No products found for testing")
                
        except Exception as e:
            print(f"  ❌ UPDATE query test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Foreign key constraint fixed!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test product creation with account type")
        print("   3. Ensure product exists before assigning accounts")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_package_id_constraint()
    sys.exit(0 if success else 1)
