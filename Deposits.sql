/*
 Navicat Premium Data Transfer

 Source Server         : SAPMMO
 Source Server Type    : PostgreSQL
 Source Server Version : 120022 (120022)
 Source Host           : ***********:5432
 Source Catalog        : sapmmo
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 120022 (120022)
 File Encoding         : 65001

 Date: 02/09/2025 11:15:27
*/


-- ----------------------------
-- Table structure for Deposits
-- ----------------------------
DROP TABLE IF EXISTS "public"."Deposits";
CREATE TABLE "public"."Deposits" (
  "id" int4 NOT NULL DEFAULT nextval('"Deposits_id_seq"'::regclass),
  "user_id" int4 NOT NULL,
  "amount" text COLLATE "pg_catalog"."default" NOT NULL,
  "mp_amount" int4 NOT NULL,
  "status" text COLLATE "pg_catalog"."default" DEFAULT '''pending'''::text,
  "transfer_content" text COLLATE "pg_catalog"."default",
  "transaction_id" int4,
  "created_at" time(6) DEFAULT CURRENT_TIMESTAMP,
  "completed_at" time(6)
)
;
ALTER TABLE "public"."Deposits" OWNER TO "sapmmo";

-- ----------------------------
-- Records of Deposits
-- ----------------------------
BEGIN;
INSERT INTO "public"."Deposits" ("id", "user_id", "amount", "mp_amount", "status", "transfer_content", "transaction_id", "created_at", "completed_at") VALUES (2, 9, '100000', 100000, 'pending', 'MIPalan6065', NULL, '08:21:14.453581', NULL);
INSERT INTO "public"."Deposits" ("id", "user_id", "amount", "mp_amount", "status", "transfer_content", "transaction_id", "created_at", "completed_at") VALUES (4, 9, '10000', 10000, 'completed', 'MIPalan6065', 6, '08:57:22.577487', '08:57:59.41727');
INSERT INTO "public"."Deposits" ("id", "user_id", "amount", "mp_amount", "status", "transfer_content", "transaction_id", "created_at", "completed_at") VALUES (5, 10, '100000', 100000, 'pending', 'MIPhuyenmip4188', NULL, '09:31:54.211392', NULL);
INSERT INTO "public"."Deposits" ("id", "user_id", "amount", "mp_amount", "status", "transfer_content", "transaction_id", "created_at", "completed_at") VALUES (6, 9, '100000', 100000, 'pending', 'MIPalan6065', NULL, '03:45:47.043848', NULL);
INSERT INTO "public"."Deposits" ("id", "user_id", "amount", "mp_amount", "status", "transfer_content", "transaction_id", "created_at", "completed_at") VALUES (3, 9, '100000', 100000, 'completed', 'MIPalan6065', 66, '08:35:29.665098', '03:46:20.577736');
INSERT INTO "public"."Deposits" ("id", "user_id", "amount", "mp_amount", "status", "transfer_content", "transaction_id", "created_at", "completed_at") VALUES (7, 9, '10000', 10000, 'completed', 'MIPalan6065', 67, '03:47:49.519127', '03:48:08.655686');
INSERT INTO "public"."Deposits" ("id", "user_id", "amount", "mp_amount", "status", "transfer_content", "transaction_id", "created_at", "completed_at") VALUES (8, 9, '10000', 10000, 'completed', 'MIPalan6065', 68, '03:48:39.126437', '03:49:04.432974');
COMMIT;
