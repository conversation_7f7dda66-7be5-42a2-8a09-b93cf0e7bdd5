# 🚀 Deployment Guide - Marketplace Updates

## 📁 Files cần upload:

### 1. Migration Scripts:
- ✅ `migrate_max_quantity_and_dynamic_types.py` - Migration chính
- ✅ `rollback_max_quantity_migration.py` - Rollback script (backup)

### 2. Application Files:
- ✅ `mip_system.py` - API updates (thêm category_id)
- ✅ `templates/marketplace/storefront.html` - UI improvements

## 🔧 Cách deploy:

### Step 1: Upload files
```bash
# Upload migration scripts
scp migrate_max_quantity_and_dynamic_types.py user@server:/path/to/app/
scp rollback_max_quantity_migration.py user@server:/path/to/app/

# Upload application files
scp mip_system.py user@server:/path/to/app/
scp templates/marketplace/storefront.html user@server:/path/to/app/templates/marketplace/
```

### Step 2: Run migration
```bash
# SSH vào server
ssh user@server

# Chạy migration
cd /path/to/app
python3 migrate_max_quantity_and_dynamic_types.py
```

### Step 3: Restart services
```bash
# Restart gunicorn
sudo systemctl restart gunicorn

# Reload nginx
sudo systemctl reload nginx
```

## 🧪 Test sau khi deploy:

### 1. Category Filtering:
- Vào `/marketplace`
- Chọn category từ dropdown → Should filter
- Click category card → Should filter + scroll
- Categories should stay visible

### 2. Cart Button:
- Add sản phẩm → Should see bounce + shake effects
- Hover cart → Should see lift effect
- Count badge should pulse

### 3. Max Quantity:
- Try add win_product nhiều lần → Should show warning
- Check cart limits

### 4. General:
- Search, sort, price display should work
- No JavaScript errors in console

## 🔄 Rollback (nếu cần):
```bash
python3 rollback_max_quantity_migration.py
```

## ✅ Success indicators:
- Category filtering works
- Cart button prominent with animations
- Max quantity validation works
- No console errors
- Mobile responsive

## 📊 Migration Results (Local Test):
```
🎉 MIGRATION COMPLETED SUCCESSFULLY!
==================================================
Total active products: 22
Products with max_quantity: 11
Single purchase products: 10
Bối Cảnh Live products: 3

✅ Features implemented:
   - max_quantity column added
   - Single purchase validation for win_product
   - Dynamic ProductType tabs
   - Metadata Builder compatibility
   - Performance index created
```

## 🔧 Migration Features:
- ✅ **Uses db_config.py** - Correct database credentials
- ✅ **Transaction safety** - Rollback on errors
- ✅ **Idempotent** - Safe to run multiple times
- ✅ **Detailed logging** - Clear progress reports
- ✅ **Verification** - Confirms changes applied
- ✅ **Rollback script** - Can revert if needed

**Deployment hoàn tất!** 🎉
