#!/usr/bin/env python3
"""
Fix Accounts table primary key and create missing tables
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_accounts_and_tables():
    """Fix Accounts primary key and create missing tables"""
    
    print("🔧 Fixing Accounts table and creating missing tables...")
    print("=" * 60)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Check Accounts table structure
        print("\n🔍 Checking Accounts table structure...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        columns = cursor.fetchall()
        print(f"📋 Accounts table has {len(columns)} columns:")
        for col in columns[:5]:  # Show first 5 columns
            print(f"  - {col[0]}: {col[1]} ({'NULL' if col[2] == 'YES' else 'NOT NULL'})")
        
        # Check if account_id has primary key
        cursor.execute('''
            SELECT constraint_name, constraint_type
            FROM information_schema.table_constraints
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            AND constraint_type = 'PRIMARY KEY';
        ''')
        
        pk_constraints = cursor.fetchall()
        if pk_constraints:
            print(f"✅ Primary key exists: {pk_constraints[0][0]}")
        else:
            print("❌ No primary key found on Accounts table")
            
            # Try to add primary key
            print("🔧 Adding primary key to account_id...")
            try:
                cursor.execute('ALTER TABLE "Accounts" ADD PRIMARY KEY (account_id);')
                print("✅ Primary key added successfully")
            except Exception as e:
                print(f"⚠️  Could not add primary key: {e}")
                
                # Check if account_id has duplicates
                cursor.execute('''
                    SELECT account_id, COUNT(*) 
                    FROM "Accounts" 
                    GROUP BY account_id 
                    HAVING COUNT(*) > 1;
                ''')
                
                duplicates = cursor.fetchall()
                if duplicates:
                    print(f"❌ Found {len(duplicates)} duplicate account_ids:")
                    for dup in duplicates[:3]:
                        print(f"  - account_id {dup[0]}: {dup[1]} records")
                    print("🔧 Need to fix duplicates first")
                    return False
        
        # Commit the primary key change
        conn.commit()
        
        # Now create PackageAccounts table without foreign key first
        print("\n🔧 Creating PackageAccounts table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "PackageAccounts" (
                id SERIAL PRIMARY KEY,
                package_id INTEGER,
                account_id INTEGER,
                is_sold BOOLEAN DEFAULT FALSE,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                sold_at TIMESTAMP,
                order_item_id INTEGER
            );
        ''')
        print("✅ PackageAccounts table created")
        
        # Create WarrantyRequests table without foreign keys first
        print("\n🔧 Creating WarrantyRequests table...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS "WarrantyRequests" (
                warranty_id SERIAL PRIMARY KEY,
                user_id INTEGER,
                order_item_id INTEGER,
                account_id INTEGER,
                reason VARCHAR(100) NOT NULL,
                description TEXT,
                status VARCHAR(20) DEFAULT 'pending',
                replacement_account_id INTEGER,
                admin_notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                resolved_at TIMESTAMP
            );
        ''')
        print("✅ WarrantyRequests table created")
        
        # Create indexes
        print("\n🔧 Creating indexes...")
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_package_accounts_package ON "PackageAccounts"(package_id);',
            'CREATE INDEX IF NOT EXISTS idx_package_accounts_account ON "PackageAccounts"(account_id);',
            'CREATE INDEX IF NOT EXISTS idx_warranty_requests_user ON "WarrantyRequests"(user_id);',
            'CREATE INDEX IF NOT EXISTS idx_warranty_requests_status ON "WarrantyRequests"(status);',
            'CREATE INDEX IF NOT EXISTS idx_products_category ON "Products"(category_id);',
            'CREATE INDEX IF NOT EXISTS idx_products_active ON "Products"(is_active);',
            'CREATE INDEX IF NOT EXISTS idx_orders_user ON "Orders"(user_id);',
            'CREATE INDEX IF NOT EXISTS idx_orders_status ON "Orders"(status);'
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                print(f"⚠️  Index issue: {e}")
        
        print("✅ Indexes created")
        
        # Insert coffee configs
        print("\n🔧 Inserting coffee configs...")
        coffee_configs = [
            ('enable_coffee_display', 'false'),
            ('coffee_cup_value', '50000'),
            ('coffee_cup_icon', '☕'),
            ('coffee_icon_type', 'emoji'),
            ('coffee_icon_width', '20'),
            ('coffee_icon_height', '20')
        ]
        
        for key, value in coffee_configs:
            cursor.execute('''
                INSERT INTO "Config" (config_key, config_value) 
                VALUES (%s, %s)
                ON CONFLICT (config_key) DO NOTHING;
            ''', (key, value))
        
        print("✅ Coffee configs inserted")
        
        # Commit all changes
        conn.commit()
        
        # Final validation
        print("\n🔍 Final validation...")
        tables = [
            "ProductCategories", "Products", "ProductFiles", "AccountPackages", 
            "PackageAccounts", "Orders", "OrderItems", "AFFPackages", 
            "UserSubscriptions", "ProductTypes", "WarrantyRequests", 
            "MarketplaceTransactions", "Config"
        ]
        
        for table in tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{table}";')
                count = cursor.fetchone()[0]
                print(f"✅ {table}: {count} records")
            except Exception as e:
                print(f"❌ {table}: {e}")
        
        # Check revenue_enabled column
        try:
            cursor.execute('SELECT COUNT(*) FROM "Accounts" WHERE revenue_enabled IS NOT NULL;')
            total_with_column = cursor.fetchone()[0]
            print(f"✅ Accounts with revenue_enabled column: {total_with_column}")
        except Exception as e:
            print(f"❌ revenue_enabled column: {e}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Fix completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_accounts_and_tables()
    sys.exit(0 if success else 1)
