#!/usr/bin/env python3
"""
Debug product-types API to understand the issue
"""

import sys
import os
import requests

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def debug_product_types_api():
    """Debug product-types API"""
    
    print("🔍 Debugging product-types API...")
    print("=" * 60)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check if ProductTypes table exists
        print("\n🔧 Step 1: Checking ProductTypes table...")
        
        cursor.execute('''
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name = 'ProductTypes';
        ''')
        
        table_exists = cursor.fetchone()
        if table_exists:
            print("  ✅ ProductTypes table exists")
            
            # Check table structure
            cursor.execute('''
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'ProductTypes' AND table_schema = 'public'
                ORDER BY ordinal_position;
            ''')
            
            columns = cursor.fetchall()
            print(f"  📋 Table structure ({len(columns)} columns):")
            for col in columns:
                nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
                default = f" DEFAULT {col[3]}" if col[3] else ""
                print(f"    - {col[0]}: {col[1]} {nullable}{default}")
            
            # Check data
            cursor.execute('SELECT COUNT(*) FROM "ProductTypes";')
            count = cursor.fetchone()[0]
            print(f"  📊 Total records: {count}")
            
            if count > 0:
                cursor.execute('SELECT type_id, name, description, is_active FROM "ProductTypes" LIMIT 5;')
                sample_data = cursor.fetchall()
                print(f"  📋 Sample data:")
                for row in sample_data:
                    print(f"    - ID {row[0]}: {row[1]} ({row[2][:50]}...) - Active: {row[3]}")
        else:
            print("  ❌ ProductTypes table does NOT exist")
        
        # Step 2: Check if API endpoint exists in code
        print("\n🔧 Step 2: Checking API endpoints in code...")
        
        # Read mip_system.py to check for product-types APIs
        try:
            with open('/var/www/sapmmo/mip_system.py', 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Search for product-types API endpoints
            api_patterns = [
                '/api/admin/marketplace/product-types',
                'api_admin_get_product_types',
                'api_admin_create_product_type',
                'def api_admin_get_product_types',
                'def api_admin_create_product_type'
            ]
            
            found_apis = []
            for pattern in api_patterns:
                if pattern in content:
                    found_apis.append(pattern)
            
            if found_apis:
                print(f"  ✅ Found API patterns in code:")
                for api in found_apis:
                    print(f"    - {api}")
            else:
                print(f"  ❌ No product-types API patterns found in code")
                
        except Exception as e:
            print(f"  ❌ Error reading mip_system.py: {e}")
        
        # Step 3: Check database connection function
        print("\n🔧 Step 3: Testing database connection function...")
        
        try:
            # Test get_db_connection function
            sys.path.append('/var/www/sapmmo')
            from mip_system import get_db_connection
            
            test_conn = get_db_connection()
            test_cursor = test_conn.cursor()
            test_cursor.execute('SELECT 1;')
            result = test_cursor.fetchone()
            test_conn.close()
            
            if result and result[0] == 1:
                print("  ✅ get_db_connection() function works correctly")
            else:
                print("  ❌ get_db_connection() function returns unexpected result")
                
        except Exception as e:
            print(f"  ❌ Error testing get_db_connection(): {e}")
        
        # Step 4: Check if there are any import errors
        print("\n🔧 Step 4: Checking imports...")
        
        try:
            import json
            print("  ✅ json module imported successfully")
        except Exception as e:
            print(f"  ❌ Error importing json: {e}")
        
        # Step 5: Test a simple query that the API would use
        print("\n🔧 Step 5: Testing API query...")
        
        if table_exists:
            try:
                # Test the exact query that the API would use
                query = '''
                    SELECT pt.type_id, pt.name, pt.description, pt.icon, pt.is_default,
                           pt.is_active, pt.metadata, pt.created_at, pt.updated_at,
                           (SELECT COUNT(*) FROM "Products" p WHERE p.product_type_id = pt.type_id AND (p.is_deleted = FALSE OR p.is_deleted IS NULL)) as product_count
                    FROM "ProductTypes" pt
                    WHERE 1=1
                    ORDER BY pt.is_default DESC, pt.name
                '''
                
                cursor.execute(query)
                rows = cursor.fetchall()
                
                print(f"  ✅ API query executed successfully - {len(rows)} rows returned")
                
                if rows:
                    print(f"  📋 Sample result:")
                    row = rows[0]
                    print(f"    - ID: {row[0]}, Name: {row[1]}, Active: {row[5]}, Product Count: {row[9]}")
                
            except Exception as e:
                print(f"  ❌ Error executing API query: {e}")
        
        # Step 6: Check Flask app routes
        print("\n🔧 Step 6: Checking Flask routes...")
        
        try:
            # Try to import the Flask app and check routes
            from mip_system import app
            
            routes = []
            for rule in app.url_map.iter_rules():
                if 'product-types' in rule.rule:
                    routes.append(f"{rule.rule} [{', '.join(rule.methods)}]")
            
            if routes:
                print(f"  ✅ Found product-types routes:")
                for route in routes:
                    print(f"    - {route}")
            else:
                print(f"  ❌ No product-types routes found in Flask app")
                
        except Exception as e:
            print(f"  ❌ Error checking Flask routes: {e}")
        
        # Step 7: Check server logs for any errors
        print("\n🔧 Step 7: Recommendations...")
        
        print("  📝 To debug further:")
        print("    1. Check server logs: sudo journalctl -u sapmmo -f")
        print("    2. Test API directly: curl -H 'Cookie: session=...' 'https://sapmmo.com/api/admin/marketplace/product-types'")
        print("    3. Check if migration was run: python3 simple_marketplace_migration.py")
        print("    4. Compare local vs server code differences")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Debug completed!")
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = debug_product_types_api()
    sys.exit(0 if success else 1)
