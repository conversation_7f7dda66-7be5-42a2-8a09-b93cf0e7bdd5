#!/usr/bin/env python3
"""
Add sold_to_user_id column to PackageAccounts table
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_sold_to_user_id_to_packageaccounts():
    """Add sold_to_user_id column to PackageAccounts table"""
    
    print("🚀 Adding sold_to_user_id column to PackageAccounts table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check if PackageAccounts table exists
        print("\n🔧 Step 1: Checking if PackageAccounts table exists...")
        
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'PackageAccounts'
            );
        ''')
        
        table_exists = cursor.fetchone()[0]
        print(f"📋 PackageAccounts table exists: {table_exists}")
        
        if not table_exists:
            print("❌ PackageAccounts table does not exist. Creating it first...")
            
            # Create PackageAccounts table if it doesn't exist
            cursor.execute('''
                CREATE SEQUENCE IF NOT EXISTS "PackageAccounts_id_seq"
                INCREMENT 1
                MINVALUE 1
                MAXVALUE **********
                START 1
                CACHE 1;
            ''')
            
            cursor.execute('''
                CREATE TABLE "PackageAccounts" (
                    "id" int4 NOT NULL DEFAULT nextval('"PackageAccounts_id_seq"'::regclass),
                    "package_id" int4 NOT NULL,
                    "account_id" int4 NOT NULL,
                    "is_sold" bool DEFAULT false,
                    "sold_at" timestamp(6),
                    "created_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP,
                    "updated_at" timestamp(6) DEFAULT CURRENT_TIMESTAMP
                );
            ''')
            
            cursor.execute('''
                ALTER TABLE "PackageAccounts" ADD CONSTRAINT "PackageAccounts_pkey" PRIMARY KEY ("id");
            ''')
            
            print("  ✅ Created PackageAccounts table")
        
        # Step 2: Check current structure
        print("\n🔧 Step 2: Checking current PackageAccounts table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'PackageAccounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Current columns: {existing_column_names}")
        
        # Step 3: Add sold_to_user_id column if not exists
        if 'sold_to_user_id' not in existing_column_names:
            print("\n🔧 Step 3: Adding sold_to_user_id column...")
            
            cursor.execute('''
                ALTER TABLE "PackageAccounts" 
                ADD COLUMN sold_to_user_id int4;
            ''')
            
            print("  ✅ Added sold_to_user_id column (int4)")
        else:
            print("\n✅ Step 3: sold_to_user_id column already exists")
        
        # Step 4: Add comment
        print("\n🔧 Step 4: Adding column comment...")
        
        cursor.execute('''
            COMMENT ON COLUMN "PackageAccounts"."sold_to_user_id" IS 'ID của user đã mua account này';
        ''')
        
        print("  ✅ Added comment for sold_to_user_id column")
        
        # Step 5: Create index for performance
        print("\n🔧 Step 5: Creating index...")
        
        try:
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_packageaccounts_sold_to_user_id 
                ON "PackageAccounts"(sold_to_user_id) 
                WHERE sold_to_user_id IS NOT NULL;
            ''')
            print("  ✅ Created index: idx_packageaccounts_sold_to_user_id")
        except Exception as e:
            print(f"  ⚠️  Index creation: {e}")
        
        # Step 6: Add foreign key if Users table exists
        print("\n🔧 Step 6: Adding foreign key constraint...")
        
        # Check if Users table exists
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Users'
            );
        ''')
        users_exists = cursor.fetchone()[0]
        
        if users_exists:
            try:
                cursor.execute('''
                    ALTER TABLE "PackageAccounts" ADD CONSTRAINT "PackageAccounts_sold_to_user_id_fkey" 
                    FOREIGN KEY ("sold_to_user_id") REFERENCES "Users" ("user_id") ON DELETE SET NULL ON UPDATE NO ACTION;
                ''')
                print("  ✅ Added foreign key to Users table")
            except Exception as e:
                print(f"  ⚠️  Foreign key creation: {e}")
        else:
            print("  ⚠️  Users table not found, skipping foreign key")
        
        # Step 7: Add other missing foreign keys if needed
        print("\n🔧 Step 7: Adding other foreign keys...")
        
        # Check if AccountPackages table exists for package_id FK
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'AccountPackages'
            );
        ''')
        packages_exists = cursor.fetchone()[0]
        
        if packages_exists:
            try:
                cursor.execute('''
                    ALTER TABLE "PackageAccounts" ADD CONSTRAINT "PackageAccounts_package_id_fkey" 
                    FOREIGN KEY ("package_id") REFERENCES "AccountPackages" ("package_id") ON DELETE CASCADE ON UPDATE NO ACTION;
                ''')
                print("  ✅ Added foreign key to AccountPackages table")
            except Exception as e:
                print(f"  ⚠️  Package foreign key: {e}")
        
        # Check if Accounts table exists for account_id FK
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Accounts'
            );
        ''')
        accounts_exists = cursor.fetchone()[0]
        
        if accounts_exists:
            try:
                cursor.execute('''
                    ALTER TABLE "PackageAccounts" ADD CONSTRAINT "PackageAccounts_account_id_fkey" 
                    FOREIGN KEY ("account_id") REFERENCES "Accounts" ("account_id") ON DELETE CASCADE ON UPDATE NO ACTION;
                ''')
                print("  ✅ Added foreign key to Accounts table")
            except Exception as e:
                print(f"  ⚠️  Account foreign key: {e}")
        
        # Step 8: Verify final structure
        print("\n🔧 Step 8: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'PackageAccounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Final PackageAccounts columns ({len(final_columns)} total):")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 9: Test the problematic query
        print("\n🔧 Step 9: Testing the problematic query...")
        
        try:
            # Test the query structure that was failing
            cursor.execute('''
                SELECT pa.id, pa.account_id, pa.is_sold, pa.sold_to_user_id, u.username
                FROM "PackageAccounts" pa
                LEFT JOIN "Users" u ON pa.sold_to_user_id = u.user_id
                WHERE pa.package_id = 999999
                LIMIT 1;
            ''')
            print("  ✅ Query test successful")
            
        except Exception as e:
            print(f"  ❌ Query test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 sold_to_user_id column added successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test edit product with account type")
        print("   3. Test account sales tracking")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_sold_to_user_id_to_packageaccounts()
    sys.exit(0 if success else 1)
