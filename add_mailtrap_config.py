#!/usr/bin/env python3
"""
Add Mailtrap configuration to database
"""

import psycopg2
import psycopg2.extras
from db_config import PG_CONFIG

def get_db_connection():
    """Kết nối database PostgreSQL từ config"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def add_mailtrap_config():
    """Thêm Mailtrap config vào SystemConfig table"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("📧 Adding Mailtrap Configuration")
        print("=" * 50)
        
        # Mailtrap configurations
        mailtrap_configs = [
            {
                'key': 'mailtrap_api_token',
                'value': 'YOUR_MAILTRAP_API_TOKEN_HERE',
                'description': 'Mailtrap API Token for sending emails',
                'category': 'email'
            },
            {
                'key': 'mailtrap_sender_email',
                'value': '<EMAIL>',
                'description': 'Sender email address for Mailtrap',
                'category': 'email'
            },
            {
                'key': 'mailtrap_sender_name',
                'value': 'MIP System',
                'description': 'Sender name for emails',
                'category': 'email'
            },
            {
                'key': 'email_verification_enabled',
                'value': 'true',
                'description': 'Enable email verification for new registrations',
                'category': 'email'
            },
            {
                'key': 'otp_expiry_minutes',
                'value': '15',
                'description': 'OTP expiry time in minutes',
                'category': 'email'
            },
            {
                'key': 'max_otp_attempts',
                'value': '3',
                'description': 'Maximum OTP verification attempts',
                'category': 'email'
            }
        ]
        
        added_count = 0
        
        for config in mailtrap_configs:
            # Check if config already exists
            cursor.execute('SELECT id FROM "SystemConfig" WHERE config_key = %s', (config['key'],))
            existing = cursor.fetchone()

            if not existing:
                cursor.execute('''
                    INSERT INTO "SystemConfig" (config_key, config_value, description, created_at, updated_at)
                    VALUES (%s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                ''', (config['key'], config['value'], config['description']))
                
                print(f"   ✅ Added: {config['key']}")
                added_count += 1
            else:
                print(f"   ℹ️ Exists: {config['key']}")
        
        conn.commit()
        
        if added_count > 0:
            print(f"\n✅ Added {added_count} new email configurations")
        else:
            print(f"\n✅ All email configurations already exist")
        
        # Show current email configs
        print(f"\n📋 Current Email Configurations:")
        cursor.execute('''
            SELECT config_key, config_value, description
            FROM "SystemConfig"
            WHERE config_key LIKE '%mail%' OR config_key LIKE '%email%' OR config_key LIKE '%otp%'
            ORDER BY config_key
        ''')
        
        configs = cursor.fetchall()
        for config in configs:
            value = config['config_value']
            if 'token' in config['config_key'].lower():
                value = value[:10] + '...' if len(value) > 10 else value
            print(f"   {config['config_key']}: {value}")
            print(f"      → {config['description']}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error adding Mailtrap config: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        return False

def main():
    """Main function"""
    print("📧 Mailtrap Configuration Setup")
    print("=" * 60)
    
    success = add_mailtrap_config()
    
    if success:
        print("\n🎉 Mailtrap configuration setup completed!")
        print("\n📋 Next steps:")
        print("  1. Update mailtrap_api_token with your actual token")
        print("  2. Update mailtrap_sender_email with your domain email")
        print("  3. Install mailtrap library: pip install mailtrap")
        print("  4. Test email sending functionality")
        print("\n💡 To update config values:")
        print("  UPDATE \"SystemConfig\" SET config_value = 'your_token' WHERE config_key = 'mailtrap_api_token';")
    else:
        print("\n❌ Setup failed!")

if __name__ == "__main__":
    main()
