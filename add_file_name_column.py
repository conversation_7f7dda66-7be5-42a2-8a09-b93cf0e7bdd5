#!/usr/bin/env python3
"""
Add file_name column to ProductFiles table
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_file_name_column():
    """Add file_name column to ProductFiles table"""
    
    print("🚀 Adding file_name column to ProductFiles table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current ProductFiles table structure
        print("\n🔧 Step 1: Checking current ProductFiles table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductFiles' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Current columns: {existing_column_names}")
        
        # Step 2: Add file_name column if not exists
        if 'file_name' not in existing_column_names:
            print("\n🔧 Step 2: Adding file_name column...")
            
            cursor.execute('''
                ALTER TABLE "ProductFiles" 
                ADD COLUMN file_name VARCHAR(255);
            ''')
            
            print("  ✅ Added file_name column (VARCHAR(255))")
        else:
            print("\n✅ Step 2: file_name column already exists")
        
        # Step 3: Update existing records with file_name based on filename
        print("\n🔧 Step 3: Updating existing records...")
        
        # Set file_name = filename for existing records where file_name is NULL
        cursor.execute('''
            UPDATE "ProductFiles" 
            SET file_name = filename 
            WHERE file_name IS NULL AND filename IS NOT NULL;
        ''')
        
        updated_rows = cursor.rowcount
        print(f"  ✅ Updated {updated_rows} existing records: file_name = filename")
        
        # Step 4: Create index for performance
        print("\n🔧 Step 4: Creating index...")
        
        try:
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_product_files_file_name 
                ON "ProductFiles"(file_name);
            ''')
            print("  ✅ Created index: idx_product_files_file_name")
        except Exception as e:
            print(f"  ⚠️  Index creation: {e}")
        
        # Step 5: Verify final structure
        print("\n🔧 Step 5: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductFiles' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        print(f"📋 Final ProductFiles columns:")
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        # Step 6: Test data
        print("\n🔧 Step 6: Testing data...")
        
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                COUNT(file_name) as with_file_name,
                COUNT(filename) as with_filename,
                COUNT(*) - COUNT(file_name) as without_file_name
            FROM "ProductFiles";
        ''')
        
        stats = cursor.fetchone()
        print(f"📊 ProductFiles statistics:")
        print(f"  - Total files: {stats[0]}")
        print(f"  - With file_name: {stats[1]}")
        print(f"  - With filename: {stats[2]}")
        print(f"  - Without file_name: {stats[3]}")
        
        # Step 7: Test problematic query
        print("\n🔧 Step 7: Testing problematic query...")
        
        try:
            # Test the query that was failing
            cursor.execute('''
                SELECT product_id, filename, file_name, original_name, file_size, 
                       file_type, upload_date, is_preview, description
                FROM "ProductFiles" 
                WHERE product_id IS NOT NULL
                LIMIT 3;
            ''')
            
            test_rows = cursor.fetchall()
            print(f"  ✅ Query test successful - {len(test_rows)} rows returned")
            
            for row in test_rows:
                print(f"    - Product {row[0]}: filename={row[1]}, file_name={row[2]}")
                
        except Exception as e:
            print(f"  ❌ Query test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Migration completed successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test product file upload functionality")
        print("   3. Test product file management")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_file_name_column()
    sys.exit(0 if success else 1)
