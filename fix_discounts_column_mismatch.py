#!/usr/bin/env python3
"""
Fix column name mismatches in Discounts table
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_column_mismatches():
    """Fix column name mismatches between code and database"""
    
    print("🔧 Fixing Discounts table column mismatches...")
    print("=" * 60)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Check current columns
        cursor.execute('''
            SELECT column_name
            FROM information_schema.columns 
            WHERE table_name = 'Discounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        current_columns = [col[0] for col in cursor.fetchall()]
        print(f"📋 Current columns: {current_columns}")
        
        # Column mappings that code expects vs what we have
        column_mappings = [
            ('type', 'discount_type'),  # Code expects discount_type, we have type
            ('value', 'value'),  # This is correct
            ('min_amount', 'min_order_value'),  # Code expects min_order_value
            ('limit_usage', 'usage_limit'),  # Code expects usage_limit
            ('valid_from', 'start_date'),  # Code expects start_date
            ('valid_to', 'end_date')  # Code expects end_date
        ]
        
        # Rename columns to match what code expects
        for current_name, expected_name in column_mappings:
            if current_name in current_columns and expected_name not in current_columns:
                print(f"🔧 Renaming {current_name} to {expected_name}...")
                try:
                    cursor.execute(f'ALTER TABLE "Discounts" RENAME COLUMN {current_name} TO {expected_name};')
                    print(f"✅ Renamed {current_name} to {expected_name}")
                except Exception as e:
                    print(f"⚠️  Could not rename {current_name}: {e}")
            elif expected_name in current_columns:
                print(f"✅ Column {expected_name} already exists")
        
        # Add any missing columns that code expects
        missing_columns = {
            'discount_type': 'VARCHAR(20) NOT NULL DEFAULT \'percentage\'',
            'min_order_value': 'DECIMAL(15,2) DEFAULT 0',
            'usage_limit': 'INTEGER DEFAULT NULL',
            'start_date': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'end_date': 'TIMESTAMP'
        }
        
        # Re-check columns after rename
        cursor.execute('''
            SELECT column_name
            FROM information_schema.columns 
            WHERE table_name = 'Discounts' AND table_schema = 'public';
        ''')
        
        updated_columns = [col[0] for col in cursor.fetchall()]
        
        for col_name, col_def in missing_columns.items():
            if col_name not in updated_columns:
                print(f"🔧 Adding missing column {col_name}...")
                try:
                    cursor.execute(f'ALTER TABLE "Discounts" ADD COLUMN {col_name} {col_def};')
                    print(f"✅ Added column {col_name}")
                    
                    # Copy data from old column if it exists
                    old_col_map = {
                        'discount_type': 'type',
                        'min_order_value': 'min_amount', 
                        'usage_limit': 'limit_usage',
                        'start_date': 'valid_from',
                        'end_date': 'valid_to'
                    }
                    
                    if col_name in old_col_map and old_col_map[col_name] in updated_columns:
                        old_col = old_col_map[col_name]
                        print(f"🔧 Copying data from {old_col} to {col_name}...")
                        cursor.execute(f'UPDATE "Discounts" SET {col_name} = {old_col};')
                        print(f"✅ Data copied from {old_col}")
                        
                except Exception as e:
                    print(f"⚠️  Could not add {col_name}: {e}")
        
        # Commit changes
        conn.commit()
        
        # Test the exact queries that are failing
        print("\n🔍 Testing queries that were failing...")
        
        # Test 1: List discounts query
        try:
            cursor.execute('''
                SELECT d.discount_id, d.code, d.description, d.discount_type, d.value,
                       d.min_order_value, d.usage_limit, d.used_count, d.is_active,
                       d.start_date, d.end_date, d.created_at
                FROM "Discounts" d
                WHERE d.is_active = TRUE
                ORDER BY d.created_at DESC
                LIMIT 3;
            ''')
            
            results = cursor.fetchall()
            print(f"✅ List query successful - found {len(results)} discounts")
            
        except Exception as e:
            print(f"❌ List query failed: {e}")
        
        # Test 2: Create discount query
        try:
            cursor.execute('''
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'Discounts' 
                AND column_name IN ('code', 'description', 'discount_type', 'value', 'min_order_value', 'usage_limit', 'start_date', 'end_date');
            ''')
            
            required_cols = [col[0] for col in cursor.fetchall()]
            expected_cols = ['code', 'description', 'discount_type', 'value', 'min_order_value', 'usage_limit', 'start_date', 'end_date']
            
            missing_cols = [col for col in expected_cols if col not in required_cols]
            if missing_cols:
                print(f"❌ Create query will fail - missing columns: {missing_cols}")
            else:
                print("✅ Create query should work - all required columns exist")
                
        except Exception as e:
            print(f"❌ Create query test failed: {e}")
        
        # Show final table structure
        print("\n📋 Final Discounts table structure:")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Discounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        for col in final_columns:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col[3]}" if col[3] else ""
            print(f"  - {col[0]}: {col[1]} {nullable}{default}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Column mismatches fixed!")
        print("📝 Next steps:")
        print("   1. Restart sapmmo service")
        print("   2. Test /admin/marketplace/discounts")
        print("   3. Test creating new discount codes")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_column_mismatches()
    sys.exit(0 if success else 1)
