#!/usr/bin/env python3
"""
Check warranty buttons and files on server
"""

import sys
import os
import re

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def check_warranty_buttons_server():
    """Check warranty buttons and files on server"""
    
    print("🚀 Checking warranty buttons on server...")
    print("=" * 70)
    
    try:
        # Check if template files exist
        print("\n🔧 Step 1: Checking template files existence...")
        
        template_files = [
            "templates/admin/warranty_management.html",
            "templates/marketplace/warranty.html",
            "templates/warranty_requests.html",
            "templates/warranty_requests_coreui.html"
        ]
        
        existing_files = []
        missing_files = []
        
        for file_path in template_files:
            full_path = os.path.join(APP_DIR, file_path)
            if os.path.exists(full_path):
                existing_files.append(file_path)
                print(f"✅ Found: {file_path}")
                
                # Get file size and modification time
                stat = os.stat(full_path)
                size = stat.st_size
                mtime = stat.st_mtime
                print(f"   Size: {size} bytes, Modified: {mtime}")
            else:
                missing_files.append(file_path)
                print(f"❌ Missing: {file_path}")
        
        # Check admin warranty file content
        print(f"\n🔧 Step 2: Checking admin warranty file content...")
        
        admin_file = os.path.join(APP_DIR, "templates/admin/warranty_management.html")
        if os.path.exists(admin_file):
            with open(admin_file, 'r', encoding='utf-8') as f:
                admin_content = f.read()
            
            print(f"📋 Admin file size: {len(admin_content)} characters")
            
            # Check for key functions and buttons
            key_elements = [
                ('getActionButtons function', r'function getActionButtons'),
                ('approve button', r'btn[^>]*approve|cil-check'),
                ('reject button', r'btn[^>]*reject|cil-x'),
                ('approveWarranty function', r'function approveWarranty'),
                ('rejectWarranty function', r'function rejectWarranty')
            ]
            
            for element_name, pattern in key_elements:
                matches = re.findall(pattern, admin_content, re.IGNORECASE)
                print(f"  📋 {element_name}: {'✅ Found' if matches else '❌ Missing'} ({len(matches)} matches)")
        
        # Check user warranty file content
        print(f"\n🔧 Step 3: Checking user warranty file content...")
        
        user_file = os.path.join(APP_DIR, "templates/marketplace/warranty.html")
        if os.path.exists(user_file):
            with open(user_file, 'r', encoding='utf-8') as f:
                user_content = f.read()
            
            print(f"📋 User file size: {len(user_content)} characters")
            
            # Check for key functions and buttons
            user_elements = [
                ('cancel button', r'btn[^>]*cancel|fas fa-times.*Hủy'),
                ('cancelWarrantyRequest function', r'function cancelWarrantyRequest'),
                ('actionButtons creation', r'let actionButtons'),
                ('Pending status check', r'request\.status === [\'"]Pending[\'"]')
            ]
            
            for element_name, pattern in user_elements:
                matches = re.findall(pattern, user_content, re.IGNORECASE)
                print(f"  📋 {element_name}: {'✅ Found' if matches else '❌ Missing'} ({len(matches)} matches)")
        
        # Check warranty routes in mip_system.py
        print(f"\n🔧 Step 4: Checking warranty routes...")
        
        mip_file = os.path.join(APP_DIR, "mip_system.py")
        if os.path.exists(mip_file):
            with open(mip_file, 'r', encoding='utf-8') as f:
                routes_content = f.read()
            
            # Check for warranty routes
            warranty_routes = [
                ('/admin/marketplace/warranty', r'/admin/marketplace/warranty'),
                ('/marketplace/warranty', r'/marketplace/warranty'),
                ('warranty approve API', r'/api.*warranty.*approve'),
                ('warranty reject API', r'/api.*warranty.*reject'),
                ('warranty cancel API', r'/api.*warranty.*cancel')
            ]
            
            for route_name, pattern in warranty_routes:
                matches = re.findall(pattern, routes_content, re.IGNORECASE)
                print(f"  📋 {route_name}: {'✅ Found' if matches else '❌ Missing'} ({len(matches)} matches)")
        
        # Check directory structure
        print(f"\n🔧 Step 5: Checking directory structure...")
        
        directories = [
            "templates/admin",
            "templates/marketplace",
            "templates/marketplace/admin"
        ]
        
        for dir_path in directories:
            full_dir = os.path.join(APP_DIR, dir_path)
            if os.path.exists(full_dir):
                files = os.listdir(full_dir)
                print(f"✅ {dir_path}: {len(files)} files")
                warranty_files = [f for f in files if 'warranty' in f.lower()]
                if warranty_files:
                    print(f"   Warranty files: {warranty_files}")
            else:
                print(f"❌ {dir_path}: Directory not found")
        
        # Check static files (CSS/JS)
        print(f"\n🔧 Step 6: Checking static files...")
        
        static_dirs = ["static", "static/css", "static/js"]
        for static_dir in static_dirs:
            full_static = os.path.join(APP_DIR, static_dir)
            if os.path.exists(full_static):
                files = os.listdir(full_static)
                print(f"✅ {static_dir}: {len(files)} files")
            else:
                print(f"❌ {static_dir}: Directory not found")
        
        # Summary and recommendations
        print(f"\n🔧 Step 7: Summary and recommendations...")
        
        if missing_files:
            print(f"❌ Missing files that need to be uploaded:")
            for file in missing_files:
                print(f"   - {file}")
        else:
            print(f"✅ All template files exist")
        
        print(f"\n📝 Troubleshooting steps:")
        print(f"   1. Check browser console for JavaScript errors")
        print(f"   2. Clear browser cache and reload")
        print(f"   3. Check if warranty API endpoints are working")
        print(f"   4. Verify user permissions for warranty actions")
        print(f"   5. Check if CoreUI CSS/JS files are loaded properly")
        
        return True
        
    except Exception as e:
        print(f"❌ Check failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = check_warranty_buttons_server()
    sys.exit(0 if success else 1)
