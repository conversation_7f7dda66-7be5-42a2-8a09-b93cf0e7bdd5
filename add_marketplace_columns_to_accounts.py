#!/usr/bin/env python3
"""
Add marketplace columns to Accounts table
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_marketplace_columns_to_accounts():
    """Add marketplace columns to Accounts table"""
    
    print("🚀 Adding marketplace columns to Accounts table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current Accounts table structure
        print("\n🔧 Step 1: Checking current Accounts table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Current columns count: {len(existing_column_names)}")
        
        # Step 2: Define marketplace columns to add
        marketplace_columns = [
            ('email', 'varchar(255)', 'Email của account'),
            ('full_email', 'varchar(255)', 'Full email của account'),
            ('full_name', 'varchar(255)', 'Tên đầy đủ của account'),
            ('bio', 'text', 'Bio/mô tả của account'),
            ('avatar_url', 'varchar(500)', 'URL avatar của account'),
            ('like_count', 'int8', 'Số lượng likes'),
            ('video_count', 'int4', 'Số lượng videos'),
            ('following_count', 'int4', 'Số lượng following'),
            ('verification_status', 'varchar(50)', 'Trạng thái xác minh'),
            ('account_age_days', 'int4', 'Tuổi account (ngày)'),
            ('last_post_date', 'timestamp(6)', 'Ngày post cuối cùng'),
            ('engagement_rate', 'decimal(5,2)', 'Tỷ lệ tương tác (%)'),
            ('category', 'varchar(100)', 'Danh mục account'),
            ('country', 'varchar(100)', 'Quốc gia'),
            ('language', 'varchar(50)', 'Ngôn ngữ chính'),
            ('price', 'decimal(10,2)', 'Giá bán account'),
            ('original_price', 'decimal(10,2)', 'Giá gốc'),
            ('discount_percent', 'decimal(5,2)', 'Phần trăm giảm giá'),
            ('quality_score', 'decimal(3,1)', 'Điểm chất lượng (1-10)'),
            ('notes', 'text', 'Ghi chú thêm'),
            ('created_date', 'timestamp(6)', 'Ngày tạo account'),
            ('updated_date', 'timestamp(6)', 'Ngày cập nhật cuối'),
            ('sold_date', 'timestamp(6)', 'Ngày bán'),
            ('sold_to_user_id', 'int4', 'ID user đã mua'),
            ('sold_price', 'decimal(10,2)', 'Giá đã bán'),
            ('marketplace_status', 'varchar(20)', 'Trạng thái marketplace'),
            ('package_id', 'int4', 'ID package chứa account này'),
            ('is_featured', 'bool', 'Account nổi bật'),
            ('is_verified', 'bool', 'Account đã xác minh'),
            ('is_active', 'bool', 'Account đang hoạt động'),
            ('tags', 'text', 'Tags/nhãn của account'),
            ('external_id', 'varchar(100)', 'ID ngoài (TikTok ID)'),
            ('cookie_data', 'text', 'Dữ liệu cookie'),
            ('login_data', 'text', 'Dữ liệu đăng nhập'),
            ('additional_info', 'jsonb', 'Thông tin bổ sung (JSON)')
        ]
        
        # Step 3: Add missing columns
        print(f"\n🔧 Step 3: Adding missing marketplace columns...")
        
        added_count = 0
        for col_name, col_type, col_comment in marketplace_columns:
            if col_name not in existing_column_names:
                try:
                    # Add column
                    cursor.execute(f'ALTER TABLE "Accounts" ADD COLUMN {col_name} {col_type};')
                    
                    # Add comment
                    cursor.execute(f'COMMENT ON COLUMN "Accounts"."{col_name}" IS \'{col_comment}\';')
                    
                    print(f"  ✅ Added: {col_name} ({col_type})")
                    added_count += 1
                    
                except Exception as e:
                    print(f"  ❌ Failed to add {col_name}: {e}")
            else:
                print(f"  ⏭️  Exists: {col_name}")
        
        print(f"\n📊 Added {added_count} new columns")
        
        # Step 4: Create indexes for performance
        print(f"\n🔧 Step 4: Creating indexes...")
        
        indexes = [
            ('idx_accounts_marketplace_status', 'marketplace_status'),
            ('idx_accounts_package_id', 'package_id'),
            ('idx_accounts_sold_to_user_id', 'sold_to_user_id'),
            ('idx_accounts_is_sold', 'is_sold'),
            ('idx_accounts_price', 'price'),
            ('idx_accounts_follower_count', 'follower_count'),
            ('idx_accounts_like_count', 'like_count'),
            ('idx_accounts_engagement_rate', 'engagement_rate'),
            ('idx_accounts_quality_score', 'quality_score'),
            ('idx_accounts_country', 'country'),
            ('idx_accounts_category', 'category'),
            ('idx_accounts_is_featured', 'is_featured'),
            ('idx_accounts_is_verified', 'is_verified'),
            ('idx_accounts_created_date', 'created_date'),
            ('idx_accounts_sold_date', 'sold_date')
        ]
        
        for index_name, column in indexes:
            try:
                cursor.execute(f'CREATE INDEX IF NOT EXISTS {index_name} ON "Accounts"({column}) WHERE {column} IS NOT NULL;')
                print(f"  ✅ Created index: {index_name}")
            except Exception as e:
                print(f"  ⚠️  Index {index_name}: {e}")
        
        # Step 5: Add foreign key constraints
        print(f"\n🔧 Step 5: Adding foreign key constraints...")
        
        # Check if Users table exists for sold_to_user_id FK
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = 'Users'
            );
        ''')
        users_exists = cursor.fetchone()[0]
        
        if users_exists:
            try:
                cursor.execute('''
                    ALTER TABLE "Accounts" ADD CONSTRAINT "Accounts_sold_to_user_id_fkey" 
                    FOREIGN KEY ("sold_to_user_id") REFERENCES "Users" ("user_id") ON DELETE SET NULL ON UPDATE NO ACTION;
                ''')
                print("  ✅ Added foreign key: sold_to_user_id → Users")
            except Exception as e:
                print(f"  ⚠️  FK sold_to_user_id: {e}")
        
        # Step 6: Set default values for important columns
        print(f"\n🔧 Step 6: Setting default values...")
        
        default_updates = [
            ("marketplace_status = 'available'", "marketplace_status IS NULL"),
            ("is_featured = false", "is_featured IS NULL"),
            ("is_verified = false", "is_verified IS NULL"),
            ("is_active = true", "is_active IS NULL"),
            ("quality_score = 5.0", "quality_score IS NULL"),
            ("engagement_rate = 0.0", "engagement_rate IS NULL"),
            ("like_count = 0", "like_count IS NULL"),
            ("video_count = 0", "video_count IS NULL"),
            ("following_count = 0", "following_count IS NULL")
        ]
        
        for update_set, update_where in default_updates:
            try:
                cursor.execute(f'UPDATE "Accounts" SET {update_set} WHERE {update_where};')
                updated = cursor.rowcount
                if updated > 0:
                    print(f"  ✅ Updated {updated} records: {update_set}")
            except Exception as e:
                print(f"  ⚠️  Default update failed: {e}")
        
        # Step 7: Verify final structure
        print(f"\n🔧 Step 7: Verifying final structure...")
        
        cursor.execute('''
            SELECT COUNT(*) 
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public';
        ''')
        
        final_count = cursor.fetchone()[0]
        print(f"📊 Final Accounts table columns: {final_count}")
        
        # Show sample of new columns
        cursor.execute('''
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            AND column_name IN ('email', 'full_email', 'marketplace_status', 'package_id', 'like_count')
            ORDER BY column_name;
        ''')
        
        sample_cols = cursor.fetchall()
        print(f"📋 Sample new columns: {[col[0] for col in sample_cols]}")
        
        # Step 8: Test query that was failing
        print(f"\n🔧 Step 8: Testing the problematic query...")
        
        try:
            cursor.execute('''
                SELECT account_id, account_name, follower_count, 
                       like_count, email, full_email, full_name
                FROM "Accounts" 
                WHERE is_deleted = 0 
                LIMIT 1;
            ''')
            
            test_result = cursor.fetchone()
            if test_result:
                print("  ✅ Query test successful")
                print(f"  📋 Sample: ID={test_result[0]}, Name={test_result[1]}, Followers={test_result[2]}")
            else:
                print("  ✅ Query syntax OK (no data)")
                
        except Exception as e:
            print(f"  ❌ Query test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Marketplace columns added successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test marketplace functionality")
        print("   3. Test account listing and purchasing")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_marketplace_columns_to_accounts()
    sys.exit(0 if success else 1)
