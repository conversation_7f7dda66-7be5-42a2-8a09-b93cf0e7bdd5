<!-- Metadata Builder UI - Thay thế textarea JSON -->
<div class="mb-3">
    <label class="form-label"><PERSON><PERSON><PERSON> hình sản phẩm</label>
    
    <!-- Product Behavior -->
    <div class="card mb-3">
        <div class="card-header">
            <h6 class="mb-0"><i class="cil-settings"></i> Hành vi sản phẩm</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="requires_accounts" onchange="updateMetadata()">
                        <label class="form-check-label" for="requires_accounts">
                            <strong>Cần gán Accounts</strong>
                            <br><small class="text-muted">Sản phẩm sẽ gán accounts TikTok cho khách hàng</small>
                        </label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="requires_files" onchange="updateMetadata()">
                        <label class="form-check-label" for="requires_files">
                            <strong>Cần upload Files</strong>
                            <br><small class="text-muted">Sản phẩm có files để khách hàng download</small>
                        </label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="requires_video_links" onchange="updateMetadata()">
                        <label class="form-check-label" for="requires_video_links">
                            <strong>Cần gán Video Links</strong>
                            <br><small class="text-muted">Sản phẩm sẽ gán video links cho khách hàng</small>
                        </label>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="subscription_based" onchange="updateMetadata()">
                        <label class="form-check-label" for="subscription_based">
                            <strong>Thanh toán theo tháng</strong>
                            <br><small class="text-muted">Sản phẩm subscription (AFF Package)</small>
                        </label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="warranty_enabled" onchange="updateMetadata()">
                        <label class="form-check-label" for="warranty_enabled">
                            <strong>Có bảo hành</strong>
                            <br><small class="text-muted">Sản phẩm có chế độ bảo hành</small>
                        </label>
                    </div>
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="downloadable" onchange="updateMetadata()">
                        <label class="form-check-label" for="downloadable">
                            <strong>Có thể download</strong>
                            <br><small class="text-muted">Files có thể download trực tiếp</small>
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Course Settings -->
    <div class="card mb-3" id="courseSettings" style="display: none;">
        <div class="card-header">
            <h6 class="mb-0"><i class="cil-education"></i> Cài đặt khóa học</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Loại khóa học</label>
                        <select class="form-select" id="course_type" onchange="updateMetadata()">
                            <option value="">Chọn loại</option>
                            <option value="basic">Cơ bản</option>
                            <option value="advanced">Nâng cao</option>
                            <option value="premium">Premium</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Thời lượng (tuần)</label>
                        <input type="number" class="form-control" id="duration_weeks" min="1" max="52" onchange="updateMetadata()">
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- AFF Package Settings -->
    <div class="card mb-3" id="affSettings" style="display: none;">
        <div class="card-header">
            <h6 class="mb-0"><i class="cil-chart-pie"></i> Cài đặt AFF Package</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" id="account_limit" onchange="updateMetadata()">
                        <label class="form-check-label" for="account_limit">
                            Có giới hạn số accounts
                        </label>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Số accounts tối đa</label>
                        <input type="number" class="form-control" id="max_accounts" min="1" onchange="updateMetadata()">
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Formula Settings -->
    <div class="card mb-3" id="formulaSettings" style="display: none;">
        <div class="card-header">
            <h6 class="mb-0"><i class="cil-chart-line"></i> Cài đặt Formula</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label">Loại formula</label>
                        <select class="form-select" id="formula_type" onchange="updateMetadata()">
                            <option value="">Chọn loại</option>
                            <option value="channel_growth">Nuôi kênh</option>
                            <option value="ai_video">Video AI</option>
                            <option value="content_creation">Tạo nội dung</option>
                            <option value="monetization">Kiếm tiền</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-check mt-4">
                        <input class="form-check-input" type="checkbox" id="includes_templates" onchange="updateMetadata()">
                        <label class="form-check-label" for="includes_templates">
                            Bao gồm templates
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includes_tools" onchange="updateMetadata()">
                        <label class="form-check-label" for="includes_tools">
                            Bao gồm tools
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- JSON Preview -->
    <div class="card">
        <div class="card-header">
            <h6 class="mb-0"><i class="cil-code"></i> JSON Preview</h6>
        </div>
        <div class="card-body">
            <textarea class="form-control" name="metadata" id="metadataJson" rows="4" readonly style="font-family: monospace; font-size: 12px;"></textarea>
            <div class="form-text">JSON được tạo tự động từ các cài đặt trên</div>
        </div>
    </div>
</div>

<script>
// Metadata Builder Logic
function updateMetadata() {
    const metadata = {};
    
    // Basic behaviors
    if (document.getElementById('requires_accounts').checked) {
        metadata.requires_accounts = true;
    }
    if (document.getElementById('requires_files').checked) {
        metadata.requires_files = true;
    }
    if (document.getElementById('requires_video_links').checked) {
        metadata.requires_video_links = true;
    }
    if (document.getElementById('subscription_based').checked) {
        metadata.subscription_based = true;
    }
    if (document.getElementById('warranty_enabled').checked) {
        metadata.warranty_enabled = true;
    }
    if (document.getElementById('downloadable').checked) {
        metadata.downloadable = true;
    }
    
    // Course settings
    const courseType = document.getElementById('course_type').value;
    if (courseType) {
        metadata.course_type = courseType;
        const durationWeeks = document.getElementById('duration_weeks').value;
        if (durationWeeks) {
            metadata.duration_weeks = parseInt(durationWeeks);
        }
    }
    
    // AFF settings
    if (document.getElementById('account_limit').checked) {
        metadata.account_limit = true;
        const maxAccounts = document.getElementById('max_accounts').value;
        if (maxAccounts) {
            metadata.max_accounts = parseInt(maxAccounts);
        }
    }
    
    // Formula settings
    const formulaType = document.getElementById('formula_type').value;
    if (formulaType) {
        metadata.formula_type = formulaType;
    }
    if (document.getElementById('includes_templates').checked) {
        metadata.includes_templates = true;
    }
    if (document.getElementById('includes_tools').checked) {
        metadata.includes_tools = true;
    }
    
    // Update JSON preview
    document.getElementById('metadataJson').value = JSON.stringify(metadata, null, 2);
    
    // Show/hide relevant sections
    toggleMetadataSections();
}

function toggleMetadataSections() {
    const productName = document.querySelector('[name="name"]').value.toLowerCase();
    
    // Show course settings if name contains "course" or "khóa học"
    const showCourse = productName.includes('course') || productName.includes('khóa học');
    document.getElementById('courseSettings').style.display = showCourse ? 'block' : 'none';
    
    // Show AFF settings if subscription is checked
    const showAff = document.getElementById('subscription_based').checked;
    document.getElementById('affSettings').style.display = showAff ? 'block' : 'none';
    
    // Show formula settings if name contains "formula" or "công thức"
    const showFormula = productName.includes('formula') || productName.includes('công thức');
    document.getElementById('formulaSettings').style.display = showFormula ? 'block' : 'none';
}

// Load existing metadata when editing
function loadExistingMetadata(metadataJson) {
    if (!metadataJson) return;
    
    try {
        const metadata = JSON.parse(metadataJson);
        
        // Load checkboxes
        Object.keys(metadata).forEach(key => {
            const checkbox = document.getElementById(key);
            if (checkbox && checkbox.type === 'checkbox') {
                checkbox.checked = metadata[key] === true;
            }
        });
        
        // Load select values
        if (metadata.course_type) {
            document.getElementById('course_type').value = metadata.course_type;
        }
        if (metadata.formula_type) {
            document.getElementById('formula_type').value = metadata.formula_type;
        }
        
        // Load number inputs
        if (metadata.duration_weeks) {
            document.getElementById('duration_weeks').value = metadata.duration_weeks;
        }
        if (metadata.max_accounts) {
            document.getElementById('max_accounts').value = metadata.max_accounts;
        }
        
        updateMetadata();
    } catch (e) {
        console.error('Error loading metadata:', e);
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    updateMetadata();
    
    // Listen for product name changes to auto-show relevant sections
    const nameInput = document.querySelector('[name="name"]');
    if (nameInput) {
        nameInput.addEventListener('input', toggleMetadataSections);
    }
});
</script>
