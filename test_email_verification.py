#!/usr/bin/env python3
"""
Test email verification system
"""

from email_verification_service import *

def test_email_config():
    """Test email configuration"""
    print("🔧 Testing Email Configuration")
    print("=" * 50)
    
    config = get_email_config()
    if config:
        print("✅ Email config loaded successfully:")
        for key, value in config.items():
            if 'token' in key.lower():
                display_value = value[:10] + '...' if len(value) > 10 else value
            else:
                display_value = value
            print(f"   {key}: {display_value}")
        return True
    else:
        print("❌ Failed to load email config")
        return False

def test_otp_generation():
    """Test OTP generation"""
    print("\n🔢 Testing OTP Generation")
    print("=" * 50)
    
    for i in range(5):
        otp = generate_otp()
        print(f"   OTP {i+1}: {otp}")
        
        # Validate OTP format
        if len(otp) == 6 and otp.isdigit():
            print(f"      ✅ Valid format")
        else:
            print(f"      ❌ Invalid format")
            return False
    
    return True

def test_verification_code_creation():
    """Test verification code creation"""
    print("\n📝 Testing Verification Code Creation")
    print("=" * 50)
    
    test_email = "<EMAIL>"
    
    # Test registration code
    print("📧 Creating registration verification code...")
    reg_code = create_verification_code(test_email, 'registration', user_id=1)
    
    if reg_code:
        print(f"   ✅ Registration code created:")
        print(f"      Code ID: {reg_code['code_id']}")
        print(f"      Code: {reg_code['code']}")
        print(f"      Expires: {reg_code['expires_at']}")
        print(f"      Max attempts: {reg_code['max_attempts']}")
    else:
        print("   ❌ Failed to create registration code")
        return False
    
    # Test password reset code
    print("\n🔑 Creating password reset verification code...")
    reset_code = create_verification_code(test_email, 'password_reset')
    
    if reset_code:
        print(f"   ✅ Password reset code created:")
        print(f"      Code ID: {reset_code['code_id']}")
        print(f"      Code: {reset_code['code']}")
        print(f"      Expires: {reset_code['expires_at']}")
    else:
        print("   ❌ Failed to create password reset code")
        return False
    
    return True

def test_otp_verification():
    """Test OTP verification"""
    print("\n🔍 Testing OTP Verification")
    print("=" * 50)
    
    test_email = "<EMAIL>"
    
    # Create a test code
    code_data = create_verification_code(test_email, 'registration')
    if not code_data:
        print("❌ Failed to create test code")
        return False
    
    test_code = code_data['code']
    print(f"📝 Created test code: {test_code}")
    
    # Test correct verification
    print("\n✅ Testing correct verification...")
    result = verify_otp_code(test_email, test_code, 'registration')
    if result['success']:
        print(f"   ✅ Verification successful")
        print(f"      User ID: {result.get('user_id')}")
        print(f"      Code ID: {result.get('code_id')}")
    else:
        print(f"   ❌ Verification failed: {result['error']}")
        return False
    
    # Test already used code
    print("\n🔄 Testing already used code...")
    result = verify_otp_code(test_email, test_code, 'registration')
    if not result['success'] and 'đã được sử dụng' in result['error']:
        print(f"   ✅ Correctly rejected used code: {result['error']}")
    else:
        print(f"   ❌ Should have rejected used code")
        return False
    
    # Test wrong code
    print("\n❌ Testing wrong code...")
    result = verify_otp_code(test_email, '999999', 'registration')
    if not result['success'] and 'không hợp lệ' in result['error']:
        print(f"   ✅ Correctly rejected wrong code: {result['error']}")
    else:
        print(f"   ❌ Should have rejected wrong code")
        return False
    
    return True

def test_email_sending():
    """Test email sending (demo mode)"""
    print("\n📧 Testing Email Sending")
    print("=" * 50)
    
    test_email = "<EMAIL>"
    
    # Test registration email
    print("📝 Testing registration email...")
    result = send_otp_email(test_email, 'registration')
    
    if result['success']:
        print(f"   ✅ Registration email sent successfully")
        print(f"      Code ID: {result['code_id']}")
        print(f"      Expires: {result['expires_at']}")
        print(f"      Max attempts: {result['max_attempts']}")
    else:
        print(f"   ❌ Failed to send registration email: {result['error']}")
        return False
    
    # Test password reset email
    print("\n🔑 Testing password reset email...")
    result = send_otp_email(test_email, 'password_reset')
    
    if result['success']:
        print(f"   ✅ Password reset email sent successfully")
        print(f"      Code ID: {result['code_id']}")
    else:
        print(f"   ❌ Failed to send password reset email: {result['error']}")
        return False
    
    return True

def test_cleanup():
    """Test cleanup function"""
    print("\n🧹 Testing Cleanup Function")
    print("=" * 50)
    
    deleted_count = cleanup_expired_codes()
    print(f"   ✅ Cleaned up {deleted_count} expired codes")
    
    return True

def main():
    """Main test function"""
    print("🧪 Email Verification System Test")
    print("=" * 60)
    
    tests = [
        ("Email Configuration", test_email_config),
        ("OTP Generation", test_otp_generation),
        ("Verification Code Creation", test_verification_code_creation),
        ("OTP Verification", test_otp_verification),
        ("Email Sending", test_email_sending),
        ("Cleanup Function", test_cleanup)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name}: PASSED")
            else:
                print(f"\n❌ {test_name}: FAILED")
        except Exception as e:
            print(f"\n💥 {test_name}: ERROR - {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! Email verification system is ready.")
        print("\n📋 System capabilities:")
        print("  ✅ Generate 6-digit OTP codes")
        print("  ✅ Store verification codes with expiry")
        print("  ✅ Verify OTP codes with attempt limits")
        print("  ✅ Send emails via Mailtrap (when configured)")
        print("  ✅ Handle registration and password reset flows")
        print("  ✅ Automatic cleanup of expired codes")
        print("\n🚀 Ready for integration with registration/login system!")
    else:
        print(f"\n❌ {total - passed} tests failed. Please fix issues before proceeding.")

if __name__ == "__main__":
    main()
