#!/usr/bin/env python3
"""
Fix WarrantyRequests table sequence and id column
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_warranty_requests_sequence():
    """Fix WarrantyRequests table sequence and id column"""
    
    print("🚀 Fixing WarrantyRequests sequence and id column...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current table structure
        print("\n🔧 Step 1: Checking WarrantyRequests table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'WarrantyRequests' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        columns = cursor.fetchall()
        column_names = [col[0] for col in columns]
        
        print(f"📋 Current columns: {column_names}")
        
        # Check id column specifically
        id_column = next((col for col in columns if col[0] == 'id'), None)
        if id_column:
            nullable = "NULL" if id_column[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {id_column[3]}" if id_column[3] else ""
            print(f"📋 id column: {id_column[1]} {nullable}{default}")
        else:
            print("❌ id column not found")
        
        # Step 2: Check current data
        print("\n🔧 Step 2: Checking current data...")
        
        cursor.execute('SELECT COUNT(*), MAX(warranty_id), MAX(id) FROM "WarrantyRequests";')
        count, max_warranty_id, max_id = cursor.fetchone()
        print(f"📊 Records: {count}, Max warranty_id: {max_warranty_id}, Max id: {max_id}")
        
        # Step 3: Fix id column if needed
        if id_column and id_column[2] == 'YES':  # If id is nullable
            print("\n🔧 Step 3: Fixing id column...")
            
            # Update NULL id values to match warranty_id
            cursor.execute('''
                UPDATE "WarrantyRequests" 
                SET id = warranty_id 
                WHERE id IS NULL;
            ''')
            updated = cursor.rowcount
            print(f"  ✅ Updated {updated} records: set id = warranty_id")
            
            # Set id column to NOT NULL
            cursor.execute('ALTER TABLE "WarrantyRequests" ALTER COLUMN id SET NOT NULL;')
            print("  ✅ Set id column to NOT NULL")
        
        # Step 4: Create/fix sequence for id column
        print("\n🔧 Step 4: Creating/fixing sequence for id column...")
        
        # Check if sequence exists
        cursor.execute('''
            SELECT pg_get_serial_sequence('"WarrantyRequests"', 'id');
        ''')
        id_sequence = cursor.fetchone()[0]
        
        if not id_sequence:
            print("  🔧 Creating new sequence for id column...")
            
            # Create sequence
            cursor.execute('''
                CREATE SEQUENCE IF NOT EXISTS "WarrantyRequests_id_seq"
                INCREMENT 1
                MINVALUE 1
                MAXVALUE 2147483647
                START 1
                CACHE 1;
            ''')
            
            # Set sequence ownership
            cursor.execute('''
                ALTER SEQUENCE "WarrantyRequests_id_seq" OWNED BY "WarrantyRequests".id;
            ''')
            
            # Set default value
            cursor.execute('''
                ALTER TABLE "WarrantyRequests" ALTER COLUMN id SET DEFAULT nextval('"WarrantyRequests_id_seq"'::regclass);
            ''')
            
            id_sequence = '"WarrantyRequests_id_seq"'
            print(f"  ✅ Created sequence: {id_sequence}")
        else:
            print(f"  ✅ Found existing sequence: {id_sequence}")
        
        # Step 5: Fix sequence value
        print("\n🔧 Step 5: Fixing sequence value...")
        
        # Get current max id
        cursor.execute('SELECT COALESCE(MAX(id), 0) FROM "WarrantyRequests";')
        current_max_id = cursor.fetchone()[0]
        
        new_value = current_max_id + 1
        cursor.execute(f"SELECT setval('{id_sequence}', {new_value}, false);")
        print(f"  ✅ Set sequence to {new_value}")
        
        # Step 6: Test sequence
        print("\n🔧 Step 6: Testing sequence...")
        
        cursor.execute(f"SELECT nextval('{id_sequence}');")
        next_id = cursor.fetchone()[0]
        print(f"  🔍 Next ID from sequence: {next_id}")
        
        # Reset sequence back (we just tested)
        cursor.execute(f"SELECT setval('{id_sequence}', {new_value}, false);")
        
        # Step 7: Test INSERT without specifying id
        print("\n🔧 Step 7: Testing INSERT without id...")
        
        try:
            cursor.execute('SAVEPOINT test_insert;')
            
            cursor.execute('''
                INSERT INTO "WarrantyRequests" (user_id, account_id, reason, description, status)
                VALUES (1, 1, 'Test reason', 'Test description', 'Pending')
                RETURNING id;
            ''')
            
            test_id = cursor.fetchone()[0]
            print(f"  ✅ Test INSERT successful, got id: {test_id}")
            
            # Rollback test
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
            print("  ✅ Test INSERT rolled back")
            
        except Exception as e:
            print(f"  ❌ Test INSERT failed: {e}")
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
        
        # Step 8: Check final structure
        print("\n🔧 Step 8: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'WarrantyRequests' AND column_name = 'id' AND table_schema = 'public';
        ''')
        
        final_id_col = cursor.fetchone()
        if final_id_col:
            nullable = "NULL" if final_id_col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {final_id_col[3]}" if final_id_col[3] else ""
            print(f"📋 Final id column: {final_id_col[1]} {nullable}{default}")
        
        # Step 9: Show current sequence status
        print("\n🔧 Step 9: Final sequence status...")
        
        cursor.execute(f'SELECT last_value, is_called FROM {id_sequence};')
        last_value, is_called = cursor.fetchone()
        print(f"📊 Sequence last_value: {last_value}, is_called: {is_called}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 WarrantyRequests sequence fixed successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test warranty request creation")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_warranty_requests_sequence()
    sys.exit(0 if success else 1)
