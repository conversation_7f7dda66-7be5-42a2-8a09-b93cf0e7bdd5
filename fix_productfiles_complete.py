#!/usr/bin/env python3
"""
Complete fix for ProductFiles table - add all missing columns
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_productfiles_complete():
    """Complete fix for ProductFiles table"""
    
    print("🚀 Complete fix for ProductFiles table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current structure
        print("\n🔧 Step 1: Checking current ProductFiles table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductFiles' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Current columns: {existing_column_names}")
        
        # Step 2: Define all required columns
        required_columns = [
            ('file_name', 'VARCHAR(255)', 'Display name for the file'),
            ('original_name', 'VARCHAR(255)', 'Original filename when uploaded'),
            ('description', 'TEXT', 'File description'),
            ('is_preview', 'BOOLEAN DEFAULT FALSE', 'Whether file is a preview file'),
            ('upload_date', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP', 'Upload date (alias for uploaded_at)')
        ]
        
        # Step 3: Add missing columns one by one
        print("\n🔧 Step 2: Adding missing columns...")
        
        for col_name, col_type, col_desc in required_columns:
            if col_name not in existing_column_names:
                print(f"\n  Adding {col_name} column...")
                try:
                    cursor.execute(f'''
                        ALTER TABLE "ProductFiles" 
                        ADD COLUMN {col_name} {col_type};
                    ''')
                    print(f"    ✅ Added {col_name} ({col_type}) - {col_desc}")
                except Exception as e:
                    print(f"    ❌ Failed to add {col_name}: {e}")
            else:
                print(f"  ✅ {col_name} already exists")
        
        # Step 4: Update existing data
        print("\n🔧 Step 3: Updating existing data...")
        
        updates = [
            ("file_name = filename", "file_name IS NULL AND filename IS NOT NULL"),
            ("original_name = filename", "original_name IS NULL AND filename IS NOT NULL"),
            ("upload_date = uploaded_at", "upload_date IS NULL AND uploaded_at IS NOT NULL"),
            ("is_preview = FALSE", "is_preview IS NULL")
        ]
        
        for update_set, where_clause in updates:
            try:
                cursor.execute(f'''
                    UPDATE "ProductFiles" 
                    SET {update_set}
                    WHERE {where_clause};
                ''')
                updated_rows = cursor.rowcount
                print(f"  ✅ Updated {updated_rows} records: {update_set}")
            except Exception as e:
                print(f"  ⚠️  Update failed for {update_set}: {e}")
        
        # Step 5: Create indexes
        print("\n🔧 Step 4: Creating indexes...")
        
        indexes = [
            ('idx_product_files_file_name', 'file_name'),
            ('idx_product_files_original_name', 'original_name'),
            ('idx_product_files_is_preview', 'is_preview'),
            ('idx_product_files_upload_date', 'upload_date')
        ]
        
        for index_name, column_name in indexes:
            try:
                cursor.execute(f'''
                    CREATE INDEX IF NOT EXISTS {index_name} 
                    ON "ProductFiles"({column_name});
                ''')
                print(f"  ✅ Created index: {index_name}")
            except Exception as e:
                print(f"  ⚠️  Index creation failed for {index_name}: {e}")
        
        # Step 6: Verify final structure
        print("\n🔧 Step 5: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'ProductFiles' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        final_columns = cursor.fetchall()
        final_column_names = [col[0] for col in final_columns]
        
        print(f"📋 Final columns: {final_column_names}")
        
        # Check if all required columns exist
        missing_columns = []
        for col_name, _, _ in required_columns:
            if col_name not in final_column_names:
                missing_columns.append(col_name)
        
        if missing_columns:
            print(f"❌ Still missing columns: {missing_columns}")
        else:
            print("✅ All required columns are present")
        
        # Step 7: Test the problematic query
        print("\n🔧 Step 6: Testing the problematic query...")
        
        try:
            cursor.execute('''
                SELECT product_id, filename, file_name, original_name, file_size, 
                       file_type, upload_date, is_preview, description
                FROM "ProductFiles" 
                LIMIT 1;
            ''')
            
            test_rows = cursor.fetchall()
            print(f"  ✅ Query test successful - {len(test_rows)} rows returned")
            
        except Exception as e:
            print(f"  ❌ Query test failed: {e}")
            
            # Try to identify which column is missing
            test_columns = ['product_id', 'filename', 'file_name', 'original_name', 'file_size', 'file_type', 'upload_date', 'is_preview', 'description']
            for col in test_columns:
                try:
                    cursor.execute(f'SELECT {col} FROM "ProductFiles" LIMIT 1;')
                    print(f"    ✅ {col} exists")
                except Exception as col_e:
                    print(f"    ❌ {col} missing: {col_e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Complete fix completed!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test product file functionality")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_productfiles_complete()
    sys.exit(0 if success else 1)
