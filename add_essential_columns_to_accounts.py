#!/usr/bin/env python3
"""
Add essential columns to Accounts table: email, full_email, full_info
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_essential_columns_to_accounts():
    """Add essential columns to Accounts table"""
    
    print("🚀 Adding essential columns to Accounts table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current Accounts table structure
        print("\n🔧 Step 1: Checking current Accounts table structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        existing_columns = cursor.fetchall()
        existing_column_names = [col[0] for col in existing_columns]
        
        print(f"📋 Current columns count: {len(existing_column_names)}")
        
        # Step 2: Define essential columns to add
        essential_columns = [
            ('email', 'varchar(255)', 'Email của account'),
            ('full_email', 'varchar(255)', 'Full email của account'),
            ('full_info', 'text', 'Thông tin đầy đủ của account')
        ]
        
        # Step 3: Add missing columns
        print(f"\n🔧 Step 3: Adding essential columns...")
        
        added_count = 0
        for col_name, col_type, col_comment in essential_columns:
            if col_name not in existing_column_names:
                try:
                    # Add column
                    cursor.execute(f'ALTER TABLE "Accounts" ADD COLUMN {col_name} {col_type};')
                    
                    # Add comment
                    cursor.execute(f'COMMENT ON COLUMN "Accounts"."{col_name}" IS \'{col_comment}\';')
                    
                    print(f"  ✅ Added: {col_name} ({col_type})")
                    added_count += 1
                    
                except Exception as e:
                    print(f"  ❌ Failed to add {col_name}: {e}")
            else:
                print(f"  ⏭️  Already exists: {col_name}")
        
        print(f"\n📊 Added {added_count} new columns")
        
        # Step 4: Create indexes for performance (optional)
        print(f"\n🔧 Step 4: Creating indexes...")
        
        indexes = [
            ('idx_accounts_email', 'email'),
            ('idx_accounts_full_email', 'full_email')
        ]
        
        for index_name, column in indexes:
            try:
                cursor.execute(f'CREATE INDEX IF NOT EXISTS {index_name} ON "Accounts"({column}) WHERE {column} IS NOT NULL;')
                print(f"  ✅ Created index: {index_name}")
            except Exception as e:
                print(f"  ⚠️  Index {index_name}: {e}")
        
        # Step 5: Verify final structure
        print(f"\n🔧 Step 5: Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND table_schema = 'public'
            AND column_name IN ('email', 'full_email', 'full_info')
            ORDER BY column_name;
        ''')
        
        new_cols = cursor.fetchall()
        print(f"📋 New columns added: {[col[0] for col in new_cols]}")
        
        # Step 6: Test query that was failing
        print(f"\n🔧 Step 6: Testing the problematic query...")
        
        try:
            cursor.execute('''
                SELECT account_id, account_name, follower_count, 
                       like_count, email, full_email, full_info
                FROM "Accounts" 
                WHERE is_deleted = 0 
                LIMIT 1;
            ''')
            
            test_result = cursor.fetchone()
            if test_result:
                print("  ✅ Query test successful")
                print(f"  📋 Sample: ID={test_result[0]}, Name={test_result[1]}")
            else:
                print("  ✅ Query syntax OK (no data)")
                
        except Exception as e:
            print(f"  ❌ Query test failed: {e}")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Essential columns added successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test the functionality that was failing")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_essential_columns_to_accounts()
    sys.exit(0 if success else 1)
