#!/usr/bin/env python3
"""
Fix WarrantyRequests status DEFAULT value to match local
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_warranty_status_default():
    """Fix WarrantyRequests status DEFAULT value"""
    
    print("🚀 Fixing WarrantyRequests status DEFAULT value...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check current status column
        print("\n🔧 Step 1: Checking current status column...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'WarrantyRequests' AND column_name = 'status' AND table_schema = 'public';
        ''')
        
        status_col = cursor.fetchone()
        if status_col:
            nullable = "NULL" if status_col[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {status_col[3]}" if status_col[3] else " (no default)"
            print(f"📋 Current status: {status_col[1]} {nullable}{default}")
        else:
            print("❌ status column not found")
            return False
        
        # Step 2: Check current status values
        print("\n🔧 Step 2: Checking current status values...")
        
        cursor.execute('''
            SELECT status, COUNT(*) 
            FROM "WarrantyRequests" 
            GROUP BY status 
            ORDER BY status;
        ''')
        
        status_counts = cursor.fetchall()
        print("📊 Current status distribution:")
        for status, count in status_counts:
            print(f"  - '{status}': {count} records")
        
        # Step 3: Fix status DEFAULT value
        print("\n🔧 Step 3: Setting status DEFAULT to 'Pending'...")
        
        try:
            # Set DEFAULT value to 'Pending' (matching local and code expectations)
            cursor.execute('''
                ALTER TABLE "WarrantyRequests" 
                ALTER COLUMN status SET DEFAULT 'Pending';
            ''')
            
            print("  ✅ Set status DEFAULT to 'Pending'")
            
        except Exception as e:
            print(f"  ❌ Failed to set DEFAULT: {e}")
            return False
        
        # Step 4: Update existing lowercase 'pending' to 'Pending'
        print("\n🔧 Step 4: Updating existing lowercase status values...")
        
        status_mappings = {
            'pending': 'Pending',
            'approved': 'Approved',
            'rejected': 'Rejected',
            'completed': 'Completed',
            'cancelled': 'Cancelled'
        }
        
        total_updated = 0
        for old_status, new_status in status_mappings.items():
            cursor.execute('''
                UPDATE "WarrantyRequests" 
                SET status = %s 
                WHERE status = %s;
            ''', (new_status, old_status))
            
            updated = cursor.rowcount
            if updated > 0:
                print(f"  ✅ Updated {updated} records: '{old_status}' → '{new_status}'")
                total_updated += updated
        
        if total_updated == 0:
            print("  ✅ No status values needed updating")
        
        # Step 5: Verify the fix
        print("\n🔧 Step 5: Verifying the fix...")
        
        # Check DEFAULT value
        cursor.execute('''
            SELECT column_default
            FROM information_schema.columns 
            WHERE table_name = 'WarrantyRequests' AND column_name = 'status' AND table_schema = 'public';
        ''')
        
        new_default = cursor.fetchone()[0]
        print(f"📋 New DEFAULT value: {new_default}")
        
        # Check status distribution after fix
        cursor.execute('''
            SELECT status, COUNT(*) 
            FROM "WarrantyRequests" 
            GROUP BY status 
            ORDER BY status;
        ''')
        
        new_status_counts = cursor.fetchall()
        print("📊 Status distribution after fix:")
        for status, count in new_status_counts:
            print(f"  - '{status}': {count} records")
        
        # Step 6: Test INSERT with new DEFAULT
        print("\n🔧 Step 6: Testing INSERT with new DEFAULT...")
        
        try:
            cursor.execute('SAVEPOINT test_insert;')
            
            # Test INSERT without specifying status
            cursor.execute('''
                INSERT INTO "WarrantyRequests" (user_id, account_id, reason, description)
                VALUES (1, 1, 'Test reason', 'Test description')
                RETURNING id, status;
            ''')
            
            test_result = cursor.fetchone()
            test_id, test_status = test_result
            print(f"  ✅ Test INSERT successful: id={test_id}, status='{test_status}'")
            
            if test_status == 'Pending':
                print("  ✅ DEFAULT value working correctly!")
            else:
                print(f"  ⚠️  Expected 'Pending', got '{test_status}'")
            
            # Rollback test
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
            print("  ✅ Test INSERT rolled back")
            
        except Exception as e:
            print(f"  ❌ Test INSERT failed: {e}")
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
        
        # Step 7: Check JavaScript compatibility
        print("\n🔧 Step 7: JavaScript compatibility check...")
        
        # Check if any records have status that won't match JavaScript
        cursor.execute('''
            SELECT DISTINCT status 
            FROM "WarrantyRequests" 
            WHERE status NOT IN ('Pending', 'Approved', 'Rejected', 'Completed', 'Cancelled');
        ''')
        
        incompatible_statuses = cursor.fetchall()
        if incompatible_statuses:
            print("⚠️  Found incompatible status values:")
            for status in incompatible_statuses:
                print(f"  - '{status[0]}'")
        else:
            print("✅ All status values are JavaScript-compatible")
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 WarrantyRequests status DEFAULT fixed successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test warranty request creation")
        print("   3. Verify Cancel button appears for Pending requests")
        
        return True
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_warranty_status_default()
    sys.exit(0 if success else 1)
