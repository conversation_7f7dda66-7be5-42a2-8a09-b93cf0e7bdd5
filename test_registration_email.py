#!/usr/bin/env python3
"""
Test registration email flow
"""

from email_verification_service import *

def test_registration_email_flow():
    """Test the complete registration email flow"""
    print("🧪 Testing Registration Email Flow")
    print("=" * 60)
    
    test_email = "<EMAIL>"
    
    # Step 1: Test email config
    print("📋 1. Testing email configuration...")
    config = get_email_config()
    
    if not config:
        print("❌ No email config found!")
        return False
    
    print("✅ Email config loaded:")
    for key, value in config.items():
        if 'token' in key.lower():
            display_value = value[:10] + '...' if len(value) > 10 else value
        else:
            display_value = value
        print(f"   {key}: {display_value}")
    
    # Step 2: Test OTP creation
    print(f"\n📋 2. Testing OTP creation for {test_email}...")
    code_data = create_verification_code(test_email, 'registration')
    
    if not code_data:
        print("❌ Failed to create verification code!")
        return False
    
    print("✅ Verification code created:")
    print(f"   Code ID: {code_data['code_id']}")
    print(f"   Code: {code_data['code']}")
    print(f"   Expires: {code_data['expires_at']}")
    
    # Step 3: Test email sending
    print(f"\n📋 3. Testing email sending...")
    email_sent = send_verification_email(test_email, code_data['code'], 'registration')
    
    if email_sent:
        print("✅ Email sent successfully!")
    else:
        print("❌ Failed to send email!")
        return False
    
    # Step 4: Test complete flow
    print(f"\n📋 4. Testing complete send_otp_email flow...")
    result = send_otp_email(test_email + "2", 'registration')
    
    print(f"Complete flow result: {result}")
    
    if result['success']:
        print("✅ Complete registration email flow working!")
        return True
    else:
        print(f"❌ Complete flow failed: {result['error']}")
        return False

def test_config_values():
    """Test specific config values"""
    print("\n🔧 Testing Config Values")
    print("=" * 60)
    
    import psycopg2
    import psycopg2.extras
    from db_config import PG_CONFIG
    
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Get all email-related configs
        cursor.execute('''
            SELECT config_key, config_value, description
            FROM "SystemConfig"
            WHERE config_key LIKE '%mail%' OR config_key LIKE '%email%' OR config_key LIKE '%otp%'
            ORDER BY config_key
        ''')
        
        configs = cursor.fetchall()
        
        print(f"Found {len(configs)} email-related configs:")
        for config in configs:
            value = config['config_value']
            if 'token' in config['config_key'].lower() and value:
                value = value[:10] + '...' if len(value) > 10 else value
            
            print(f"   {config['config_key']}: '{value}'")
            if config['description']:
                print(f"      → {config['description']}")
        
        # Check if required configs exist
        required_configs = [
            'mailtrap_api_token',
            'mailtrap_sender_email',
            'mailtrap_sender_name'
        ]
        
        print(f"\n📋 Checking required configs:")
        for req_config in required_configs:
            cursor.execute('SELECT config_value FROM "SystemConfig" WHERE config_key = %s', (req_config,))
            result = cursor.fetchone()
            
            if result and result['config_value']:
                print(f"   ✅ {req_config}: Present")
            else:
                print(f"   ❌ {req_config}: Missing or empty")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking config: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Registration Email Debug Tool")
    print("=" * 60)
    
    # Test config values first
    config_ok = test_config_values()
    
    if not config_ok:
        print("\n❌ Config issues found! Fix configs first.")
        return
    
    # Test email flow
    flow_ok = test_registration_email_flow()
    
    if flow_ok:
        print("\n🎉 Registration email flow is working!")
        print("\n💡 If you're still not receiving emails:")
        print("  1. Check your Mailtrap inbox")
        print("  2. Verify API token is correct")
        print("  3. Check sender email domain")
        print("  4. Look for emails in spam folder")
    else:
        print("\n❌ Registration email flow has issues!")
        print("\n🔧 Debug steps:")
        print("  1. Check database configs")
        print("  2. Verify Mailtrap credentials")
        print("  3. Test email sending manually")

if __name__ == "__main__":
    main()
