#!/bin/bash

# =====================================================
# Deployment Script - Marketplace Updates
# Date: 2025-09-04
# Features: Category Filtering, Cart Effects, Max Quantity
# =====================================================

set -e  # Exit on any error

echo "🚀 Starting Marketplace Updates Deployment"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_HOST="localhost"
DB_USER="alandoan"
DB_NAME="sapmmo"
APP_DIR="/path/to/your/app"  # Update this path
BACKUP_DIR="/path/to/backups"  # Update this path

# Functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as correct user
check_user() {
    log_info "Checking user permissions..."
    if [[ $EUID -eq 0 ]]; then
        log_warning "Running as root. Consider running as app user."
    fi
    log_success "User check completed"
}

# Backup database
backup_database() {
    log_info "Creating database backup..."
    
    BACKUP_FILE="${BACKUP_DIR}/sapmmo_backup_$(date +%Y%m%d_%H%M%S).sql"
    
    if ! mkdir -p "$BACKUP_DIR"; then
        log_error "Failed to create backup directory"
        exit 1
    fi
    
    if pg_dump -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_FILE"; then
        log_success "Database backed up to: $BACKUP_FILE"
    else
        log_error "Database backup failed"
        exit 1
    fi
}

# Run database migration
run_migration() {
    log_info "Running database migration..."
    
    if [[ ! -f "migration_max_quantity_and_dynamic_types.sql" ]]; then
        log_error "Migration file not found!"
        exit 1
    fi
    
    if psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -f migration_max_quantity_and_dynamic_types.sql; then
        log_success "Database migration completed successfully"
    else
        log_error "Database migration failed"
        exit 1
    fi
}

# Verify migration
verify_migration() {
    log_info "Verifying migration..."
    
    # Check if max_quantity column exists
    COLUMN_EXISTS=$(psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'Products' AND column_name = 'max_quantity';")
    
    if [[ "$COLUMN_EXISTS" -eq 1 ]]; then
        log_success "max_quantity column verified"
    else
        log_error "max_quantity column not found"
        exit 1
    fi
    
    # Check products with max_quantity
    PRODUCTS_COUNT=$(psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM \"Products\" WHERE max_quantity IS NOT NULL;")
    log_info "Products with max_quantity: $PRODUCTS_COUNT"
    
    log_success "Migration verification completed"
}

# Deploy application files
deploy_files() {
    log_info "Deploying application files..."
    
    # List of files to deploy
    FILES_TO_DEPLOY=(
        "mip_system.py"
        "templates/marketplace/storefront.html"
    )
    
    for file in "${FILES_TO_DEPLOY[@]}"; do
        if [[ -f "$file" ]]; then
            log_info "Deploying: $file"
            # Copy file to application directory
            # cp "$file" "$APP_DIR/$file"
            log_success "Deployed: $file"
        else
            log_warning "File not found: $file"
        fi
    done
    
    log_success "File deployment completed"
}

# Restart application services
restart_services() {
    log_info "Restarting application services..."
    
    # Restart gunicorn (adjust service name as needed)
    if systemctl is-active --quiet gunicorn; then
        log_info "Restarting gunicorn..."
        # sudo systemctl restart gunicorn
        log_success "Gunicorn restarted"
    else
        log_warning "Gunicorn service not found or not running"
    fi
    
    # Restart nginx (if needed)
    if systemctl is-active --quiet nginx; then
        log_info "Reloading nginx..."
        # sudo systemctl reload nginx
        log_success "Nginx reloaded"
    else
        log_warning "Nginx service not found or not running"
    fi
    
    log_success "Service restart completed"
}

# Test deployment
test_deployment() {
    log_info "Testing deployment..."
    
    # Test database connection
    if psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "SELECT 1;" > /dev/null 2>&1; then
        log_success "Database connection test passed"
    else
        log_error "Database connection test failed"
        exit 1
    fi
    
    # Test application (if URL is accessible)
    # if curl -f http://localhost/marketplace > /dev/null 2>&1; then
    #     log_success "Application test passed"
    # else
    #     log_warning "Application test failed or not accessible"
    # fi
    
    log_success "Deployment testing completed"
}

# Main deployment process
main() {
    echo "🎯 Marketplace Updates Deployment"
    echo "Features: Category Filtering, Cart Effects, Max Quantity"
    echo ""
    
    # Confirmation
    read -p "Are you sure you want to deploy? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Deployment cancelled"
        exit 0
    fi
    
    # Deployment steps
    check_user
    backup_database
    run_migration
    verify_migration
    deploy_files
    restart_services
    test_deployment
    
    echo ""
    log_success "🎉 Deployment completed successfully!"
    echo ""
    echo "📋 Post-deployment checklist:"
    echo "  1. Test category filtering at /marketplace"
    echo "  2. Test cart button animations"
    echo "  3. Test max quantity validation"
    echo "  4. Check browser console for errors"
    echo "  5. Test on mobile devices"
    echo ""
    echo "📊 Database summary:"
    psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            'Total Products' as metric,
            COUNT(*) as count
        FROM \"Products\" 
        WHERE is_active = true
        UNION ALL
        SELECT 
            'Products with max_quantity',
            COUNT(*)
        FROM \"Products\" 
        WHERE is_active = true AND max_quantity IS NOT NULL
        UNION ALL
        SELECT 
            'Single purchase products',
            COUNT(*)
        FROM \"Products\" 
        WHERE is_active = true AND max_quantity = 1;
    "
    
    echo ""
    log_success "Deployment log saved. Check application functionality!"
}

# Error handling
trap 'log_error "Deployment failed at line $LINENO"' ERR

# Run main function
main "$@"
