#!/bin/bash

# =============================================================================
# MARKETPLACE HEALTH CHECK SCRIPT
# Script để kiểm tra tình trạng marketplace sau deployment
# =============================================================================

echo "🔍 Marketplace Health Check"
echo "=========================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Database config - Read from db_config.py
APP_DIR="/var/www/sapmmo"
DB_CONFIG_FILE="$APP_DIR/db_config.py"

if [ ! -f "$DB_CONFIG_FILE" ]; then
    echo -e "${RED}❌ Database config file not found: $DB_CONFIG_FILE${NC}"
    exit 1
fi

# Extract database config from Python file
DB_HOST=$(python3 -c "import sys; sys.path.append('$APP_DIR'); from db_config import PG_CONFIG; print(PG_CONFIG['host'])")
DB_NAME=$(python3 -c "import sys; sys.path.append('$APP_DIR'); from db_config import PG_CONFIG; print(PG_CONFIG['database'])")
DB_USER=$(python3 -c "import sys; sys.path.append('$APP_DIR'); from db_config import PG_CONFIG; print(PG_CONFIG['user'])")
DB_PASSWORD=$(python3 -c "import sys; sys.path.append('$APP_DIR'); from db_config import PG_CONFIG; print(PG_CONFIG['password'])")

# Check database tables
echo -e "${YELLOW}📊 Checking database tables...${NC}"

tables=("ProductCategories" "Products" "ProductFiles" "AccountPackages" "PackageAccounts" "Orders" "OrderItems" "AFFPackages" "UserSubscriptions" "ProductTypes" "WarrantyRequests" "MarketplaceTransactions" "Config")

for table in "${tables[@]}"; do
    if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "SELECT COUNT(*) FROM \"$table\";" > /dev/null 2>&1; then
        count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM \"$table\";")
        echo -e "${GREEN}✅ $table: $count records${NC}"
    else
        echo -e "${RED}❌ $table: Missing or error${NC}"
    fi
done

# Check revenue_enabled column
echo -e "\n${YELLOW}🔧 Checking Accounts table...${NC}"
if PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -c "SELECT revenue_enabled FROM \"Accounts\" LIMIT 1;" > /dev/null 2>&1; then
    enabled_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM \"Accounts\" WHERE revenue_enabled = TRUE;")
    total_count=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT COUNT(*) FROM \"Accounts\" WHERE is_deleted = 0;")
    echo -e "${GREEN}✅ revenue_enabled column exists${NC}"
    echo -e "   Enabled for revenue: $enabled_count accounts"
    echo -e "   Total accounts: $total_count accounts"
else
    echo -e "${RED}❌ revenue_enabled column missing${NC}"
fi

# Check upload directories
echo -e "\n${YELLOW}📁 Checking upload directories...${NC}"

dirs=("/var/www/sapmmo/static/uploads/coffee-icons" "/var/www/sapmmo/static/uploads/products" "/var/www/sapmmo/static/uploads/categories")

for dir in "${dirs[@]}"; do
    if [ -d "$dir" ]; then
        files=$(find "$dir" -type f | wc -l)
        echo -e "${GREEN}✅ $dir ($files files)${NC}"
    else
        echo -e "${RED}❌ $dir missing${NC}"
    fi
done

# Check services
echo -e "\n${YELLOW}🔄 Checking services...${NC}"

if systemctl is-active --quiet sapmmo; then
    echo -e "${GREEN}✅ SAPMMO service: Running${NC}"
else
    echo -e "${RED}❌ SAPMMO service: Not running${NC}"
fi

if systemctl is-active --quiet nginx; then
    echo -e "${GREEN}✅ Nginx service: Running${NC}"
else
    echo -e "${RED}❌ Nginx service: Not running${NC}"
fi

# Check config values
echo -e "\n${YELLOW}⚙️  Checking coffee cup configs...${NC}"

configs=("enable_coffee_display" "coffee_cup_value" "coffee_icon_type" "coffee_icon_width" "coffee_icon_height")

for config in "${configs[@]}"; do
    value=$(PGPASSWORD="$DB_PASSWORD" psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" -t -c "SELECT config_value FROM \"Config\" WHERE config_key = '$config';")
    if [ -n "$value" ]; then
        echo -e "${GREEN}✅ $config: $value${NC}"
    else
        echo -e "${RED}❌ $config: Missing${NC}"
    fi
done

echo -e "\n${GREEN}🎉 Health check completed!${NC}"
