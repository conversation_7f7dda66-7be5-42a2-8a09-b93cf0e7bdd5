-- =====================================================
-- Migration: Max Quantity & Dynamic Product Types
-- Date: 2025-09-04
-- Time: 11:35 AM - Present
-- Description: Add max_quantity column and update product types for dynamic tabs
-- =====================================================

-- Start transaction
BEGIN;

-- =====================================================
-- 1. ADD max_quantity COLUMN TO Products TABLE
-- =====================================================

-- Check if max_quantity column exists, if not add it
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'Products' AND column_name = 'max_quantity'
    ) THEN
        ALTER TABLE "Products" ADD COLUMN max_quantity INTEGER DEFAULT NULL;
        RAISE NOTICE 'Added max_quantity column to Products table';
    ELSE
        RAISE NOTICE 'max_quantity column already exists in Products table';
    END IF;
END $$;

-- =====================================================
-- 2. UPDATE EXISTING win_product ITEMS TO HAVE max_quantity = 1
-- =====================================================

-- Update all products with product_type = 'win_product' to have max_quantity = 1
-- This implements the "single purchase only" rule for file products
UPDATE "Products" 
SET max_quantity = 1 
WHERE product_type = 'win_product' 
  AND max_quantity IS NULL;

-- Get count of updated products
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % win_product items to have max_quantity = 1', updated_count;
END $$;

-- =====================================================
-- 3. UPDATE PRODUCT_TYPE FOR DYNAMIC TABS
-- =====================================================

-- Update products that belong to "Bối Cảnh Live" ProductType
-- to use the ProductType name as product_type for dynamic tabs
UPDATE "Products" 
SET product_type = pt.name
FROM "ProductTypes" pt
WHERE "Products".product_type_id = pt.type_id
  AND pt.name = 'Bối Cảnh Live'
  AND "Products".product_type != pt.name;

-- Get count of updated products
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated % products to use ProductType name as product_type', updated_count;
END $$;

-- =====================================================
-- 4. UPDATE METADATA FOR PRODUCTS WITH max_quantity
-- =====================================================

-- Update metadata for products that now have max_quantity = 1
-- to include single_purchase_only flag
UPDATE "Products" 
SET metadata = COALESCE(metadata, '{}'::jsonb) || '{"single_purchase_only": true, "max_quantity": 1}'::jsonb
WHERE max_quantity = 1 
  AND product_type = 'win_product'
  AND (metadata IS NULL OR NOT (metadata ? 'single_purchase_only'));

-- Get count of updated metadata
DO $$
DECLARE
    updated_count INTEGER;
BEGIN
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RAISE NOTICE 'Updated metadata for % products with max_quantity = 1', updated_count;
END $$;

-- =====================================================
-- 5. VERIFICATION QUERIES
-- =====================================================

-- Verify max_quantity column was added
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'Products' AND column_name = 'max_quantity'
    ) THEN
        RAISE NOTICE '✅ VERIFICATION: max_quantity column exists';
    ELSE
        RAISE EXCEPTION '❌ VERIFICATION FAILED: max_quantity column missing';
    END IF;
END $$;

-- Show summary of products with max_quantity
SELECT 
    product_type,
    COUNT(*) as total_products,
    COUNT(max_quantity) as products_with_max_quantity,
    COUNT(CASE WHEN max_quantity = 1 THEN 1 END) as single_purchase_products
FROM "Products" 
WHERE is_active = true
GROUP BY product_type
ORDER BY total_products DESC;

-- Show ProductType "Bối Cảnh Live" products
SELECT 
    p.product_id,
    p.name,
    p.product_type,
    p.max_quantity,
    pt.name as producttype_name
FROM "Products" p
LEFT JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
WHERE pt.name = 'Bối Cảnh Live' OR p.product_type = 'Bối Cảnh Live'
ORDER BY p.product_id;

-- =====================================================
-- 6. CREATE INDEX FOR PERFORMANCE
-- =====================================================

-- Create index on max_quantity for faster filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_max_quantity 
ON "Products" (max_quantity) 
WHERE max_quantity IS NOT NULL;

RAISE NOTICE '✅ Created index on max_quantity column';

-- =====================================================
-- 7. FINAL SUMMARY
-- =====================================================

DO $$
DECLARE
    total_products INTEGER;
    products_with_max_qty INTEGER;
    single_purchase_products INTEGER;
    boi_canh_live_products INTEGER;
BEGIN
    -- Get counts
    SELECT COUNT(*) INTO total_products FROM "Products" WHERE is_active = true;
    SELECT COUNT(*) INTO products_with_max_qty FROM "Products" WHERE is_active = true AND max_quantity IS NOT NULL;
    SELECT COUNT(*) INTO single_purchase_products FROM "Products" WHERE is_active = true AND max_quantity = 1;
    SELECT COUNT(*) INTO boi_canh_live_products FROM "Products" WHERE is_active = true AND product_type = 'Bối Cảnh Live';
    
    RAISE NOTICE '';
    RAISE NOTICE '🎉 MIGRATION COMPLETED SUCCESSFULLY!';
    RAISE NOTICE '==========================================';
    RAISE NOTICE 'Total active products: %', total_products;
    RAISE NOTICE 'Products with max_quantity: %', products_with_max_qty;
    RAISE NOTICE 'Single purchase products: %', single_purchase_products;
    RAISE NOTICE 'Bối Cảnh Live products: %', boi_canh_live_products;
    RAISE NOTICE '';
    RAISE NOTICE '✅ Features implemented:';
    RAISE NOTICE '   - max_quantity column added';
    RAISE NOTICE '   - Single purchase validation for win_product';
    RAISE NOTICE '   - Dynamic ProductType tabs';
    RAISE NOTICE '   - Metadata Builder compatibility';
    RAISE NOTICE '   - Performance index created';
    RAISE NOTICE '';
END $$;

-- Commit transaction
COMMIT;

-- =====================================================
-- ROLLBACK SCRIPT (if needed)
-- =====================================================
/*
-- Uncomment and run this section if you need to rollback

BEGIN;

-- Remove max_quantity column
ALTER TABLE "Products" DROP COLUMN IF EXISTS max_quantity;

-- Revert product_type for Bối Cảnh Live products
UPDATE "Products" 
SET product_type = 'win_product'
FROM "ProductTypes" pt
WHERE "Products".product_type_id = pt.type_id
  AND pt.name = 'Bối Cảnh Live'
  AND "Products".product_type = 'Bối Cảnh Live';

-- Remove metadata changes
UPDATE "Products" 
SET metadata = metadata - 'single_purchase_only' - 'max_quantity'
WHERE metadata ? 'single_purchase_only';

-- Drop index
DROP INDEX IF EXISTS idx_products_max_quantity;

COMMIT;

RAISE NOTICE '🔄 Rollback completed';
*/
