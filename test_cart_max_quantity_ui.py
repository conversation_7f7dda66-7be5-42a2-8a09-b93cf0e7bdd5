#!/usr/bin/env python3
"""
Test thực tế cart max_quantity UI behavior
"""

import psycopg2
import psycopg2.extras
import json

def get_db_connection():
    """Kết nối database PostgreSQL local"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def create_test_products():
    """Tạo sản phẩm test với max_quantity khác nhau"""
    conn = get_db_connection()
    if not conn:
        return []
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🧪 Creating Test Products for Cart UI Testing")
        print("=" * 50)
        
        # Lấy category và product type
        cursor.execute('SELECT category_id FROM "ProductCategories" LIMIT 1')
        category_result = cursor.fetchone()
        category_id = category_result['category_id'] if category_result else 1
        
        cursor.execute('SELECT type_id FROM "ProductTypes" LIMIT 1')
        type_result = cursor.fetchone()
        type_id = type_result['type_id'] if type_result else 1
        
        test_products = [
            {
                'name': 'Test E-book - Single Purchase Only',
                'max_quantity': 1,
                'metadata': {
                    'requires_files': True,
                    'single_purchase_only': True,
                    'max_quantity': 1,
                    'product_type': 'win_product'
                },
                'description': 'Sản phẩm này chỉ có thể mua 1 lần. Khi thêm vào giỏ hàng, nút + sẽ bị disable.'
            },
            {
                'name': 'Test Template Pack - Limited to 3',
                'max_quantity': 3,
                'metadata': {
                    'requires_files': True,
                    'max_quantity': 3,
                    'product_type': 'win_product'
                },
                'description': 'Sản phẩm này có thể mua tối đa 3 sản phẩm. Nút + sẽ disable khi đạt 3.'
            },
            {
                'name': 'Test Account Package - Unlimited',
                'max_quantity': None,
                'metadata': {
                    'requires_accounts': True,
                    'product_type': 'account'
                },
                'description': 'Sản phẩm này không có giới hạn số lượng mua.'
            }
        ]
        
        created_products = []
        
        for product_data in test_products:
            # Xóa sản phẩm test cũ nếu có
            cursor.execute('DELETE FROM "Products" WHERE name = %s', (product_data['name'],))
            
            # Tạo sản phẩm mới
            cursor.execute('''
                INSERT INTO "Products" (
                    category_id, product_type_id, name, short_description, description,
                    price, stock, unlimited_stock, product_type, metadata, max_quantity, is_active
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING product_id
            ''', (
                category_id, type_id, product_data['name'],
                f"Test sản phẩm với max_quantity = {product_data['max_quantity']}",
                product_data['description'],
                25000, 100, False,
                product_data['metadata'].get('product_type', 'win_product'),
                json.dumps(product_data['metadata']),
                product_data['max_quantity'], True
            ))
            
            product_id = cursor.fetchone()[0]
            created_products.append({
                'product_id': product_id,
                'name': product_data['name'],
                'max_quantity': product_data['max_quantity']
            })
            
            print(f"✅ Created: {product_data['name']} (ID: {product_id}, max_quantity: {product_data['max_quantity']})")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return created_products
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_api_response():
    """Test API response có trả về đúng max_quantity không"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n🧪 Testing API Response for Cart UI")
        print("=" * 50)
        
        # Test query giống như API /api/marketplace/products
        cursor.execute('''
            SELECT p.product_id, p.name, p.short_description, p.description, p.price, p.stock,
                   p.unlimited_stock, p.product_type, p.image_url, p.is_featured,
                   c.name as category_name, c.icon as category_icon, p.status,
                   (SELECT COUNT(*) FROM "OrderItems" oi
                    JOIN "Orders" o ON oi.order_id = o.order_id
                    WHERE oi.product_id = p.product_id AND o.status = 'completed') as sold_count,
                   p.is_deleted, pt.name as type_name, p.max_quantity
            FROM "Products" p
            LEFT JOIN "ProductCategories" c ON p.category_id = c.category_id
            LEFT JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
            WHERE p.name LIKE %s AND (p.is_deleted = FALSE OR p.is_deleted IS NULL)
            ORDER BY p.created_at DESC
        ''', ('%Test%',))
        
        products = cursor.fetchall()
        
        print(f"📋 API Response (như frontend sẽ nhận):")
        print(f"{'ID':<4} {'Tên sản phẩm':<35} {'Max Qty':<8} {'UI Behavior'}")
        print("-" * 80)
        
        for product in products:
            max_qty = product['max_quantity']
            
            # Simulate frontend logic
            if max_qty == 1:
                ui_behavior = "Disable + button, show 'Chỉ mua 1 lần'"
            elif max_qty and max_qty > 1:
                ui_behavior = f"Disable + when quantity = {max_qty}"
            else:
                ui_behavior = "No limit, + always enabled"
            
            product_name = (product['name'][:32] + '...') if len(product['name']) > 35 else product['name']
            max_qty_display = str(max_qty) if max_qty else 'None'
            
            print(f"{product['product_id']:<4} {product_name:<35} {max_qty_display:<8} {ui_behavior}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_cart_interactions():
    """Simulate các tương tác trong giỏ hàng"""
    print("\n🧪 Simulating Cart UI Interactions")
    print("=" * 50)
    
    # Simulate products from API
    products = [
        {'product_id': 1, 'name': 'E-book - Single Purchase', 'max_quantity': 1, 'price': 25000},
        {'product_id': 2, 'name': 'Template Pack - Max 3', 'max_quantity': 3, 'price': 50000},
        {'product_id': 3, 'name': 'Account Package - Unlimited', 'max_quantity': None, 'price': 100000}
    ]
    
    cart = []
    
    def add_to_cart(product_id):
        product = next((p for p in products if p['product_id'] == product_id), None)
        if not product:
            return False, "Product not found"
        
        existing_item = next((item for item in cart if item['product_id'] == product_id), None)
        
        # Logic từ frontend
        if product['max_quantity'] and product['max_quantity'] == 1:
            if existing_item:
                return False, f"Sản phẩm \"{product['name']}\" chỉ có thể mua 1 lần!"
        
        if existing_item:
            if product['max_quantity'] and existing_item['quantity'] >= product['max_quantity']:
                return False, f"Sản phẩm \"{product['name']}\" chỉ có thể mua tối đa {product['max_quantity']} sản phẩm!"
            existing_item['quantity'] += 1
        else:
            cart.append({
                'product_id': product_id,
                'name': product['name'],
                'quantity': 1,
                'max_quantity': product['max_quantity'],
                'price': product['price']
            })
        
        return True, f"Đã thêm \"{product['name']}\" vào giỏ hàng!"
    
    def update_cart_quantity(cart_index, new_quantity):
        if cart_index >= len(cart):
            return False, "Invalid cart index"
        
        cart_item = cart[cart_index]
        
        if new_quantity <= 0:
            cart.pop(cart_index)
            return True, f"Đã xóa \"{cart_item['name']}\" khỏi giỏ hàng"
        
        # Logic từ frontend
        if cart_item['max_quantity'] and new_quantity > cart_item['max_quantity']:
            return False, f"Sản phẩm \"{cart_item['name']}\" chỉ có thể mua tối đa {cart_item['max_quantity']} sản phẩm!"
        
        cart_item['quantity'] = new_quantity
        return True, f"Đã cập nhật số lượng \"{cart_item['name']}\" thành {new_quantity}"
    
    def get_ui_state(cart_item):
        """Simulate UI state cho cart item"""
        can_increase = not cart_item['max_quantity'] or cart_item['quantity'] < cart_item['max_quantity']
        
        ui_state = {
            'plus_button_enabled': can_increase,
            'minus_button_enabled': True,
            'quantity_input_value': cart_item['quantity'],
            'max_quantity_text': f" (tối đa {cart_item['max_quantity']})" if cart_item['max_quantity'] else '',
            'single_purchase_note': cart_item['max_quantity'] == 1
        }
        
        return ui_state
    
    # Test scenarios
    scenarios = [
        ("Add E-book (max_quantity=1)", lambda: add_to_cart(1)),
        ("Try to add E-book again", lambda: add_to_cart(1)),
        ("Add Template Pack (max_quantity=3)", lambda: add_to_cart(2)),
        ("Add Template Pack again (quantity=2)", lambda: add_to_cart(2)),
        ("Add Template Pack again (quantity=3)", lambda: add_to_cart(2)),
        ("Try to add Template Pack again (should fail)", lambda: add_to_cart(2)),
        ("Add Account Package (unlimited)", lambda: add_to_cart(3)),
        ("Add Account Package again (quantity=2)", lambda: add_to_cart(3)),
        ("Try to increase E-book quantity to 2", lambda: update_cart_quantity(0, 2)),
        ("Try to increase Template Pack to 4", lambda: update_cart_quantity(1, 4)),
        ("Increase Account Package to 5", lambda: update_cart_quantity(2, 5))
    ]
    
    print(f"{'Scenario':<45} {'Result':<8} {'Message'}")
    print("-" * 90)
    
    for description, action in scenarios:
        success, message = action()
        result = "✅ PASS" if success else "❌ BLOCK"
        print(f"{description:<45} {result:<8} {message}")
    
    # Show final cart state with UI simulation
    print(f"\n🛒 Final Cart State with UI Simulation:")
    print(f"{'Product':<25} {'Qty':<4} {'+ Button':<10} {'- Button':<10} {'Notes'}")
    print("-" * 70)
    
    for i, item in enumerate(cart):
        ui_state = get_ui_state(item)
        plus_btn = "Enabled" if ui_state['plus_button_enabled'] else "Disabled"
        minus_btn = "Enabled" if ui_state['minus_button_enabled'] else "Disabled"
        notes = "Chỉ mua 1 lần" if ui_state['single_purchase_note'] else ui_state['max_quantity_text']
        
        print(f"{item['name']:<25} {item['quantity']:<4} {plus_btn:<10} {minus_btn:<10} {notes}")
    
    return True

def main():
    """Main function"""
    print("🧪 Testing Cart Max Quantity UI Behavior")
    print("=" * 60)
    
    # Test 1: Tạo sản phẩm test
    created_products = create_test_products()
    
    if created_products:
        # Test 2: API Response
        api_success = test_api_response()
        
        # Test 3: Cart Interactions
        cart_success = simulate_cart_interactions()
        
        print(f"\n✅ Test Summary:")
        print(f"   📦 Test Products: {len(created_products)} created")
        print(f"   🔌 API Response: {'✅ PASS' if api_success else '❌ FAIL'}")
        print(f"   🛒 Cart Interactions: {'✅ PASS' if cart_success else '❌ FAIL'}")
        
        print(f"\n🎯 UI Behavior Summary:")
        print(f"   ✅ max_quantity = 1: Nút + disable, hiển thị 'Chỉ mua 1 lần'")
        print(f"   ✅ max_quantity = N: Nút + disable khi quantity = N")
        print(f"   ✅ max_quantity = null: Nút + luôn enable")
        print(f"   ✅ Toast messages: Hiển thị cảnh báo khi vượt giới hạn")
        
        print(f"\n🔧 Test trên UI:")
        print(f"   1. Mở /marketplace")
        print(f"   2. Tìm sản phẩm 'Test E-book - Single Purchase Only'")
        print(f"   3. Thêm vào giỏ hàng")
        print(f"   4. Mở giỏ hàng - nút + sẽ bị disable")
        print(f"   5. Thử click nút + - sẽ có toast warning")
    else:
        print(f"\n❌ Failed to create test products")

if __name__ == "__main__":
    main()
