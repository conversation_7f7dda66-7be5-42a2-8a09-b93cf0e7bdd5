#!/usr/bin/env python3
"""
Test product type fix - tạo sản phẩm với type mới và kiểm tra hiển thị
"""

import psycopg2
import psycopg2.extras
import json

def get_db_connection():
    """Kết nối database PostgreSQL local"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def test_product_type_creation():
    """Test tạo product type mới"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🧪 Testing Product Type Creation")
        print("=" * 50)
        
        # 1. Tạo product type "TikTok Playbook" nếu chưa có
        cursor.execute('SELECT type_id FROM "ProductTypes" WHERE name = %s', ('TikTok Playbook',))
        existing = cursor.fetchone()
        
        if existing:
            type_id = existing['type_id']
            print(f"✅ Product type 'TikTok Playbook' đã tồn tại (ID: {type_id})")
        else:
            cursor.execute('''
                INSERT INTO "ProductTypes" (name, description, icon, is_active, metadata)
                VALUES (%s, %s, %s, %s, %s)
                RETURNING type_id
            ''', (
                'TikTok Playbook',
                'Hướng dẫn chi tiết về TikTok marketing và content creation',
                'cil-book',
                True,
                json.dumps({"requires_files": True, "downloadable": True})
            ))
            type_id = cursor.fetchone()[0]
            print(f"✅ Tạo product type 'TikTok Playbook' thành công (ID: {type_id})")
        
        # 2. Lấy category để gán sản phẩm
        cursor.execute('SELECT category_id FROM "ProductCategories" WHERE name LIKE %s LIMIT 1', ('%TikTok%',))
        category_result = cursor.fetchone()
        
        if not category_result:
            # Tạo category nếu chưa có
            cursor.execute('''
                INSERT INTO "ProductCategories" (name, description, icon, is_active)
                VALUES (%s, %s, %s, %s)
                RETURNING category_id
            ''', ('TikTok Playbook', 'Hướng dẫn TikTok', 'cil-book', True))
            category_id = cursor.fetchone()[0]
            print(f"✅ Tạo category 'TikTok Playbook' thành công (ID: {category_id})")
        else:
            category_id = category_result['category_id']
            print(f"✅ Sử dụng category ID: {category_id}")
        
        # 3. Link product type với category
        cursor.execute('''
            INSERT INTO "CategoryProductTypes" (category_id, type_id, is_primary)
            VALUES (%s, %s, %s)
            ON CONFLICT (category_id, type_id) DO NOTHING
        ''', (category_id, type_id, True))
        
        # 4. Tạo sản phẩm test
        test_product_name = "Test TikTok Playbook - Product Type Fix"
        
        # Xóa sản phẩm test cũ nếu có
        cursor.execute('DELETE FROM "Products" WHERE name = %s', (test_product_name,))
        
        cursor.execute('''
            INSERT INTO "Products" (
                category_id, product_type_id, name, short_description, description,
                price, stock, unlimited_stock, product_type, is_active, is_featured
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING product_id
        ''', (
            category_id,
            type_id,
            test_product_name,
            "Sản phẩm test để kiểm tra hiển thị product type",
            "Đây là sản phẩm test để kiểm tra việc hiển thị đúng tên product type thay vì 'Win Product'",
            50000,  # 50k MP
            10,
            False,
            'tiktok_playbook',  # product_type string sẽ được tạo từ logic mới
            True,
            False
        ))
        
        product_id = cursor.fetchone()[0]
        print(f"✅ Tạo sản phẩm test thành công (ID: {product_id})")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_response():
    """Test API response để xem có trả về đúng type_name không"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n🧪 Testing API Response")
        print("=" * 50)
        
        # Test query giống như API /api/marketplace/products
        cursor.execute('''
            SELECT p.product_id, p.name, p.short_description, p.description, p.price, p.stock,
                   p.unlimited_stock, p.product_type, p.image_url, p.is_featured,
                   c.name as category_name, c.icon as category_icon, p.status,
                   (SELECT COUNT(*) FROM "OrderItems" oi
                    JOIN "Orders" o ON oi.order_id = o.order_id
                    WHERE oi.product_id = p.product_id AND o.status = 'completed') as sold_count,
                   p.is_deleted, pt.name as type_name
            FROM "Products" p
            LEFT JOIN "ProductCategories" c ON p.category_id = c.category_id
            LEFT JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
            WHERE p.name LIKE %s
            ORDER BY p.created_at DESC
            LIMIT 5
        ''', ('%Test TikTok Playbook%',))
        
        products = cursor.fetchall()
        
        print(f"📋 Tìm thấy {len(products)} sản phẩm test:")
        
        for product in products:
            print(f"\n   📦 {product['name']}")
            print(f"      product_type: '{product['product_type']}'")
            print(f"      type_name: '{product['type_name']}'")
            print(f"      category: '{product['category_name']}'")
            
            # Test logic hiển thị
            if product['type_name']:
                display_name = product['type_name']
            else:
                labels = {
                    'account': 'Account',
                    'win_product': 'Win Product',
                    'course': 'Khóa học',
                    'aff_package': 'AFF Package'
                }
                display_name = labels.get(product['product_type'], product['product_type'])
            
            print(f"      Hiển thị: '{display_name}'")
            
            if product['type_name'] == 'TikTok Playbook':
                print(f"      ✅ PASS - Hiển thị đúng tên ProductType")
            else:
                print(f"      ❌ FAIL - Không hiển thị đúng tên ProductType")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🧪 Testing Product Type Fix")
    print("=" * 60)
    
    # Test 1: Tạo product type và sản phẩm
    creation_success = test_product_type_creation()
    
    if creation_success:
        # Test 2: Kiểm tra API response
        api_success = test_api_response()
        
        if api_success:
            print(f"\n✅ Test hoàn thành!")
            print(f"📋 Kết quả:")
            print(f"   - Product type 'TikTok Playbook' đã được tạo")
            print(f"   - Sản phẩm test đã được tạo")
            print(f"   - API trả về đúng type_name")
            print(f"   - Frontend sẽ hiển thị 'TikTok Playbook' thay vì 'Win Product'")
            
            print(f"\n🔧 Để test trên UI:")
            print(f"   1. Mở /admin/marketplace/products")
            print(f"   2. Tìm sản phẩm 'Test TikTok Playbook'")
            print(f"   3. Kiểm tra cột 'Loại sản phẩm' hiển thị 'TikTok Playbook'")
        else:
            print(f"\n❌ Test API thất bại!")
    else:
        print(f"\n❌ Test tạo product type thất bại!")

if __name__ == "__main__":
    main()
