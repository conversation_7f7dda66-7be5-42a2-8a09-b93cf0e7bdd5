#!/usr/bin/env python3
"""
Fix server password column - rename password_hash to password
Sử dụng thông tin database từ db_config.py
"""

import psycopg2
import psycopg2.extras
from datetime import datetime
import sys
import os

# Import database config
try:
    from db_config import PG_CONFIG
    print(f"✅ Loaded database config: {PG_CONFIG['host']}:{PG_CONFIG['port']}/{PG_CONFIG['database']}")
except ImportError:
    print("❌ Cannot import db_config.py")
    print("Make sure db_config.py exists in the same directory")
    sys.exit(1)

def get_db_connection():
    """Kết nối database PostgreSQL sử dụng config từ db_config.py"""
    try:
        conn = psycopg2.connect(
            host=PG_CONFIG['host'],
            database=PG_CONFIG['database'],
            user=PG_CONFIG['user'],
            password=PG_CONFIG['password'],
            port=PG_CONFIG['port']
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        print(f"📋 Config used:")
        print(f"   Host: {PG_CONFIG['host']}")
        print(f"   Port: {PG_CONFIG['port']}")
        print(f"   Database: {PG_CONFIG['database']}")
        print(f"   User: {PG_CONFIG['user']}")
        print(f"   Password: {'***' if PG_CONFIG['password'] else '(empty)'}")
        return None

def check_password_columns(conn):
    """Kiểm tra các cột password hiện có"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🔍 Checking password columns...")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            AND column_name LIKE '%password%'
            ORDER BY column_name;
        ''')
        
        columns = cursor.fetchall()
        print(f"📋 Found {len(columns)} password-related columns:")
        
        has_password = False
        has_password_hash = False
        
        for col in columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            print(f"  - {col['column_name']}: {col['data_type']} {nullable}")
            
            if col['column_name'] == 'password':
                has_password = True
            elif col['column_name'] == 'password_hash':
                has_password_hash = True
        
        cursor.close()
        return has_password, has_password_hash
        
    except Exception as e:
        print(f"❌ Error checking columns: {e}")
        return False, False

def fix_password_column_server(conn):
    """Fix password column trên server - rename password_hash to password"""
    try:
        cursor = conn.cursor()
        
        print("\n🔧 Starting server password column fix...")
        
        # Step 1: Check current state
        has_password, has_password_hash = check_password_columns(conn)
        
        # Step 2: Handle different scenarios
        if has_password and has_password_hash:
            print("\n🔧 Both columns exist - removing password_hash column...")
            
            # Keep password column, drop password_hash
            cursor.execute('ALTER TABLE "Users" DROP COLUMN IF EXISTS password_hash;')
            print("✅ Dropped password_hash column")
            
        elif has_password_hash and not has_password:
            print("\n🔧 Only password_hash exists - renaming to password...")
            
            # Rename password_hash to password
            cursor.execute('ALTER TABLE "Users" RENAME COLUMN password_hash TO password;')
            print("✅ Renamed password_hash to password")
            
        elif has_password and not has_password_hash:
            print("\n🔧 Only password exists - good!")
            print("✅ Password column already exists and is correct")
            
        else:
            print("\n❌ No password columns found!")
            
            # Create password column
            cursor.execute('''
                ALTER TABLE "Users" 
                ADD COLUMN password TEXT NOT NULL DEFAULT 'temp_password';
            ''')
            print("✅ Created password column")
        
        # Step 3: Verify final structure
        print("\n🔧 Verifying final structure...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            AND column_name LIKE '%password%'
            ORDER BY column_name;
        ''')
        
        final_cols = cursor.fetchall()
        print("📋 Final password columns:")
        for col in final_cols:
            nullable = "NULL" if col[2] == 'YES' else "NOT NULL"
            print(f"  - {col[0]}: {col[1]} {nullable}")
        
        # Step 4: Test INSERT
        print("\n🔧 Testing INSERT with password...")
        
        try:
            cursor.execute('SAVEPOINT test_insert;')
            
            test_username = f"test_password_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            cursor.execute('''
                INSERT INTO "Users" (username, password, role, unit_code, phone, email, mp_balance, is_deleted)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING user_id;
            ''', (test_username, 'test_hash_123', 'member', 'TEST002', '+84123456789', '<EMAIL>', 0, 0))
            
            test_user_id = cursor.fetchone()[0]
            print(f"✅ Test INSERT successful, got user_id: {test_user_id}")
            
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
            print("✅ Test INSERT rolled back")
            
        except Exception as e:
            print(f"❌ Test INSERT failed: {e}")
            cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
            return False
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error fixing password column: {e}")
        return False

def check_users_table_structure(conn):
    """Kiểm tra cấu trúc bảng Users"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("\n📋 Current Users table structure:")
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Users' AND table_schema = 'public'
            ORDER BY ordinal_position;
        ''')
        
        columns = cursor.fetchall()
        for col in columns:
            nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
            default = f" DEFAULT {col['column_default']}" if col['column_default'] else ""
            print(f"  - {col['column_name']}: {col['data_type']} {nullable}{default}")
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking table structure: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Fixing server password column...")
    print("🎯 Goal: Rename password_hash to password for consistency")
    print("=" * 60)
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database")
        print("\n🔧 Troubleshooting:")
        print("1. Check if PostgreSQL is running")
        print("2. Verify db_config.py settings")
        print("3. Check user permissions")
        return
    
    try:
        print("✅ Database connection successful!")
        
        # Check current table structure
        check_users_table_structure(conn)
        
        # Fix password column
        success = fix_password_column_server(conn)
        
        if success:
            conn.commit()
            print("\n✅ Server password column fix completed successfully!")
            print("🎉 Database now uses 'password' column consistently!")
            
            print("\n📝 Next steps:")
            print("1. Update mip_system.py to use 'password' column")
            print("2. Restart gunicorn service: sudo systemctl restart gunicorn")
            print("3. Test registration functionality")
            
        else:
            conn.rollback()
            print("\n❌ Server password column fix failed!")
            
    except Exception as e:
        conn.rollback()
        print(f"\n❌ Unexpected error: {e}")
        
    finally:
        conn.close()
        print("\n🔒 Database connection closed")

if __name__ == "__main__":
    main()
