#!/usr/bin/env python3
"""
Migration: Max Quantity & Dynamic Product Types
Date: 2025-09-04
Time: 11:35 AM - Present
Description: Add max_quantity column and update product types for dynamic tabs
"""

import psycopg2
import psycopg2.extras
import sys
import traceback
from datetime import datetime

# Import database config
try:
    from db_config import PG_CONFIG
    print("✅ Imported database config from db_config.py")

    def get_db_connection():
        """Kết nối database PostgreSQL từ config"""
        try:
            conn = psycopg2.connect(**PG_CONFIG)
            return conn
        except Exception as e:
            print(f"❌ Lỗi kết nối database: {e}")
            return None

except ImportError:
    print("⚠️  db_config.py not found, using fallback connection")

    def get_db_connection():
        """Fallback database connection"""
        try:
            conn = psycopg2.connect(
                host="localhost",
                database="sapmmo",
                user="alandoan",
                password="",
                port="5432"
            )
            return conn
        except Exception as e:
            print(f"❌ Lỗi kết nối database: {e}")
            return None

def check_column_exists(cursor, table_name, column_name):
    """Kiểm tra xem column có tồn tại không"""
    cursor.execute("""
        SELECT COUNT(*) 
        FROM information_schema.columns 
        WHERE table_name = %s AND column_name = %s
    """, (table_name, column_name))
    return cursor.fetchone()[0] > 0

def add_max_quantity_column(cursor):
    """Thêm cột max_quantity vào bảng Products"""
    print("🔧 Adding max_quantity column to Products table...")
    
    if check_column_exists(cursor, 'Products', 'max_quantity'):
        print("   ⚠️  max_quantity column already exists")
        return True
    
    try:
        cursor.execute('''
            ALTER TABLE "Products" 
            ADD COLUMN max_quantity INTEGER DEFAULT NULL
        ''')
        print("   ✅ Added max_quantity column successfully")
        return True
    except Exception as e:
        print(f"   ❌ Error adding max_quantity column: {e}")
        return False

def update_win_product_max_quantity(cursor):
    """Cập nhật max_quantity = 1 cho win_product items"""
    print("🔧 Updating win_product items to have max_quantity = 1...")
    
    try:
        cursor.execute('''
            UPDATE "Products" 
            SET max_quantity = 1 
            WHERE product_type = 'win_product' 
              AND max_quantity IS NULL
        ''')
        
        updated_count = cursor.rowcount
        print(f"   ✅ Updated {updated_count} win_product items")
        return True
    except Exception as e:
        print(f"   ❌ Error updating win_product max_quantity: {e}")
        return False

def update_product_type_for_dynamic_tabs(cursor):
    """Cập nhật product_type cho dynamic tabs"""
    print("🔧 Updating product_type for dynamic tabs...")
    
    try:
        cursor.execute('''
            UPDATE "Products" 
            SET product_type = pt.name
            FROM "ProductTypes" pt
            WHERE "Products".product_type_id = pt.type_id
              AND pt.name = 'Bối Cảnh Live'
              AND "Products".product_type != pt.name
        ''')
        
        updated_count = cursor.rowcount
        print(f"   ✅ Updated {updated_count} products to use ProductType name")
        return True
    except Exception as e:
        print(f"   ❌ Error updating product_type: {e}")
        return False

def update_metadata_for_max_quantity(cursor):
    """Cập nhật metadata cho products có max_quantity"""
    print("🔧 Updating metadata for products with max_quantity...")
    
    try:
        cursor.execute('''
            UPDATE "Products" 
            SET metadata = COALESCE(metadata, '{}'::jsonb) || '{"single_purchase_only": true, "max_quantity": 1}'::jsonb
            WHERE max_quantity = 1 
              AND product_type = 'win_product'
              AND (metadata IS NULL OR NOT (metadata ? 'single_purchase_only'))
        ''')
        
        updated_count = cursor.rowcount
        print(f"   ✅ Updated metadata for {updated_count} products")
        return True
    except Exception as e:
        print(f"   ❌ Error updating metadata: {e}")
        return False

def create_performance_index(cursor):
    """Tạo index cho performance"""
    print("🔧 Creating performance index...")

    try:
        # Check if index already exists
        cursor.execute('''
            SELECT COUNT(*)
            FROM pg_indexes
            WHERE indexname = 'idx_products_max_quantity'
        ''')

        if cursor.fetchone()[0] > 0:
            print("   ⚠️  Index idx_products_max_quantity already exists")
            return True

        # Create index without CONCURRENTLY (since we're in transaction)
        cursor.execute('''
            CREATE INDEX idx_products_max_quantity
            ON "Products" (max_quantity)
            WHERE max_quantity IS NOT NULL
        ''')
        print("   ✅ Created index on max_quantity column")
        return True
    except Exception as e:
        print(f"   ❌ Error creating index: {e}")
        return False

def verify_migration(cursor):
    """Xác minh migration đã thành công"""
    print("🔍 Verifying migration...")
    
    try:
        # Kiểm tra column tồn tại
        if not check_column_exists(cursor, 'Products', 'max_quantity'):
            print("   ❌ max_quantity column not found")
            return False
        
        print("   ✅ max_quantity column exists")
        
        # Thống kê products
        cursor.execute('''
            SELECT 
                product_type,
                COUNT(*) as total_products,
                COUNT(max_quantity) as products_with_max_quantity,
                COUNT(CASE WHEN max_quantity = 1 THEN 1 END) as single_purchase_products
            FROM "Products" 
            WHERE is_active = true
            GROUP BY product_type
            ORDER BY total_products DESC
        ''')
        
        print("\n   📊 Products summary:")
        for row in cursor.fetchall():
            print(f"      {row[0]}: {row[1]} total, {row[2]} with max_qty, {row[3]} single purchase")
        
        # Kiểm tra Bối Cảnh Live products
        cursor.execute('''
            SELECT 
                p.product_id,
                p.name,
                p.product_type,
                p.max_quantity,
                pt.name as producttype_name
            FROM "Products" p
            LEFT JOIN "ProductTypes" pt ON p.product_type_id = pt.type_id
            WHERE pt.name = 'Bối Cảnh Live' OR p.product_type = 'Bối Cảnh Live'
            ORDER BY p.product_id
            LIMIT 5
        ''')
        
        boi_canh_products = cursor.fetchall()
        if boi_canh_products:
            print(f"\n   📋 Sample 'Bối Cảnh Live' products:")
            for row in boi_canh_products:
                print(f"      {row[0]}: {row[1]} (type: {row[2]}, max_qty: {row[3]})")
        
        return True
    except Exception as e:
        print(f"   ❌ Error during verification: {e}")
        return False

def get_final_summary(cursor):
    """Lấy tổng kết cuối cùng"""
    try:
        cursor.execute('SELECT COUNT(*) FROM "Products" WHERE is_active = true')
        total_products = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM "Products" WHERE is_active = true AND max_quantity IS NOT NULL')
        products_with_max_qty = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM "Products" WHERE is_active = true AND max_quantity = 1')
        single_purchase_products = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM "Products" WHERE is_active = true AND product_type = \'Bối Cảnh Live\'')
        boi_canh_live_products = cursor.fetchone()[0]
        
        return {
            'total_products': total_products,
            'products_with_max_qty': products_with_max_qty,
            'single_purchase_products': single_purchase_products,
            'boi_canh_live_products': boi_canh_live_products
        }
    except Exception as e:
        print(f"❌ Error getting summary: {e}")
        return None

def main():
    """Main migration function"""
    print("🚀 Starting Marketplace Updates Migration")
    print("=" * 60)
    print(f"Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Features: Category Filtering, Cart Effects, Max Quantity")
    print("=" * 60)
    
    # Kết nối database
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database. Aborting migration.")
        sys.exit(1)
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Bắt đầu transaction
        print("🔄 Starting transaction...")
        
        # Thực hiện các migration steps
        steps = [
            ("Add max_quantity column", add_max_quantity_column),
            ("Update win_product max_quantity", update_win_product_max_quantity),
            ("Update product_type for dynamic tabs", update_product_type_for_dynamic_tabs),
            ("Update metadata", update_metadata_for_max_quantity),
            ("Create performance index", create_performance_index),
            ("Verify migration", verify_migration)
        ]
        
        success = True
        for step_name, step_func in steps:
            print(f"\n📋 Step: {step_name}")
            if not step_func(cursor):
                success = False
                break
        
        if success:
            # Commit transaction
            conn.commit()
            print("\n✅ Transaction committed successfully")
            
            # Lấy tổng kết
            summary = get_final_summary(cursor)
            if summary:
                print("\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
                print("=" * 50)
                print(f"Total active products: {summary['total_products']}")
                print(f"Products with max_quantity: {summary['products_with_max_qty']}")
                print(f"Single purchase products: {summary['single_purchase_products']}")
                print(f"Bối Cảnh Live products: {summary['boi_canh_live_products']}")
                print("\n✅ Features implemented:")
                print("   - max_quantity column added")
                print("   - Single purchase validation for win_product")
                print("   - Dynamic ProductType tabs")
                print("   - Metadata Builder compatibility")
                print("   - Performance index created")
                print("\n🎯 Ready for deployment!")
        else:
            # Rollback transaction
            conn.rollback()
            print("\n❌ Migration failed. Transaction rolled back.")
            sys.exit(1)
        
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        print("Traceback:")
        traceback.print_exc()
        conn.rollback()
        sys.exit(1)
    
    finally:
        cursor.close()
        conn.close()
        print("\n🔌 Database connection closed")

if __name__ == "__main__":
    main()
