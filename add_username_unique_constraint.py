#!/usr/bin/env python3
"""
Add unique constraint on username column
"""

import psycopg2
import psycopg2.extras

def get_db_connection():
    """Kết nối database PostgreSQL"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def check_existing_constraints(conn):
    """Kiểm tra constraints hiện có"""
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        print("🔍 Checking existing constraints...")
        cursor.execute('''
            SELECT constraint_name, constraint_type
            FROM information_schema.table_constraints
            WHERE table_name = 'Users' AND table_schema = 'public'
            ORDER BY constraint_name;
        ''')
        
        constraints = cursor.fetchall()
        print(f"📋 Found {len(constraints)} constraints:")
        
        has_username_unique = False
        for constraint in constraints:
            print(f"  - {constraint['constraint_name']}: {constraint['constraint_type']}")
            if 'username' in constraint['constraint_name'].lower() and constraint['constraint_type'] == 'UNIQUE':
                has_username_unique = True
        
        cursor.close()
        return has_username_unique
        
    except Exception as e:
        print(f"❌ Error checking constraints: {e}")
        return False

def check_duplicate_usernames(conn):
    """Kiểm tra username trùng lặp"""
    try:
        cursor = conn.cursor()
        
        print("\n🔍 Checking for duplicate usernames...")
        cursor.execute('''
            SELECT username, COUNT(*) as count
            FROM "Users"
            WHERE is_deleted = 0 OR is_deleted IS NULL
            GROUP BY username
            HAVING COUNT(*) > 1
            ORDER BY count DESC;
        ''')
        
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"⚠️  Found {len(duplicates)} duplicate usernames:")
            for dup in duplicates:
                print(f"  - '{dup[0]}': {dup[1]} occurrences")
            return False
        else:
            print("✅ No duplicate usernames found")
            return True
        
        cursor.close()
        
    except Exception as e:
        print(f"❌ Error checking duplicates: {e}")
        return False

def add_unique_constraint(conn):
    """Thêm unique constraint cho username"""
    try:
        cursor = conn.cursor()
        
        print("\n🔧 Adding unique constraint on username...")
        
        # Add unique constraint
        cursor.execute('''
            ALTER TABLE "Users" 
            ADD CONSTRAINT unique_username 
            UNIQUE (username);
        ''')
        
        print("✅ Unique constraint added successfully")
        
        # Verify constraint
        cursor.execute('''
            SELECT constraint_name, constraint_type
            FROM information_schema.table_constraints
            WHERE table_name = 'Users' AND table_schema = 'public'
            AND constraint_name = 'unique_username';
        ''')
        
        result = cursor.fetchone()
        if result:
            print(f"✅ Constraint verified: {result[0]} ({result[1]})")
        else:
            print("❌ Constraint verification failed")
            return False
        
        cursor.close()
        return True
        
    except Exception as e:
        print(f"❌ Error adding constraint: {e}")
        return False

def test_unique_constraint(conn):
    """Test unique constraint"""
    try:
        cursor = conn.cursor()
        
        print("\n🧪 Testing unique constraint...")
        
        # Try to insert duplicate username
        test_username = "test_unique_constraint"
        
        # First insert
        cursor.execute('SAVEPOINT test_unique;')
        cursor.execute('''
            INSERT INTO "Users" (username, password_hash, role, unit_code, mp_balance, is_deleted)
            VALUES (%s, %s, %s, %s, %s, %s)
            RETURNING user_id;
        ''', (test_username, 'test_hash', 'member', 'TEST001', 0, 0))
        
        user_id = cursor.fetchone()[0]
        print(f"✅ First insert successful, user_id: {user_id}")
        
        # Try duplicate insert
        try:
            cursor.execute('''
                INSERT INTO "Users" (username, password_hash, role, unit_code, mp_balance, is_deleted)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING user_id;
            ''', (test_username, 'test_hash2', 'member', 'TEST002', 0, 0))
            
            print("❌ Duplicate insert was allowed - constraint not working!")
            cursor.execute('ROLLBACK TO SAVEPOINT test_unique;')
            return False
            
        except psycopg2.IntegrityError as e:
            print("✅ Duplicate insert correctly rejected!")
            print(f"   Error: {e}")
            cursor.execute('ROLLBACK TO SAVEPOINT test_unique;')
            return True
        
        cursor.close()
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Adding unique constraint on username...")
    print("=" * 60)
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database")
        return
    
    try:
        # Check existing constraints
        has_unique = check_existing_constraints(conn)
        
        if has_unique:
            print("\n✅ Username unique constraint already exists!")
        else:
            # Check for duplicates first
            no_duplicates = check_duplicate_usernames(conn)
            
            if not no_duplicates:
                print("\n❌ Cannot add unique constraint - duplicate usernames exist!")
                print("Please resolve duplicate usernames first.")
                return
            
            # Add unique constraint
            success = add_unique_constraint(conn)
            
            if not success:
                conn.rollback()
                print("\n❌ Failed to add unique constraint!")
                return
        
        # Test constraint
        test_success = test_unique_constraint(conn)
        
        if test_success:
            conn.commit()
            print("\n✅ Unique constraint is working correctly!")
            print("🎉 Username uniqueness is now enforced!")
        else:
            conn.rollback()
            print("\n❌ Unique constraint test failed!")
            
    except Exception as e:
        conn.rollback()
        print(f"\n❌ Unexpected error: {e}")
        
    finally:
        conn.close()
        print("\n🔒 Database connection closed")

if __name__ == "__main__":
    main()
