#!/usr/bin/env python3
"""
Simple test for registration redirect functionality
"""

from flask import Flask, url_for
import sys
import os

# Add current directory to path to import mip_system
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_url_generation():
    """Test URL generation for redirect"""
    
    print("🧪 Testing URL generation...")
    
    try:
        # Import the Flask app
        from mip_system import app
        
        with app.test_request_context():
            # Test URL generation
            test_username = "testuser123"
            success_msg = "Đăng ký thành công! Vui lòng đăng nhập."
            
            # Generate URL like in the code
            login_url = url_for('login', username=test_username, success=success_msg)
            
            print(f"✅ Generated URL: {login_url}")
            
            # Check URL format
            expected_parts = [
                '/login',
                f'username={test_username}',
                'success='
            ]
            
            for part in expected_parts:
                if part in login_url:
                    print(f"✅ URL contains: {part}")
                else:
                    print(f"❌ URL missing: {part}")
                    return False
            
            return True
            
    except Exception as e:
        print(f"❌ Error testing URL generation: {e}")
        return False

def test_template_rendering():
    """Test template rendering with username and success"""
    
    print("\n🧪 Testing template rendering...")
    
    try:
        from mip_system import app
        
        with app.test_client() as client:
            # Test GET request to login with parameters
            test_username = "testuser123"
            success_msg = "Đăng ký thành công! Vui lòng đăng nhập."
            
            response = client.get(f'/login?username={test_username}&success={success_msg}')
            
            print(f"✅ Response status: {response.status_code}")
            
            if response.status_code == 200:
                html = response.get_data(as_text=True)
                
                # Check username pre-fill
                if f'value="{test_username}"' in html:
                    print(f"✅ Username '{test_username}' is pre-filled in form")
                else:
                    print(f"❌ Username '{test_username}' is NOT pre-filled")
                    return False
                
                # Check success message
                if 'alert-success' in html:
                    print("✅ Success alert div is present")
                else:
                    print("❌ Success alert div is missing")
                    return False
                
                if success_msg in html:
                    print("✅ Success message text is present")
                else:
                    print("❌ Success message text is missing")
                    return False
                
                return True
            else:
                print(f"❌ Failed to load login page: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing template rendering: {e}")
        return False

def test_normalize_username():
    """Test username normalization"""
    
    print("\n🧪 Testing username normalization...")
    
    try:
        from mip_system import normalize_username
        
        test_cases = [
            ('Test.User-123', 'testuser123'),
            ('ADMIN_USER', 'admin_user'),
            ('<EMAIL>', 'userdomaincom'),
            ('Simple', 'simple')
        ]
        
        for original, expected in test_cases:
            result = normalize_username(original)
            if result == expected:
                print(f"✅ '{original}' → '{result}'")
            else:
                print(f"❌ '{original}' → '{result}' (expected: '{expected}')")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing username normalization: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Testing registration redirect functionality...")
    print("=" * 60)
    
    # Test 1: URL generation
    success1 = test_url_generation()
    
    # Test 2: Template rendering
    success2 = test_template_rendering()
    
    # Test 3: Username normalization
    success3 = test_normalize_username()
    
    print("\n" + "=" * 60)
    print("📋 Test Results:")
    print(f"  - URL generation: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"  - Template rendering: {'✅ PASS' if success2 else '❌ FAIL'}")
    print(f"  - Username normalization: {'✅ PASS' if success3 else '❌ FAIL'}")
    
    if success1 and success2 and success3:
        print("\n🎉 All tests passed! Registration redirect is working!")
    else:
        print("\n❌ Some tests failed!")

if __name__ == "__main__":
    main()
