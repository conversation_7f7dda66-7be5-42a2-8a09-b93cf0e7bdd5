from flask import Blueprint, jsonify, request, session
import psycopg2
import psycopg2.extras
from datetime import datetime, timedelta
import json
from functools import wraps
import os
from db_config import PG_CONFIG
from db_utils import get_db_connection

# Tạo blueprint
transactions_bp = Blueprint('transactions', __name__)

# Decorator kiểm tra đăng nhập
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return jsonify({'error': 'Unauthorized'}), 403

        # Lấy thông tin vai trò và team_id của user
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT role, team_id FROM "Users" WHERE user_id = %s', (session['user_id'],))
        result = cursor.fetchone()
        conn.close()

        if not result:
            return jsonify({'error': 'User not found'}), 404

        user_role, user_team_id = result
        return f(user_role, user_team_id, *args, **kwargs)

    return decorated_function

# API lấy thống kê giao dịch (cho admin)
@transactions_bp.route('/api/transactions/stats')
@login_required
def get_transaction_stats(user_role, user_team_id):
    if user_role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    start_date = request.args.get('start_date', datetime.now().strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Tổng MP nạp vào
        cursor.execute('''
            SELECT COALESCE(SUM(amount), 0)
            FROM "MPTransactions"
            WHERE transaction_type = 'ADD'
            AND created_at BETWEEN %s AND %s
            AND is_deleted = 0
        ''', (f"{start_date} 00:00:00", f"{end_date} 23:59:59"))
        total_deposit = cursor.fetchone()[0]

        # Tổng MP sử dụng (bao gồm cả marketplace purchases)
        cursor.execute('''
            SELECT COALESCE(SUM(amount), 0)
            FROM "MPTransactions"
            WHERE transaction_type IN ('SPEND', 'PURCHASE')
            AND created_at BETWEEN %s AND %s
            AND is_deleted = 0
        ''', (f"{start_date} 00:00:00", f"{end_date} 23:59:59"))
        total_spent = cursor.fetchone()[0]

        # Tổng số dư MP hiện tại
        cursor.execute('''
            SELECT COALESCE(SUM(mp_balance), 0)
            FROM "Users"
            WHERE is_deleted = 0
        ''')
        total_balance = cursor.fetchone()[0]

        # Dữ liệu biểu đồ giao dịch theo thời gian
        chart_data = get_transaction_chart_data(cursor, start_date, end_date)

        # Dữ liệu phân bổ MP theo người dùng
        user_distribution = get_user_distribution(cursor)

        return jsonify({
            'success': True,
            'total_deposit': total_deposit,
            'total_spent': total_spent,
            'total_balance': total_balance,
            'chart_data': chart_data,
            'user_distribution': user_distribution
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

    finally:
        conn.close()

# API lấy thống kê giao dịch cho user
@transactions_bp.route('/api/transactions/user/stats')
@login_required
def get_user_transaction_stats(user_role, user_team_id):
    user_id = session['user_id']
    start_date = request.args.get('start_date', datetime.now().strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Tổng MP nạp vào
        cursor.execute('''
            SELECT COALESCE(SUM(amount), 0)
            FROM "MPTransactions"
            WHERE transaction_type = 'ADD'
            AND user_id = %s
            AND created_at BETWEEN %s AND %s
            AND is_deleted = 0
        ''', (user_id, f"{start_date} 00:00:00", f"{end_date} 23:59:59"))
        total_deposit = cursor.fetchone()[0]

        # Tổng MP sử dụng (bao gồm cả marketplace purchases)
        cursor.execute('''
            SELECT COALESCE(SUM(amount), 0)
            FROM "MPTransactions"
            WHERE transaction_type IN ('SPEND', 'PURCHASE')
            AND user_id = %s
            AND created_at BETWEEN %s AND %s
            AND is_deleted = 0
        ''', (user_id, f"{start_date} 00:00:00", f"{end_date} 23:59:59"))
        total_spent = cursor.fetchone()[0]

        # Số dư MP hiện tại
        cursor.execute('''
            SELECT mp_balance
            FROM "Users"
            WHERE user_id = %s
        ''', (user_id,))
        current_balance = cursor.fetchone()[0]

        # Dữ liệu biểu đồ giao dịch theo thời gian
        chart_data = get_user_transaction_chart_data(cursor, user_id, start_date, end_date)

        return jsonify({
            'success': True,
            'total_deposit': total_deposit,
            'total_spent': total_spent,
            'current_balance': current_balance,
            'chart_data': chart_data
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

    finally:
        conn.close()

# API lấy danh sách người dùng
@transactions_bp.route('/api/transactions/users')
@login_required
def get_users(user_role, user_team_id):
    if user_role != 'admin':
        return jsonify({'error': 'Unauthorized'}), 403

    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    per_page = 10

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Xây dựng câu truy vấn
        query = '''
            SELECT u.user_id, u.username, u.team_id, t.team_name, u.mp_balance,
                   (SELECT COALESCE(SUM(amount), 0) FROM "MPTransactions"
                    WHERE user_id = u.user_id AND transaction_type = 'SPEND' AND is_deleted = 0) as mp_spent
            FROM "Users" u
            LEFT JOIN "Teams" t ON u.team_id = t.team_id
            WHERE u.is_deleted = 0
        '''
        params = []

        if search:
            query += " AND (u.username LIKE %s OR t.team_name LIKE %s)"
            params.extend([f'%{search}%', f'%{search}%'])

        # Đếm tổng số người dùng
        count_query = f"SELECT COUNT(*) FROM ({query}) AS subquery"
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]

        # Thêm phân trang
        query += " ORDER BY u.user_id DESC LIMIT %s OFFSET %s"
        params.extend([per_page, (page - 1) * per_page])

        cursor.execute(query, params)
        users = cursor.fetchall()

        # Tạo danh sách người dùng
        user_list = []
        for user in users:
            user_list.append({
                'user_id': user[0],
                'username': user[1],
                'team_id': user[2],
                'team_name': user[3],
                'mp_balance': user[4],
                'mp_spent': user[5]
            })

        # Tạo thông tin phân trang
        pagination = {
            'total': total,
            'per_page': per_page,
            'current_page': page,
            'total_pages': (total + per_page - 1) // per_page,
            'start': (page - 1) * per_page + 1 if total > 0 else 0,
            'end': min(page * per_page, total)
        }

        return jsonify({
            'success': True,
            'users': user_list,
            'pagination': pagination
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

    finally:
        conn.close()

# API lấy danh sách giao dịch
@transactions_bp.route('/api/transactions')
@login_required
def get_transactions(user_role, user_team_id):
    page = request.args.get('page', 1, type=int)
    transaction_type = request.args.get('type', 'all')
    user_id = request.args.get('user_id', None)
    start_date = request.args.get('start_date', datetime.now().strftime('%Y-%m-%d'))
    end_date = request.args.get('end_date', datetime.now().strftime('%Y-%m-%d'))
    per_page = 10

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Xây dựng câu truy vấn (đã lưu VN time)
        query = '''
            SELECT t.transaction_id, t.user_id, t.amount, t.transaction_type,
                   t.description, t.created_at,
                   t.created_by, u.username as created_by_name
            FROM "MPTransactions" t
            JOIN "Users" u ON t.created_by = u.user_id
            WHERE t.is_deleted = 0
            AND DATE(t.created_at) BETWEEN %s AND %s
        '''
        params = [start_date, end_date]

        # Lọc theo loại giao dịch
        if transaction_type != 'all':
            if transaction_type == 'deposit':
                query += " AND t.transaction_type = %s"
                params.append('ADD')
            elif transaction_type == 'spending':
                # Include both SPEND and PURCHASE for backward compatibility
                query += " AND t.transaction_type IN (%s, %s)"
                params.extend(['SPEND', 'PURCHASE'])
            else:
                query += " AND t.transaction_type = %s"
                params.append(transaction_type)

        # Lọc theo user_id
        if user_id:
            query += " AND t.user_id = %s"
            params.append(user_id)
        elif user_role != 'admin':
            # Nếu không phải admin, chỉ xem giao dịch của mình
            query += " AND t.user_id = %s"
            params.append(session['user_id'])

        # Đếm tổng số giao dịch
        count_query = f"SELECT COUNT(*) FROM ({query}) AS subquery"
        cursor.execute(count_query, params)
        total = cursor.fetchone()[0]

        # Thêm phân trang
        query += " ORDER BY t.created_at DESC LIMIT %s OFFSET %s"
        params.extend([per_page, (page - 1) * per_page])

        cursor.execute(query, params)
        transactions = cursor.fetchall()

        # Tạo danh sách giao dịch
        transaction_list = []
        for transaction in transactions:
            transaction_list.append({
                'transaction_id': transaction[0],
                'user_id': transaction[1],
                'amount': transaction[2],
                'transaction_type': transaction[3],
                'description': transaction[4],
                'created_at': transaction[5].isoformat() if transaction[5] else None,
                'created_by': transaction[6],
                'created_by_name': transaction[7]
            })

        # Tạo thông tin phân trang
        pagination = {
            'total': total,
            'per_page': per_page,
            'current_page': page,
            'total_pages': (total + per_page - 1) // per_page,
            'start': (page - 1) * per_page + 1 if total > 0 else 0,
            'end': min(page * per_page, total)
        }

        return jsonify({
            'success': True,
            'transactions': transaction_list,
            'pagination': pagination
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

    finally:
        conn.close()

# API lấy thông tin webhook
@transactions_bp.route('/api/transactions/webhook-info/<int:user_id>')
@login_required
def get_webhook_info(user_role, user_team_id, user_id):
    if user_role != 'admin' and session['user_id'] != user_id:
        return jsonify({'error': 'Unauthorized'}), 403

    conn = get_db_connection()
    cursor = conn.cursor()

    try:
        # Lấy unit_code của user
        cursor.execute('SELECT unit_code FROM "Users" WHERE user_id = %s', (user_id,))
        result = cursor.fetchone()

        if not result:
            return jsonify({
                'success': False,
                'error': 'User not found'
            })

        unit_code = result[0] or f"MIP{user_id}"

        # Lấy lịch sử webhook
        cursor.execute('''
            SELECT d.id, d.amount, d.status, d.created_at, d.completed_at, d.transfer_content, t.content
            FROM "Deposits" d
            LEFT JOIN "Transactions" t ON d.transaction_id = t.id
            WHERE d.user_id = %s
            ORDER BY d.created_at DESC
            LIMIT 10
        ''', (user_id,))

        webhook_history = []
        for deposit in cursor.fetchall():
            webhook_history.append({
                'id': deposit[0],
                'amount': deposit[1],
                'status': deposit[2],
                'created_at': deposit[3],
                'completed_at': deposit[4],
                'transfer_content': deposit[5],
                'content': deposit[6] or deposit[5]
            })

        return jsonify({
            'success': True,
            'unit_code': unit_code,
            'webhook_history': webhook_history
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

    finally:
        conn.close()

# Hàm lấy dữ liệu biểu đồ giao dịch
def get_transaction_chart_data(cursor, start_date, end_date):
    # Chuyển đổi chuỗi ngày thành đối tượng datetime
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d')

    # Tính số ngày giữa start_date và end_date
    days_diff = (end_dt - start_dt).days + 1

    # Xác định định dạng thời gian dựa trên khoảng thời gian
    if days_diff <= 1:
        # Theo giờ
        format_str = '%Y-%m-%d %H:00:00'
        group_by = "to_char(created_at, 'YYYY-MM-DD HH24:00:00')"
        interval = 'hour'
    elif days_diff <= 31:
        # Theo ngày
        format_str = '%Y-%m-%d'
        group_by = "to_char(created_at, 'YYYY-MM-DD')"
        interval = 'day'
    elif days_diff <= 366:
        # Theo tuần
        format_str = '%Y-%W'
        group_by = "to_char(created_at, 'YYYY-IW')"
        interval = 'week'
    else:
        # Theo tháng
        format_str = '%Y-%m'
        group_by = "to_char(created_at, 'YYYY-MM')"
        interval = 'month'

    # Lấy dữ liệu MP nạp vào theo thời gian
    cursor.execute(f'''
        SELECT {group_by} as time_group, COALESCE(SUM(amount), 0) as total
        FROM "MPTransactions"
        WHERE transaction_type = 'ADD'
        AND created_at BETWEEN %s AND %s
        AND is_deleted = 0
        GROUP BY time_group
        ORDER BY time_group
    ''', (f"{start_date} 00:00:00", f"{end_date} 23:59:59"))

    deposits_data = {}
    for row in cursor.fetchall():
        deposits_data[row[0]] = row[1]

    # Lấy dữ liệu MP sử dụng theo thời gian (bao gồm cả marketplace purchases)
    cursor.execute(f'''
        SELECT {group_by} as time_group, COALESCE(SUM(amount), 0) as total
        FROM "MPTransactions"
        WHERE transaction_type IN ('SPEND', 'PURCHASE')
        AND created_at BETWEEN %s AND %s
        AND is_deleted = 0
        GROUP BY time_group
        ORDER BY time_group
    ''', (f"{start_date} 00:00:00", f"{end_date} 23:59:59"))

    spending_data = {}
    for row in cursor.fetchall():
        spending_data[row[0]] = row[1]

    # Tạo danh sách các mốc thời gian
    labels = []
    deposits = []
    spending = []

    current_dt = start_dt
    while current_dt <= end_dt:
        if interval == 'hour':
            # Tạo 24 mốc giờ trong ngày
            for hour in range(24):
                time_key = current_dt.strftime('%Y-%m-%d') + f' {hour:02d}:00:00'
                label = f'{hour:02d}:00'

                labels.append(label)
                deposits.append(deposits_data.get(time_key, 0))
                spending.append(spending_data.get(time_key, 0))
            break
        elif interval == 'day':
            time_key = current_dt.strftime(format_str)
            label = current_dt.strftime('%d/%m')
        elif interval == 'week':
            time_key = current_dt.strftime(format_str)
            label = f'Tuần {int(current_dt.strftime("%W")) + 1}'
        else:  # month
            time_key = current_dt.strftime(format_str)
            label = current_dt.strftime('%m/%Y')

        if interval != 'hour':
            labels.append(label)
            deposits.append(deposits_data.get(time_key, 0))
            spending.append(spending_data.get(time_key, 0))

            # Tăng thời gian
            if interval == 'day':
                current_dt += timedelta(days=1)
            elif interval == 'week':
                current_dt += timedelta(days=7)
            else:  # month
                # Tăng 1 tháng
                if current_dt.month == 12:
                    current_dt = current_dt.replace(year=current_dt.year + 1, month=1)
                else:
                    current_dt = current_dt.replace(month=current_dt.month + 1)

    return {
        'labels': labels,
        'deposits': deposits,
        'spending': spending
    }

# Hàm lấy dữ liệu biểu đồ giao dịch cho user
def get_user_transaction_chart_data(cursor, user_id, start_date, end_date):
    # Chuyển đổi chuỗi ngày thành đối tượng datetime
    start_dt = datetime.strptime(start_date, '%Y-%m-%d')
    end_dt = datetime.strptime(end_date, '%Y-%m-%d')

    # Tính số ngày giữa start_date và end_date
    days_diff = (end_dt - start_dt).days + 1

    # Xác định định dạng thời gian dựa trên khoảng thời gian
    if days_diff <= 1:
        # Theo giờ
        format_str = '%Y-%m-%d %H:00:00'
        group_by = "to_char(created_at, 'YYYY-MM-DD HH24:00:00')"
        interval = 'hour'
    elif days_diff <= 31:
        # Theo ngày
        format_str = '%Y-%m-%d'
        group_by = "to_char(created_at, 'YYYY-MM-DD')"
        interval = 'day'
    elif days_diff <= 366:
        # Theo tuần
        format_str = '%Y-%W'
        group_by = "to_char(created_at, 'YYYY-IW')"
        interval = 'week'
    else:
        # Theo tháng
        format_str = '%Y-%m'
        group_by = "to_char(created_at, 'YYYY-MM')"
        interval = 'month'

    # Lấy dữ liệu MP nạp vào theo thời gian
    cursor.execute(f'''
        SELECT {group_by} as time_group, COALESCE(SUM(amount), 0) as total
        FROM "MPTransactions"
        WHERE transaction_type = 'ADD'
        AND user_id = %s
        AND created_at BETWEEN %s AND %s
        AND is_deleted = 0
        GROUP BY time_group
        ORDER BY time_group
    ''', (user_id, f"{start_date} 00:00:00", f"{end_date} 23:59:59"))

    deposits_data = {}
    for row in cursor.fetchall():
        deposits_data[row[0]] = row[1]

    # Lấy dữ liệu MP sử dụng theo thời gian (bao gồm cả marketplace purchases)
    cursor.execute(f'''
        SELECT {group_by} as time_group, COALESCE(SUM(amount), 0) as total
        FROM "MPTransactions"
        WHERE transaction_type IN ('SPEND', 'PURCHASE')
        AND user_id = %s
        AND created_at BETWEEN %s AND %s
        AND is_deleted = 0
        GROUP BY time_group
        ORDER BY time_group
    ''', (user_id, f"{start_date} 00:00:00", f"{end_date} 23:59:59"))

    spending_data = {}
    for row in cursor.fetchall():
        spending_data[row[0]] = row[1]

    # Tạo danh sách các mốc thời gian
    labels = []
    deposits = []
    spending = []

    current_dt = start_dt
    while current_dt <= end_dt:
        if interval == 'hour':
            # Tạo 24 mốc giờ trong ngày
            for hour in range(24):
                time_key = current_dt.strftime('%Y-%m-%d') + f' {hour:02d}:00:00'
                label = f'{hour:02d}:00'

                labels.append(label)
                deposits.append(deposits_data.get(time_key, 0))
                spending.append(spending_data.get(time_key, 0))
            break
        elif interval == 'day':
            time_key = current_dt.strftime(format_str)
            label = current_dt.strftime('%d/%m')
        elif interval == 'week':
            time_key = current_dt.strftime(format_str)
            label = f'Tuần {int(current_dt.strftime("%W")) + 1}'
        else:  # month
            time_key = current_dt.strftime(format_str)
            label = current_dt.strftime('%m/%Y')

        if interval != 'hour':
            labels.append(label)
            deposits.append(deposits_data.get(time_key, 0))
            spending.append(spending_data.get(time_key, 0))

            # Tăng thời gian
            if interval == 'day':
                current_dt += timedelta(days=1)
            elif interval == 'week':
                current_dt += timedelta(days=7)
            else:  # month
                # Tăng 1 tháng
                if current_dt.month == 12:
                    current_dt = current_dt.replace(year=current_dt.year + 1, month=1)
                else:
                    current_dt = current_dt.replace(month=current_dt.month + 1)

    return {
        'labels': labels,
        'deposits': deposits,
        'spending': spending
    }

# Hàm lấy dữ liệu phân bổ MP theo người dùng
def get_user_distribution(cursor):
    # Lấy top 5 người dùng có số dư MP cao nhất
    cursor.execute('''
        SELECT username, mp_balance
        FROM "Users"
        WHERE is_deleted = 0
        ORDER BY mp_balance DESC
        LIMIT 5
    ''')

    top_users = cursor.fetchall()

    # Lấy tổng số dư MP của tất cả người dùng
    cursor.execute('''
        SELECT COALESCE(SUM(mp_balance), 0)
        FROM "Users"
        WHERE is_deleted = 0
    ''')

    total_balance = cursor.fetchone()[0]

    # Tính tổng số dư MP của top 5 người dùng
    top_users_balance = sum(user[1] for user in top_users)

    # Tính số dư MP của những người dùng còn lại
    others_balance = total_balance - top_users_balance

    # Tạo dữ liệu cho biểu đồ
    labels = [user[0] for user in top_users]
    data = [user[1] for user in top_users]

    # Thêm "Khác" nếu có
    if others_balance > 0:
        labels.append('Khác')
        data.append(others_balance)

    return {
        'labels': labels,
        'data': data
    }
