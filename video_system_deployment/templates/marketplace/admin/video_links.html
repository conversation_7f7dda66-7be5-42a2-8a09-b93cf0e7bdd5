{% extends "base_coreui.html" %}
{% block title %}Q<PERSON><PERSON>n lý Video Links - Marketplace{% endblock %}

{% block head_extra %}
<style>
    .status-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
    .video-count-badge {
        background: linear-gradient(45deg, #00C6AE, #00a693);
        color: white;
        font-weight: bold;
    }
    .drive-link {
        color: #4285f4;
        text-decoration: none;
        font-size: 0.9rem;
        max-width: 200px;
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .drive-link:hover {
        text-decoration: underline;
    }
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .filter-section {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    .table td {
        vertical-align: middle;
    }
    .description-cell {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .action-buttons {
        white-space: nowrap;
    }
    .pagination-info {
        color: #6c757d;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="cil-video text-primary"></i> Quản lý Video Links</h2>
                    <p class="text-muted">Quản lý các link Google Drive chứa video để bán</p>
                </div>
                <button class="btn btn-primary" onclick="showCreateLinkModal()">
                    <i class="cil-plus"></i> Thêm Video Link
                </button>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalLinks">0</h4>
                            <p class="mb-0">Tổng Links</p>
                        </div>
                        <div class="align-self-center">
                            <i class="cil-video" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="availableLinks">0</h4>
                            <p class="mb-0">Có sẵn</p>
                        </div>
                        <div class="align-self-center">
                            <i class="cil-check" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="soldLinks">0</h4>
                            <p class="mb-0">Đã bán</p>
                        </div>
                        <div class="align-self-center">
                            <i class="cil-ban" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 id="totalVideos">0</h4>
                            <p class="mb-0">Tổng Videos</p>
                        </div>
                        <div class="align-self-center">
                            <i class="cil-media-play" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">Trạng thái</label>
                <select class="form-select" id="statusFilter" onchange="loadVideoLinks()">
                    <option value="">Tất cả</option>
                    <option value="available">Có sẵn</option>
                    <option value="sold">Đã bán</option>
                    <option value="reserved">Đã đặt</option>
                    <option value="inactive">Không hoạt động</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Loại video</label>
                <select class="form-select" id="videoTypeFilter" onchange="loadVideoLinks()">
                    <option value="">Tất cả loại</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Số lượng video</label>
                <select class="form-select" id="videoCountFilter" onchange="loadVideoLinks()">
                    <option value="">Tất cả</option>
                    <option value="1-50">1-50 videos</option>
                    <option value="51-100">51-100 videos</option>
                    <option value="101+">101+ videos</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Tìm kiếm</label>
                <input type="text" class="form-control" id="searchInput" placeholder="Tên link..." onkeyup="loadVideoLinks()">
            </div>
        </div>
    </div>

    <!-- Video Links List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Danh sách Video Links</h5>
                </div>
                <div class="card-body">
                    <!-- Pagination Info -->
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="pagination-info" id="paginationInfo">
                            Đang tải...
                        </div>
                        <div>
                            <select class="form-select form-select-sm" id="pageSizeSelect" onchange="changePageSize()" style="width: auto;">
                                <option value="20">20 / trang</option>
                                <option value="50">50 / trang</option>
                                <option value="100">100 / trang</option>
                            </select>
                        </div>
                    </div>

                    <!-- Video Links Table -->
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th style="width: 40px;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                                        </div>
                                    </th>
                                    <th style="width: 120px;">Tên Link</th>
                                    <th style="width: 100px;">Videos</th>
                                    <th style="width: 120px;">Loại Video</th>
                                    <th style="width: 200px;">Google Drive</th>
                                    <th>Mô tả</th>
                                    <th style="width: 100px;">Trạng thái</th>
                                    <th style="width: 120px;">Ngày tạo</th>
                                    <th style="width: 120px;">Hành động</th>
                                </tr>
                            </thead>
                            <tbody id="videoLinksTableBody">
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 mb-0">Đang tải dữ liệu...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <button class="btn btn-outline-danger btn-sm" id="bulkDeleteBtn" onclick="bulkDeleteVideoLinks()" style="display: none;">
                                <i class="cil-trash"></i> Xóa đã chọn
                            </button>
                        </div>
                        <nav aria-label="Video links pagination">
                            <ul class="pagination pagination-sm mb-0" id="paginationNav">
                                <!-- Pagination will be generated here -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create/Edit Video Link Modal -->
<div class="modal fade" id="videoLinkModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">Thêm Video Link</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="videoLinkForm">
                    <input type="hidden" id="linkId" name="link_id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tên Link <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="linkName" name="name" 
                                       placeholder="Link 0901" required>
                                <div class="form-text">Ví dụ: Link 0901, Link 0902...</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Số lượng video <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="videoCount" name="video_count" 
                                       min="1" placeholder="50" required>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Link Google Drive <span class="text-danger">*</span></label>
                        <input type="url" class="form-control" id="driveUrl" name="drive_url" 
                               placeholder="https://drive.google.com/drive/folders/..." required>
                        <div class="form-text">Link chia sẻ Google Drive folder chứa video</div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Loại video</label>
                                <input type="text" class="form-control" id="videoType" name="video_type" 
                                       placeholder="Nuôi kênh TikTok">
                                <div class="form-text">Ví dụ: Nuôi kênh TikTok, Viral Content...</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Trạng thái</label>
                                <select class="form-select" id="linkStatus" name="status">
                                    <option value="available">Có sẵn</option>
                                    <option value="reserved">Đã đặt</option>
                                    <option value="inactive">Không hoạt động</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Mô tả</label>
                        <textarea class="form-control" id="linkDescription" name="description" rows="3"
                                  placeholder="Mô tả chi tiết về video trong link..."></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="saveVideoLink()">
                    <span id="saveButtonText">Lưu</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Xác nhận xóa</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Bạn có chắc chắn muốn xóa video link này?</p>
                <p class="text-danger"><strong>Lưu ý:</strong> Hành động này không thể hoàn tác!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">Xóa</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let videoLinks = [];
let editingLinkId = null;
let deletingLinkId = null;

// Initialize page
$(document).ready(function() {
    loadVideoLinks();
    loadVideoTypes();
});

// Global variables for pagination
let currentPage = 1;
let pageSize = 20;
let totalPages = 1;
let selectedLinkIds = [];

function loadVideoLinks(page = 1) {
    currentPage = page;

    const params = new URLSearchParams({
        status: document.getElementById('statusFilter').value,
        video_type: document.getElementById('videoTypeFilter').value,
        video_count: document.getElementById('videoCountFilter').value,
        search: document.getElementById('searchInput').value,
        page: page,
        page_size: pageSize
    });

    // Show loading
    document.getElementById('videoLinksTableBody').innerHTML = `
        <tr>
            <td colspan="9" class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2 mb-0">Đang tải dữ liệu...</p>
            </td>
        </tr>
    `;

    fetch(`/api/admin/marketplace/video-links?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                videoLinks = data.video_links;
                totalPages = data.pagination.total_pages;
                displayVideoLinks(data.video_links);
                updatePaginationInfo(data.pagination);
                updatePagination(data.pagination);
                updateStats(data.stats);
            } else {
                showToast('Lỗi: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Error loading video links:', error);
            showToast('Lỗi kết nối', 'danger');
        });
}

function displayVideoLinks(links) {
    const tableBody = document.getElementById('videoLinksTableBody');

    if (links.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-4">
                    <i class="cil-video" style="font-size: 3rem; color: #ccc;"></i>
                    <p class="text-muted mt-2 mb-2">Chưa có video link nào</p>
                    <button class="btn btn-primary" onclick="showCreateLinkModal()">
                        <i class="cil-plus"></i> Thêm Video Link đầu tiên
                    </button>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = '';

    links.forEach(link => {
        const row = document.createElement('tr');

        const statusBadge = getStatusBadge(link.status);
        const videoTypeBadge = link.video_type ?
            `<span class="badge bg-info">${link.video_type}</span>` :
            '<span class="text-muted">-</span>';

        const description = link.description ?
            (link.description.length > 50 ? link.description.substring(0, 50) + '...' : link.description) :
            '<span class="text-muted">Không có mô tả</span>';

        row.innerHTML = `
            <td>
                <div class="form-check">
                    <input class="form-check-input link-checkbox" type="checkbox" value="${link.link_id}" onchange="updateSelectedLinks()">
                </div>
            </td>
            <td>
                <strong class="text-primary">${link.name}</strong>
            </td>
            <td>
                <span class="badge video-count-badge">${link.video_count}</span>
            </td>
            <td>${videoTypeBadge}</td>
            <td>
                <a href="${link.drive_url}" target="_blank" class="drive-link" title="${link.drive_url}">
                    <i class="cil-external-link me-1"></i>Google Drive
                </a>
            </td>
            <td>
                <div class="description-cell" title="${link.description || ''}">${description}</div>
            </td>
            <td>${statusBadge}</td>
            <td>
                <small class="text-muted">${formatDate(link.created_at)}</small>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-primary me-1" onclick="editVideoLink(${link.link_id})" title="Sửa">
                        <i class="cil-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteVideoLink(${link.link_id})" title="Xóa">
                        <i class="cil-trash"></i>
                    </button>
                </div>
            </td>
        `;

        tableBody.appendChild(row);
    });
}

function updateStats(stats) {
    document.getElementById('totalLinks').textContent = stats.total || 0;
    document.getElementById('availableLinks').textContent = stats.available || 0;
    document.getElementById('soldLinks').textContent = stats.sold || 0;
    document.getElementById('totalVideos').textContent = stats.total_videos || 0;
}

function updatePaginationInfo(pagination) {
    const info = document.getElementById('paginationInfo');
    const start = (pagination.current_page - 1) * pagination.page_size + 1;
    const end = Math.min(pagination.current_page * pagination.page_size, pagination.total_items);

    info.textContent = `Hiển thị ${start}-${end} trong tổng số ${pagination.total_items} video links`;
}

function updatePagination(pagination) {
    const nav = document.getElementById('paginationNav');
    nav.innerHTML = '';

    if (pagination.total_pages <= 1) return;

    // Previous button
    if (pagination.current_page > 1) {
        nav.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadVideoLinks(${pagination.current_page - 1})">Trước</a>
            </li>
        `;
    }

    // Page numbers
    const startPage = Math.max(1, pagination.current_page - 2);
    const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

    if (startPage > 1) {
        nav.innerHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadVideoLinks(1)">1</a></li>`;
        if (startPage > 2) {
            nav.innerHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const activeClass = i === pagination.current_page ? 'active' : '';
        nav.innerHTML += `
            <li class="page-item ${activeClass}">
                <a class="page-link" href="#" onclick="loadVideoLinks(${i})">${i}</a>
            </li>
        `;
    }

    if (endPage < pagination.total_pages) {
        if (endPage < pagination.total_pages - 1) {
            nav.innerHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
        }
        nav.innerHTML += `<li class="page-item"><a class="page-link" href="#" onclick="loadVideoLinks(${pagination.total_pages})">${pagination.total_pages}</a></li>`;
    }

    // Next button
    if (pagination.current_page < pagination.total_pages) {
        nav.innerHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="loadVideoLinks(${pagination.current_page + 1})">Sau</a>
            </li>
        `;
    }
}

function changePageSize() {
    pageSize = parseInt(document.getElementById('pageSizeSelect').value);
    loadVideoLinks(1);
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.link-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });

    updateSelectedLinks();
}

function updateSelectedLinks() {
    const checkboxes = document.querySelectorAll('.link-checkbox:checked');
    selectedLinkIds = Array.from(checkboxes).map(cb => parseInt(cb.value));

    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');

    if (selectedLinkIds.length > 0) {
        bulkDeleteBtn.style.display = 'inline-block';
        bulkDeleteBtn.textContent = `Xóa ${selectedLinkIds.length} đã chọn`;
    } else {
        bulkDeleteBtn.style.display = 'none';
    }

    // Update select all checkbox state
    const allCheckboxes = document.querySelectorAll('.link-checkbox');
    const checkedCheckboxes = document.querySelectorAll('.link-checkbox:checked');

    if (checkedCheckboxes.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (checkedCheckboxes.length === allCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

function bulkDeleteVideoLinks() {
    if (selectedLinkIds.length === 0) return;

    if (!confirm(`Bạn có chắc chắn muốn xóa ${selectedLinkIds.length} video links đã chọn?`)) {
        return;
    }

    fetch('/api/admin/marketplace/video-links/bulk-delete', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            link_ids: selectedLinkIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(`Đã xóa ${selectedLinkIds.length} video links`, 'success');
            selectedLinkIds = [];
            loadVideoLinks(currentPage);
        } else {
            showToast('Lỗi: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Lỗi kết nối', 'danger');
    });
}

function getStatusBadge(status) {
    const badges = {
        'available': '<span class="badge bg-success">Có sẵn</span>',
        'sold': '<span class="badge bg-danger">Đã bán</span>',
        'reserved': '<span class="badge bg-warning">Đã đặt</span>',
        'inactive': '<span class="badge bg-secondary">Không hoạt động</span>'
    };
    return badges[status] || '<span class="badge bg-secondary">Không xác định</span>';
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN') + ' ' + date.toLocaleTimeString('vi-VN', {hour: '2-digit', minute: '2-digit'});
}

function showCreateLinkModal() {
    editingLinkId = null;
    document.getElementById('modalTitle').textContent = 'Thêm Video Link';
    document.getElementById('saveButtonText').textContent = 'Lưu';
    document.getElementById('videoLinkForm').reset();
    new coreui.Modal(document.getElementById('videoLinkModal')).show();
}

function editVideoLink(linkId) {
    const link = videoLinks.find(l => l.link_id === linkId);
    if (!link) return;

    editingLinkId = linkId;
    document.getElementById('modalTitle').textContent = 'Sửa Video Link';
    document.getElementById('saveButtonText').textContent = 'Cập nhật';

    // Fill form
    document.getElementById('linkId').value = link.link_id;
    document.getElementById('linkName').value = link.name;
    document.getElementById('videoCount').value = link.video_count;
    document.getElementById('driveUrl').value = link.drive_url;
    document.getElementById('videoType').value = link.video_type || '';
    document.getElementById('linkStatus').value = link.status;
    document.getElementById('linkDescription').value = link.description || '';

    new coreui.Modal(document.getElementById('videoLinkModal')).show();
}

function saveVideoLink() {
    const form = document.getElementById('videoLinkForm');
    const formData = new FormData(form);

    const url = editingLinkId ? 
        `/api/admin/marketplace/video-links/${editingLinkId}` : 
        '/api/admin/marketplace/video-links';
    
    const method = editingLinkId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(editingLinkId ? 'Cập nhật thành công!' : 'Thêm thành công!', 'success');
            coreui.Modal.getInstance(document.getElementById('videoLinkModal')).hide();
            loadVideoLinks();
        } else {
            showToast('Lỗi: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error saving video link:', error);
        showToast('Lỗi kết nối', 'danger');
    });
}

function deleteVideoLink(linkId) {
    deletingLinkId = linkId;
    new coreui.Modal(document.getElementById('deleteModal')).show();
}

function confirmDelete() {
    if (!deletingLinkId) return;

    fetch(`/api/admin/marketplace/video-links/${deletingLinkId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Xóa thành công!', 'success');
            coreui.Modal.getInstance(document.getElementById('deleteModal')).hide();
            loadVideoLinks();
        } else {
            showToast('Lỗi: ' + data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error deleting video link:', error);
        showToast('Lỗi kết nối', 'danger');
    });
}

function loadVideoTypes() {
    fetch('/api/admin/marketplace/video-types')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('videoTypeFilter');
                data.video_types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => console.error('Error loading video types:', error));
}

function showToast(message, type = 'info') {
    // Simple toast implementation
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        if (toast.parentElement) {
            toast.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
