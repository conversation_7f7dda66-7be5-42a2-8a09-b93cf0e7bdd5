#!/usr/bin/env python3
"""
Deploy Video System Migration
Tạo toàn bộ database schema và data cần thiết cho video system
"""

import psycopg2
import psycopg2.extras
import json
from datetime import datetime
from db_config import PG_CONFIG

def get_db_connection():
    """Kết nối database PostgreSQL từ config"""
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def create_video_tables(cursor):
    """Tạo các tables cho video system"""
    print("📋 Creating video system tables...")
    
    # 1. VideoLinks table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS "VideoLinks" (
            link_id SERIAL PRIMARY KEY,
            name VARCHAR(100) NOT NULL UNIQUE,
            drive_url TEXT NOT NULL,
            video_count INTEGER NOT NULL DEFAULT 0,
            video_type VARCHAR(100),
            description TEXT,
            status VARCHAR(20) DEFAULT 'available',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by INTEGER REFERENCES "Users"(user_id)
        )
    ''')
    print("   ✅ VideoLinks table created")
    
    # 2. ProductVideoLinks table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS "ProductVideoLinks" (
            mapping_id SERIAL PRIMARY KEY,
            product_id INTEGER NOT NULL REFERENCES "Products"(product_id) ON DELETE CASCADE,
            link_id INTEGER NOT NULL REFERENCES "VideoLinks"(link_id) ON DELETE CASCADE,
            assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(product_id, link_id)
        )
    ''')
    print("   ✅ ProductVideoLinks table created")
    
    # 3. UserVideoLinks table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS "UserVideoLinks" (
            user_link_id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES "Users"(user_id) ON DELETE CASCADE,
            order_id INTEGER NOT NULL REFERENCES "Orders"(order_id) ON DELETE CASCADE,
            product_id INTEGER NOT NULL REFERENCES "Products"(product_id) ON DELETE CASCADE,
            link_id INTEGER NOT NULL REFERENCES "VideoLinks"(link_id) ON DELETE CASCADE,
            purchased_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(user_id, order_id, link_id)
        )
    ''')
    print("   ✅ UserVideoLinks table created")

def create_indexes(cursor):
    """Tạo indexes để tối ưu performance"""
    print("📊 Creating indexes...")
    
    indexes = [
        # VideoLinks indexes
        'CREATE INDEX IF NOT EXISTS idx_videolinks_status ON "VideoLinks"(status)',
        'CREATE INDEX IF NOT EXISTS idx_videolinks_video_type ON "VideoLinks"(video_type)',
        'CREATE INDEX IF NOT EXISTS idx_videolinks_created_at ON "VideoLinks"(created_at)',
        'CREATE INDEX IF NOT EXISTS idx_videolinks_name ON "VideoLinks"(name)',
        
        # ProductVideoLinks indexes
        'CREATE INDEX IF NOT EXISTS idx_productvideolinks_product_id ON "ProductVideoLinks"(product_id)',
        'CREATE INDEX IF NOT EXISTS idx_productvideolinks_link_id ON "ProductVideoLinks"(link_id)',
        
        # UserVideoLinks indexes
        'CREATE INDEX IF NOT EXISTS idx_uservideolinks_user_id ON "UserVideoLinks"(user_id)',
        'CREATE INDEX IF NOT EXISTS idx_uservideolinks_order_id ON "UserVideoLinks"(order_id)',
        'CREATE INDEX IF NOT EXISTS idx_uservideolinks_link_id ON "UserVideoLinks"(link_id)',
        'CREATE INDEX IF NOT EXISTS idx_uservideolinks_purchased_at ON "UserVideoLinks"(purchased_at)'
    ]
    
    for index_sql in indexes:
        cursor.execute(index_sql)
    
    print("   ✅ All indexes created")

def create_video_product_type(cursor):
    """Tạo product type 'videos'"""
    print("🎬 Creating video product type...")

    # Check if product type exists
    cursor.execute('SELECT type_id FROM "ProductTypes" WHERE name = %s', ('videos',))
    existing = cursor.fetchone()

    if not existing:
        cursor.execute('''
            INSERT INTO "ProductTypes" (name, description, icon, metadata, created_at)
            VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
        ''', (
            'videos',
            'Sản phẩm video với Google Drive links',
            'cil-video',
            json.dumps({
                'requires_video_links': True,
                'display_name': 'Video'
            })
        ))
        print("   ✅ Video product type created")
    else:
        print("   ℹ️ Video product type already exists")

def insert_sample_video_links(cursor):
    """Tạo sample video links để test"""
    print("📝 Creating sample video links...")
    
    # Check if sample data exists
    cursor.execute('SELECT COUNT(*) FROM "VideoLinks"')
    count = cursor.fetchone()[0]
    
    if count == 0:
        sample_links = [
            {
                'name': 'Video Package 001',
                'drive_url': 'https://drive.google.com/drive/folders/sample001',
                'video_count': 50,
                'video_type': 'Nuôi kênh TikTok',
                'description': '50 video nuôi kênh TikTok chất lượng cao, phù hợp cho các kênh mới',
                'status': 'available'
            },
            {
                'name': 'Video Package 002',
                'drive_url': 'https://drive.google.com/drive/folders/sample002',
                'video_count': 100,
                'video_type': 'Viral Content',
                'description': '100 video viral content đa dạng chủ đề, tăng tương tác nhanh',
                'status': 'available'
            },
            {
                'name': 'Video Package 003',
                'drive_url': 'https://drive.google.com/drive/folders/sample003',
                'video_count': 30,
                'video_type': 'Dance Videos',
                'description': '30 video nhảy trending, phù hợp cho kênh giải trí',
                'status': 'available'
            }
        ]
        
        for link in sample_links:
            cursor.execute('''
                INSERT INTO "VideoLinks" (name, drive_url, video_count, video_type, description, status, created_at)
                VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
            ''', (
                link['name'],
                link['drive_url'],
                link['video_count'],
                link['video_type'],
                link['description'],
                link['status']
            ))
        
        print(f"   ✅ Created {len(sample_links)} sample video links")
    else:
        print(f"   ℹ️ Video links already exist ({count} links)")

def update_existing_data(cursor):
    """Cập nhật dữ liệu hiện có nếu cần"""
    print("🔄 Updating existing data...")

    # Get video product type ID
    cursor.execute('SELECT type_id FROM "ProductTypes" WHERE name = %s', ('videos',))
    video_type_result = cursor.fetchone()

    print(f"   Debug: video_type_result = {video_type_result}")

    if video_type_result:
        video_type_id = video_type_result[0]
        print(f"   Debug: video_type_id = {video_type_id}")

        # Update Products table to ensure video products work correctly
        cursor.execute('''
            UPDATE "Products"
            SET product_type = %s, product_type_id = %s
            WHERE product_type = 'video' OR name ILIKE %s
        ''', ('videos', video_type_id, '%video%'))

        updated_count = cursor.rowcount
        if updated_count > 0:
            print(f"   ✅ Updated {updated_count} products to use 'videos' type")
        else:
            print("   ℹ️ No products needed updating")
    else:
        print("   ⚠️ Video product type not found, skipping product updates")

def verify_deployment(cursor):
    """Verify deployment thành công"""
    print("🔍 Verifying deployment...")
    
    # Check tables exist
    tables = ['VideoLinks', 'ProductVideoLinks', 'UserVideoLinks']
    for table in tables:
        cursor.execute('''
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = %s
            )
        ''', (table,))
        exists = cursor.fetchone()[0]
        if exists:
            print(f"   ✅ Table {table} exists")
        else:
            print(f"   ❌ Table {table} missing")
            return False
    
    # Check product type
    cursor.execute('SELECT COUNT(*) FROM "ProductTypes" WHERE name = %s', ('videos',))
    video_type_count = cursor.fetchone()[0]
    if video_type_count > 0:
        print("   ✅ Video product type exists")
    else:
        print("   ❌ Video product type missing")
        return False
    
    # Check sample data
    cursor.execute('SELECT COUNT(*) FROM "VideoLinks"')
    link_count = cursor.fetchone()[0]
    print(f"   ✅ VideoLinks count: {link_count}")
    
    # Check indexes
    cursor.execute('''
        SELECT COUNT(*) FROM pg_indexes 
        WHERE tablename IN ('VideoLinks', 'ProductVideoLinks', 'UserVideoLinks')
    ''')
    index_count = cursor.fetchone()[0]
    print(f"   ✅ Indexes count: {index_count}")
    
    return True

def main():
    """Main deployment function"""
    print("🚀 Deploying Video System to Production")
    print("=" * 60)
    
    # Connect to database
    conn = get_db_connection()
    if not conn:
        print("❌ Cannot connect to database. Check db_config.py")
        return False
    
    try:
        cursor = conn.cursor()
        
        print(f"✅ Connected to database: {PG_CONFIG['database']} on {PG_CONFIG['host']}")
        
        # Step 1: Create tables
        create_video_tables(cursor)
        
        # Step 2: Create indexes
        create_indexes(cursor)
        
        # Step 3: Create product type
        create_video_product_type(cursor)
        
        # Step 4: Insert sample data
        insert_sample_video_links(cursor)
        
        # Step 5: Update existing data
        update_existing_data(cursor)
        
        # Commit all changes
        conn.commit()
        print("💾 All changes committed to database")
        
        # Step 6: Verify deployment
        if verify_deployment(cursor):
            print("\n🎉 Video System Deployment Successful!")
            print("\n📋 What was deployed:")
            print("  ✅ VideoLinks table with indexes")
            print("  ✅ ProductVideoLinks table with indexes") 
            print("  ✅ UserVideoLinks table with indexes")
            print("  ✅ Video product type")
            print("  ✅ Sample video links")
            print("  ✅ Database indexes for performance")
            
            print("\n🎯 Next steps:")
            print("  1. Deploy updated mip_system.py with video APIs")
            print("  2. Deploy video_links.html template")
            print("  3. Deploy updated user_orders.html template")
            print("  4. Deploy updated order_detail.html template")
            print("  5. Test admin video links management")
            print("  6. Test video product creation")
            print("  7. Test video purchase flow")
            
            return True
        else:
            print("\n❌ Deployment verification failed!")
            return False
            
    except Exception as e:
        print(f"❌ Deployment error: {e}")
        import traceback
        traceback.print_exc()
        conn.rollback()
        return False
        
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
