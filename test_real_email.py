#!/usr/bin/env python3
"""
Test sending email to real email address
"""

from email_verification_service import send_otp_email

def test_real_email():
    """Test sending OTP to real email"""
    print("📧 Testing Real Email Sending")
    print("=" * 50)
    
    # Prompt for email
    real_email = input("Nhập email thật của bạn để test: ").strip()
    
    if not real_email:
        print("❌ Email không được để trống")
        return
    
    if '@' not in real_email:
        print("❌ Email không hợp lệ")
        return
    
    print(f"📤 Đang gửi OTP test đến: {real_email}")
    
    # Send OTP
    result = send_otp_email(real_email, 'registration')
    
    if result['success']:
        print("✅ Email đã gửi thành công!")
        print(f"   Code ID: {result['code_id']}")
        print(f"   Expires: {result['expires_at']}")
        print(f"   Max attempts: {result['max_attempts']}")
        print(f"\n📧 Kiểm tra email của bạn (bao gồm spam folder)")
        print(f"   Sender: SAP MMO <<EMAIL>>")
        print(f"   Subject: Xá<PERSON> thực tài khoản MIP System")
    else:
        print(f"❌ Gửi email thất bại: {result['error']}")

if __name__ == "__main__":
    test_real_email()
