#!/usr/bin/env python3
"""
Add detailed debug logging to registration function
"""

def add_debug_to_mip_system():
    """Add debug logging to registration function in mip_system.py"""
    try:
        # Read current mip_system.py
        with open('mip_system.py', 'r') as f:
            content = f.read()
        
        print("🔧 Adding debug logging to registration function...")
        
        # Find registration function and add debug
        debug_code = '''
            # DEBUG: Log registration attempt
            print(f"🔍 REGISTRATION DEBUG: Starting registration for {email}")
            print(f"🔍 REGISTRATION DEBUG: EMAIL_SERVICE_AVAILABLE = {EMAIL_SERVICE_AVAILABLE}")
            
            # Store registration data in session and send OTP
            session['pending_registration'] = {
                'username': username,
                'email': email,
                'phone': formatted_phone,
                'password': generate_password_hash(password)
            }
            print(f"🔍 REGISTRATION DEBUG: Stored pending registration: {session['pending_registration']}")
            
            # Send OTP email
            if not EMAIL_SERVICE_AVAILABLE:
                print(f"❌ REGISTRATION DEBUG: EMAIL_SERVICE_AVAILABLE is False")
                return render_template('register.html', error='Email service không khả dụng')
            
            try:
                print(f"🔍 REGISTRATION DEBUG: About to call send_otp_email")
                print(f"🔍 REGISTRATION DEBUG: Parameters - email={email}, type=registration")
                
                result = send_otp_email(email, 'registration', ip_address=request.remote_addr, user_agent=request.headers.get('User-Agent'))
                
                print(f"🔍 REGISTRATION DEBUG: send_otp_email result: {result}")
                
                if result is None:
                    print(f"❌ REGISTRATION DEBUG: send_otp_email returned None")
                    return render_template('register.html', error='Lỗi gửi email: Function returned None')
                
                if not isinstance(result, dict):
                    print(f"❌ REGISTRATION DEBUG: send_otp_email returned non-dict: {type(result)}")
                    return render_template('register.html', error=f'Lỗi gửi email: Invalid return type {type(result)}')
                
                if 'success' not in result:
                    print(f"❌ REGISTRATION DEBUG: send_otp_email result missing 'success' key: {result}")
                    return render_template('register.html', error='Lỗi gửi email: Missing success key')
                
            except Exception as e:
                print(f"❌ REGISTRATION DEBUG: Exception in send_otp_email: {e}")
                import traceback
                traceback.print_exc()
                return render_template('register.html', error=f'Lỗi gửi email: {str(e)}')
            
            if result['success']:
                print(f"✅ REGISTRATION DEBUG: OTP sent successfully, redirecting to verify page")
                # Redirect to email verification page
                return redirect(url_for('verify_email', email=email, type='registration'))
            else:
                error_msg = result.get('error', 'Unknown error')
                print(f"❌ REGISTRATION DEBUG: OTP failed with error: {error_msg}")
                return render_template('register.html', error=f'Không thể gửi email xác thực: {error_msg}')
'''
        
        # Find the location to insert debug code
        # Look for the session storage part
        session_pattern = "session['pending_registration'] = {"
        
        if session_pattern in content:
            # Find the start and end of the registration OTP section
            start_idx = content.find(session_pattern)
            
            # Find the end of this section (look for the next major block)
            end_patterns = [
                "except Exception as e:",
                "finally:",
                "cursor.close()",
                "conn.close()"
            ]
            
            end_idx = -1
            for pattern in end_patterns:
                idx = content.find(pattern, start_idx)
                if idx > start_idx:
                    end_idx = idx
                    break
            
            if end_idx > start_idx:
                # Replace the section with debug version
                before = content[:start_idx]
                after = content[end_idx:]
                
                new_content = before + debug_code + after
                
                # Write back to file
                with open('mip_system.py', 'w') as f:
                    f.write(new_content)
                
                print("✅ Debug logging added to registration function")
                return True
            else:
                print("❌ Could not find end of registration section")
                return False
        else:
            print("❌ Could not find registration session storage section")
            return False
            
    except Exception as e:
        print(f"❌ Error adding debug to mip_system.py: {e}")
        import traceback
        traceback.print_exc()
        return False

def backup_mip_system():
    """Create backup of mip_system.py"""
    try:
        import shutil
        import datetime
        
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"mip_system_backup_{timestamp}.py"
        
        shutil.copy2('mip_system.py', backup_name)
        print(f"✅ Created backup: {backup_name}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating backup: {e}")
        return False

def check_current_registration_function():
    """Check current registration function"""
    try:
        with open('mip_system.py', 'r') as f:
            content = f.read()
        
        print("🔍 Checking current registration function...")
        
        # Check for key components
        checks = [
            ('email_verification_service import', 'from email_verification_service import'),
            ('EMAIL_SERVICE_AVAILABLE', 'EMAIL_SERVICE_AVAILABLE'),
            ('send_otp_email call', 'send_otp_email('),
            ('pending_registration session', "session['pending_registration']"),
            ('verify_email redirect', "url_for('verify_email'")
        ]
        
        results = {}
        for name, pattern in checks:
            found = pattern in content
            results[name] = found
            print(f"   {name}: {'✅' if found else '❌'}")
        
        return all(results.values())
        
    except Exception as e:
        print(f"❌ Error checking registration function: {e}")
        return False

def main():
    """Main function"""
    print("🔧 Registration Debug Patcher")
    print("=" * 50)
    
    # Check current function
    current_ok = check_current_registration_function()
    
    if not current_ok:
        print("\n❌ Registration function is missing key components!")
        print("Please ensure mip_system.py has been updated with email verification code.")
        return
    
    # Create backup
    backup_ok = backup_mip_system()
    
    if not backup_ok:
        print("❌ Could not create backup - aborting")
        return
    
    # Add debug logging
    debug_ok = add_debug_to_mip_system()
    
    if debug_ok:
        print("\n🎉 Debug logging added successfully!")
        print("\n📋 Next steps:")
        print("1. Restart web server")
        print("2. Try registration again")
        print("3. Check web server logs for detailed debug output")
        print("4. Run: python3 debug_registration_live.py")
    else:
        print("\n❌ Failed to add debug logging")

if __name__ == "__main__":
    main()
