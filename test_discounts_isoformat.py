#!/usr/bin/env python3
"""
Test discounts isoformat issue
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def test_discounts_isoformat():
    """Test discounts data for None datetime fields"""
    
    print("🔍 Testing discounts isoformat issue...")
    print("=" * 60)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Check raw data in Discounts table
        print("\n🔍 Checking raw Discounts data...")
        cursor.execute('''
            SELECT discount_id, code, description, discount_type, value,
                   min_order_value, usage_limit, used_count, is_active,
                   start_date, end_date, created_at
            FROM "Discounts"
            ORDER BY created_at DESC
            LIMIT 5;
        ''')
        
        raw_data = cursor.fetchall()
        print(f"📋 Found {len(raw_data)} discounts:")
        
        for i, row in enumerate(raw_data):
            print(f"\n  Discount {i+1}:")
            print(f"    - ID: {row[0]}")
            print(f"    - Code: {row[1]}")
            print(f"    - Type: {row[3]}")
            print(f"    - Value: {row[4]}")
            print(f"    - Start Date: {row[9]} (Type: {type(row[9])})")
            print(f"    - End Date: {row[10]} (Type: {type(row[10])})")
            print(f"    - Created At: {row[11]} (Type: {type(row[11])})")
            
            # Test isoformat on each datetime field
            for field_name, field_value in [('start_date', row[9]), ('end_date', row[10]), ('created_at', row[11])]:
                try:
                    if field_value is None:
                        print(f"    - {field_name}: None (will use None)")
                    else:
                        iso_result = field_value.isoformat()
                        print(f"    - {field_name}: {iso_result} ✅")
                except Exception as e:
                    print(f"    - {field_name}: ERROR - {e} ❌")
        
        # Test the exact query from the API
        print("\n🔍 Testing exact API query...")
        try:
            cursor.execute('''
                SELECT d.discount_id, d.code, d.description, d.discount_type, d.value,
                       d.min_order_value, d.usage_limit, d.used_count, d.is_active,
                       d.start_date, d.end_date, d.created_at,
                       COALESCE(SUM(o.discount_amount), 0) as total_savings
                FROM "Discounts" d
                LEFT JOIN "Orders" o ON d.code = o.discount_code AND o.status = 'completed'
                WHERE 1=1
                GROUP BY d.discount_id
                ORDER BY d.created_at DESC
                LIMIT 3;
            ''')
            
            api_data = cursor.fetchall()
            print(f"✅ API query successful - {len(api_data)} results")
            
            # Test formatting each result
            for i, row in enumerate(api_data):
                print(f"\n  Processing row {i+1}:")
                try:
                    formatted = {
                        'discount_id': row[0],
                        'code': row[1],
                        'description': row[2],
                        'discount_type': row[3],
                        'value': float(row[4]),
                        'min_order_value': float(row[5]) if row[5] else 0,
                        'usage_limit': row[6],
                        'used_count': row[7],
                        'is_active': row[8],
                        'start_date': row[9].isoformat() if row[9] else None,
                        'end_date': row[10].isoformat() if row[10] else None,
                        'created_at': row[11].isoformat() if row[11] else None,
                        'total_savings': float(row[12])
                    }
                    print(f"    ✅ Row {i+1} formatted successfully")
                    print(f"       Code: {formatted['code']}")
                    print(f"       Start: {formatted['start_date']}")
                    print(f"       End: {formatted['end_date']}")
                    print(f"       Created: {formatted['created_at']}")
                    
                except Exception as e:
                    print(f"    ❌ Row {i+1} formatting failed: {e}")
                    print(f"       Raw data: {row}")
                    
        except Exception as e:
            print(f"❌ API query failed: {e}")
        
        # Check for any NULL datetime fields
        print("\n🔍 Checking for NULL datetime fields...")
        cursor.execute('''
            SELECT 
                COUNT(*) as total,
                COUNT(CASE WHEN start_date IS NULL THEN 1 END) as null_start_date,
                COUNT(CASE WHEN end_date IS NULL THEN 1 END) as null_end_date,
                COUNT(CASE WHEN created_at IS NULL THEN 1 END) as null_created_at
            FROM "Discounts";
        ''')
        
        null_stats = cursor.fetchone()
        print(f"📊 NULL datetime statistics:")
        print(f"    - Total discounts: {null_stats[0]}")
        print(f"    - NULL start_date: {null_stats[1]}")
        print(f"    - NULL end_date: {null_stats[2]}")
        print(f"    - NULL created_at: {null_stats[3]}")
        
        if null_stats[1] > 0 or null_stats[2] > 0 or null_stats[3] > 0:
            print("⚠️  Found NULL datetime fields - this could cause isoformat errors")
            
            # Show records with NULL dates
            cursor.execute('''
                SELECT discount_id, code, start_date, end_date, created_at
                FROM "Discounts"
                WHERE start_date IS NULL OR end_date IS NULL OR created_at IS NULL;
            ''')
            
            null_records = cursor.fetchall()
            print(f"\n📋 Records with NULL dates:")
            for record in null_records:
                print(f"    - ID {record[0]} ({record[1]}): start={record[2]}, end={record[3]}, created={record[4]}")
        else:
            print("✅ No NULL datetime fields found")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Discounts isoformat test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_discounts_isoformat()
    sys.exit(0 if success else 1)
