#!/usr/bin/env python3
"""
Test script để kiểm tra tính năng import accounts với các trường Email, Full Email, Full Info
"""

import pandas as pd
import os

def create_test_excel_file():
    """Tạo file Excel test với các trường mới"""
    
    # Dữ liệu test
    test_data = [
        {
            'username': 'test_account_1',
            'team': 'Team Test',
            'trạng thái': 'Đang nuôi',
            'email': '<EMAIL>',
            'full_email': '<EMAIL>|password123|refresh_token_1|client_id_1',
            'full_info': 'Account test 1 - Thông tin chi tiết về tài khoản này'
        },
        {
            'username': 'test_account_2',
            'team': 'Team Test',
            'trạng thái': 'Đủ điều kiện',
            'email': '<EMAIL>',
            'full email': '<EMAIL>|password456|refresh_token_2|client_id_2',  # Test với tên cột có dấu cách
            'full info': 'Account test 2 - Thông tin bổ sung khác'  # Test với tên cột có dấu cách
        },
        {
            'username': 'test_account_3',
            'team': 'Team Test',
            'trạng thái': 'Có giỏ',
            'email': '',  # Test với email trống
            'full_email': '',  # Test với full_email trống
            'full_info': 'Chỉ có thông tin cơ bản'
        },
        {
            'username': 'test_account_4',
            'team': 'Team Test',
            'trạng thái': 'Bật hụt'
            # Test không có các trường email (sẽ là None/NaN)
        }
    ]
    
    # Tạo DataFrame
    df = pd.DataFrame(test_data)
    
    # Lưu file Excel
    output_file = 'test_import_accounts_with_email.xlsx'
    df.to_excel(output_file, index=False)
    
    print(f"✅ Đã tạo file test: {output_file}")
    print(f"📋 Cấu trúc file:")
    print(f"   - Số dòng: {len(df)}")
    print(f"   - Các cột: {list(df.columns)}")
    
    # Hiển thị preview
    print(f"\n📄 Preview dữ liệu:")
    for i, row in df.iterrows():
        email = row.get('email', 'N/A')
        full_email = row.get('full_email', 'N/A')
        if pd.isna(email):
            email = 'N/A'
        if pd.isna(full_email):
            full_email = 'N/A'
        else:
            full_email = str(full_email)[:30] + "..." if len(str(full_email)) > 30 else str(full_email)
        print(f"   Dòng {i+1}: {row['username']} - {email} - {full_email}")
    
    return output_file

def create_test_excel_file_minimal():
    """Tạo file Excel test tối thiểu chỉ với username"""
    
    test_data = [
        {'username': 'minimal_test_1'},
        {'username': 'minimal_test_2'},
        {'username': 'minimal_test_3'}
    ]
    
    df = pd.DataFrame(test_data)
    output_file = 'test_import_minimal.xlsx'
    df.to_excel(output_file, index=False)
    
    print(f"✅ Đã tạo file test tối thiểu: {output_file}")
    return output_file

def create_test_excel_file_update():
    """Tạo file Excel test để cập nhật tài khoản đã tồn tại"""
    
    test_data = [
        {
            'username': 'existing_account_1',
            'team': 'Team Updated',
            'trạng thái': 'Có giỏ',
            'email': '<EMAIL>',
            'full_email': '<EMAIL>|newpass123|new_refresh_token|new_client_id',
            'full_info': 'Thông tin đã được cập nhật cho account 1'
        },
        {
            'username': 'existing_account_2',
            'team': 'Team Updated',
            'trạng thái': 'Thu giỏ',
            'email': '<EMAIL>',
            'full_email': '<EMAIL>|newpass456|new_refresh_token_2|new_client_id_2',
            'full_info': 'Thông tin đã được cập nhật cho account 2'
        }
    ]
    
    df = pd.DataFrame(test_data)
    output_file = 'test_import_update_accounts.xlsx'
    df.to_excel(output_file, index=False)
    
    print(f"✅ Đã tạo file test cập nhật: {output_file}")
    return output_file

if __name__ == "__main__":
    print("🧪 Tạo các file Excel test cho tính năng import accounts")
    print("=" * 60)
    
    # Tạo các file test
    file1 = create_test_excel_file()
    print()
    file2 = create_test_excel_file_minimal()
    print()
    file3 = create_test_excel_file_update()
    
    print("\n" + "=" * 60)
    print("📝 Hướng dẫn test:")
    print("1. Upload file test_import_accounts_with_email.xlsx để test import với đầy đủ thông tin")
    print("2. Upload file test_import_minimal.xlsx để test import chỉ với username")
    print("3. Upload file test_import_update_accounts.xlsx để test cập nhật tài khoản đã tồn tại")
    print("\n🔍 Kiểm tra:")
    print("- Các trường email, full_email, full_info có được import đúng không")
    print("- Tên cột có dấu cách ('full email', 'full info') có hoạt động không")
    print("- Trường hợp để trống có xử lý đúng không")
    print("- Cập nhật tài khoản đã tồn tại có hoạt động không")
