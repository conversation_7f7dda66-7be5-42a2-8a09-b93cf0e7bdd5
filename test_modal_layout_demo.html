<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Modal Layout</title>
    <link href="https://cdn.jsdelivr.net/npm/@coreui/coreui@5.0.0/dist/css/coreui.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@coreui/icons@3.0.1/css/all.min.css" rel="stylesheet">
    <style>
        .description-content {
            line-height: 1.6;
            color: #495057;
            font-size: 0.9rem;
        }
        .description-content h4,
        .description-content h5,
        .description-content h6 {
            margin-top: 1rem;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }
        .description-content h4 {
            font-size: 1.1rem;
        }
        .description-content h5 {
            font-size: 1rem;
        }
        .description-content h6 {
            font-size: 0.95rem;
        }
        .description-content ul,
        .description-content ol {
            padding-left: 1.2rem;
            margin-bottom: 0.8rem;
        }
        .description-content li {
            margin-bottom: 0.3rem;
        }
        .description-content strong {
            color: #212529;
            font-weight: 600;
        }
        .description-content em {
            font-style: italic;
            color: #6c757d;
        }
        .description-content a {
            color: #00C6AE;
            text-decoration: none;
            font-size: 0.85rem;
        }
        .description-content a:hover {
            color: #00a693;
            text-decoration: underline;
        }
        .description-content p {
            margin-bottom: 0.8rem;
        }
        
        /* Scrollbar styling for description */
        .description-content::-webkit-scrollbar {
            width: 6px;
        }
        .description-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }
        .description-content::-webkit-scrollbar-thumb {
            background: #00C6AE;
            border-radius: 3px;
        }
        .description-content::-webkit-scrollbar-thumb:hover {
            background: #00a693;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="cil-star me-2"></i>Demo: Modal Layout Mới</h4>
                    </div>
                    <div class="card-body">
                        
                        <!-- Modal Content Demo -->
                        <div class="modal-content" style="position: static; display: block;">
                            <div class="modal-header">
                                <h5 class="modal-title">Gói Account TikTok Premium</h5>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <img src="https://via.placeholder.com/400x300/00C6AE/white?text=TikTok+Account" 
                                             class="img-fluid rounded shadow-sm" alt="TikTok Account">
                                    </div>
                                    <div class="col-md-6">
                                        <h5 class="mb-3">Gói Account TikTok Premium</h5>
                                        <p class="text-muted mb-3"><i class="cil-info me-2"></i>Gói tài khoản TikTok chất lượng cao với follower thật</p>
                                        <div class="mb-3">
                                            <span class="badge bg-info me-2">Account Package</span>
                                            <span class="badge bg-warning">Nổi bật</span>
                                        </div>
                                        <h4 class="text-primary mb-3">500,000 MP</h4>
                                        <div class="mb-3">
                                            <strong><i class="cil-layers me-2"></i>Tình trạng:</strong> 
                                            <span class="text-success">15 còn lại</span>
                                        </div>
                                        
                                        <!-- Mô tả chi tiết ngay dưới Tình trạng -->
                                        <div class="mt-4">
                                            <h6 class="text-primary mb-2"><i class="cil-description me-2"></i>Mô tả chi tiết</h6>
                                            <div class="description-content" style="max-height: 300px; overflow-y: auto; padding-right: 10px;" id="description-demo">
                                                <!-- Will be populated by JavaScript -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary">Đóng</button>
                                <button type="button" class="btn btn-primary">Thêm vào giỏ</button>
                            </div>
                        </div>

                        <!-- Layout Comparison -->
                        <div class="mt-5">
                            <div class="alert alert-success">
                                <h6><i class="cil-check me-2"></i>Layout mới:</h6>
                                <ul class="mb-0">
                                    <li>✅ Hình ảnh bên trái (col-md-6)</li>
                                    <li>✅ Thông tin cơ bản bên phải (col-md-6)</li>
                                    <li>✅ Mô tả chi tiết ngay dưới "Tình trạng"</li>
                                    <li>✅ Scroll riêng cho phần mô tả (max-height: 300px)</li>
                                    <li>✅ Không chiếm diện tích thừa</li>
                                    <li>✅ Layout cân đối và gọn gàng</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@coreui/coreui@5.0.0/dist/js/coreui.bundle.min.js"></script>
    <script>
        function formatDescription(description) {
            if (!description) return '';
            
            // Convert line breaks to HTML
            let formatted = description.replace(/\n/g, '<br>');
            
            // Format bullet points
            formatted = formatted.replace(/^[-*•]\s+(.+)$/gm, '<li class="mb-1">$1</li>');
            
            // Wrap consecutive list items in ul tags
            formatted = formatted.replace(/(<li[^>]*>.*?<\/li>(?:\s*<br>\s*<li[^>]*>.*?<\/li>)*)/gs, '<ul class="mb-3">$1</ul>');
            
            // Format numbered lists
            formatted = formatted.replace(/^(\d+)\.\s+(.+)$/gm, '<li class="mb-1">$2</li>');
            formatted = formatted.replace(/(<li[^>]*>.*?<\/li>(?:\s*<br>\s*<li[^>]*>.*?<\/li>)*)/gs, '<ol class="mb-3">$1</ol>');
            
            // Format bold text **text** or __text__
            formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
            formatted = formatted.replace(/__(.*?)__/g, '<strong>$1</strong>');
            
            // Format italic text *text* or _text_
            formatted = formatted.replace(/\*(.*?)\*/g, '<em>$1</em>');
            formatted = formatted.replace(/_(.*?)_/g, '<em>$1</em>');
            
            // Format headers ### Header
            formatted = formatted.replace(/^###\s+(.+)$/gm, '<h6 class="text-primary mt-3 mb-2">$1</h6>');
            formatted = formatted.replace(/^##\s+(.+)$/gm, '<h5 class="text-primary mt-3 mb-2">$1</h5>');
            formatted = formatted.replace(/^#\s+(.+)$/gm, '<h4 class="text-primary mt-3 mb-2">$1</h4>');
            
            // Format links [text](url)
            formatted = formatted.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" class="text-decoration-none">$1 <i class="cil-external-link"></i></a>');
            
            // Clean up extra br tags
            formatted = formatted.replace(/<br>\s*<br>/g, '<br>');
            
            return formatted;
        }

        // Sample description
        const description = `## Gói Account TikTok Premium

**Đặc điểm nổi bật:**
- Follower từ 10K - 50K thật 100%
- Tương tác cao, engagement rate > 3%
- Account đã verify, có tick xanh
- Bảo hành 30 ngày

### Thông tin chi tiết:
- **Độ tuổi account:** 6-12 tháng
- **Niche:** Lifestyle, Entertainment, Dance
- **Quốc gia:** Việt Nam, Thái Lan, Philippines

### Quy trình giao hàng:
1. Thanh toán đơn hàng
2. Nhận thông tin account trong 2-4 giờ
3. Kiểm tra và xác nhận
4. Hỗ trợ đổi account nếu có vấn đề

**Lưu ý quan trọng:**
- Không thay đổi thông tin cá nhân trong 7 ngày đầu
- Sử dụng proxy để tránh bị khóa
- Liên hệ support nếu có vấn đề

[Xem hướng dẫn sử dụng](https://example.com/guide)`;

        // Populate description
        document.getElementById('description-demo').innerHTML = formatDescription(description);
    </script>
</body>
</html>
