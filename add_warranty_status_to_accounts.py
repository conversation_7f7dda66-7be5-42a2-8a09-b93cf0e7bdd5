#!/usr/bin/env python3
"""
Add warranty_status column to Accounts table
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def add_warranty_status_to_accounts():
    """Add warranty_status column to Accounts table"""
    
    print("🚀 Adding warranty_status column to Accounts table...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check if warranty_status column exists
        print("\n🔧 Step 1: Checking if warranty_status column exists...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND column_name = 'warranty_status' AND table_schema = 'public';
        ''')
        
        existing_column = cursor.fetchone()
        
        if existing_column:
            print(f"✅ warranty_status column already exists: {existing_column[1]} ({existing_column[2]})")
            return True
        
        # Step 2: Add warranty_status column
        print("\n🔧 Step 2: Adding warranty_status column...")
        
        try:
            # Add column with default value (matching local structure)
            cursor.execute('''
                ALTER TABLE "Accounts"
                ADD COLUMN warranty_status character varying DEFAULT 'Normal';
            ''')

            print("  ✅ Added warranty_status column (character varying)")

            # Add comment
            cursor.execute('''
                COMMENT ON COLUMN "Accounts"."warranty_status" IS 'Trạng thái bảo hành: Normal, Warranty_Requested, Recalled, Warranty_Completed';
            ''')

            print("  ✅ Added column comment")
            
        except Exception as e:
            print(f"  ❌ Failed to add warranty_status column: {e}")
            return False
        
        # Step 3: Set default values for existing records
        print("\n🔧 Step 3: Setting default values for existing records...")
        
        try:
            # Update existing records to have 'Normal' warranty status (matching code default)
            cursor.execute('''
                UPDATE "Accounts"
                SET warranty_status = 'Normal'
                WHERE warranty_status IS NULL;
            ''')

            updated_count = cursor.rowcount
            print(f"  ✅ Updated {updated_count} existing records to 'Normal' status")
            
        except Exception as e:
            print(f"  ⚠️  Default value update: {e}")
        
        # Step 4: Create index for performance
        print("\n🔧 Step 4: Creating index...")
        
        try:
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_accounts_warranty_status 
                ON "Accounts"(warranty_status) 
                WHERE warranty_status IS NOT NULL;
            ''')
            
            print("  ✅ Created index: idx_accounts_warranty_status")
            
        except Exception as e:
            print(f"  ⚠️  Index creation: {e}")
        
        # Step 5: Add check constraint for valid values
        print("\n🔧 Step 5: Adding check constraint...")
        
        try:
            cursor.execute('''
                ALTER TABLE "Accounts" ADD CONSTRAINT "chk_warranty_status"
                CHECK (warranty_status IN ('Normal', 'Warranty_Requested', 'Recalled', 'Warranty_Completed'));
            ''')

            print("  ✅ Added check constraint for warranty_status values")
            
        except Exception as e:
            print(f"  ⚠️  Check constraint: {e}")
        
        # Step 6: Verify the addition
        print("\n🔧 Step 6: Verifying warranty_status column...")
        
        cursor.execute('''
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'Accounts' AND column_name = 'warranty_status' AND table_schema = 'public';
        ''')
        
        column_info = cursor.fetchone()
        if column_info:
            nullable = "NULL" if column_info[2] == 'YES' else "NOT NULL"
            default = f" DEFAULT {column_info[3]}" if column_info[3] else ""
            print(f"📋 warranty_status: {column_info[1]} {nullable}{default}")
        else:
            print("❌ warranty_status column not found after addition")
            return False
        
        # Step 7: Check warranty_status values distribution
        print("\n🔧 Step 7: Checking warranty_status values...")
        
        try:
            cursor.execute('''
                SELECT warranty_status, COUNT(*) 
                FROM "Accounts" 
                GROUP BY warranty_status 
                ORDER BY warranty_status;
            ''')
            
            status_counts = cursor.fetchall()
            print("📊 Warranty status distribution:")
            for status, count in status_counts:
                print(f"  - {status}: {count} accounts")
                
        except Exception as e:
            print(f"  ⚠️  Status check: {e}")
        
        # Step 8: Test the problematic query
        print("\n🔧 Step 8: Testing the problematic query...")
        
        try:
            cursor.execute('''
                SELECT a.account_id, a.account_name, a.warranty_status
                FROM "Accounts" a
                WHERE a.is_deleted = 0
                LIMIT 3;
            ''')
            
            test_results = cursor.fetchall()
            print("✅ Query test successful:")
            for result in test_results:
                print(f"  - Account: {result[1]} (ID: {result[0]}) - Warranty: {result[2]}")
                
        except Exception as e:
            print(f"❌ Query test failed: {e}")
            return False
        
        # Commit all changes
        conn.commit()
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 warranty_status column added successfully!")
        print("📝 Next steps:")
        print("   1. Restart application: sudo systemctl restart sapmmo")
        print("   2. Test warranty request functionality")
        print("   3. Test warranty status management")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = add_warranty_status_to_accounts()
    sys.exit(0 if success else 1)
