# Email Verification System Deployment Guide

## 📋 Overview
This deployment adds email verification functionality to the registration and password reset flows.

## 🗄️ Database Changes

### New Table: EmailVerificationCodes
```sql
CREATE TABLE "EmailVerificationCodes" (
    code_id SERIAL PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    code <PERSON><PERSON><PERSON><PERSON>(6) NOT NULL,
    code_type VA<PERSON>HAR(20) NOT NULL, -- 'registration' or 'password_reset'
    user_id INTEGER REFERENCES "Users"(user_id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    attempts INTEGER DEFAULT 0,
    max_attempts INTEGER DEFAULT 3,
    is_used BOOLEAN DEFAULT FALSE,
    ip_address INET,
    user_agent TEXT
);
```

### Updated Users Table
- Added: `email_verified BOOLEAN DEFAULT FALSE`
- Added: `email_verified_at TIMESTAMP NULL`
- Modified: `email` column set to NOT NULL

### Updated SystemConfig Table
- Renamed: `config_id` → `id` (to match local structure)

### New SystemConfig Entries
- `mailtrap_api_token`: Mailtrap API Token
- `mailtrap_sender_email`: Sender email address
- `mailtrap_sender_name`: Sender name (SAPMMO)
- `email_verification_enabled`: Enable/disable email verification
- `otp_expiry_minutes`: OTP expiry time (default: 15)
- `max_otp_attempts`: Maximum OTP attempts (default: 3)

## 📁 Files to Upload

### Python Files
1. **email_verification_service.py** - Email service functions
2. **mip_system.py** - Updated with email verification routes
3. **deploy_email_verification_system.py** - Migration script

### HTML Templates
1. **templates/verify_email.html** - OTP verification page
2. **templates/forgot_password.html** - Forgot password form
3. **templates/reset_password.html** - Reset password form
4. **templates/login.html** - Updated with forgot password link
5. **templates/register.html** - Updated with required email field

## 🚀 Deployment Steps

### Step 1: Run Migration
```bash
python3 deploy_email_verification_system.py
```

### Step 2: Install Dependencies
```bash
pip install mailtrap
```

### Step 3: Upload Files
Upload all Python and HTML files to server

### Step 4: Configure Email Settings
1. Go to `/config` page
2. Update email configurations:
   - Set Mailtrap API token
   - Set sender email domain
   - Configure sender name

### Step 5: Test Functionality
1. Test email config in `/config`
2. Test registration with email verification
3. Test forgot password flow

## 🔧 Configuration

### Required Mailtrap Settings
- **API Token**: Get from Mailtrap dashboard
- **Sender Email**: Use verified domain
- **Sender Name**: SAPMMO

### Email Templates
- **Registration**: "Xác thực tài khoản SAPMMO"
- **Password Reset**: "Đặt lại mật khẩu SAPMMO"
- **Test Email**: "Test Email - SAPMMO"

## 🎯 Features Added

### Registration Flow
1. User fills registration form (email required)
2. System sends OTP to email
3. User enters 6-digit OTP
4. Account created after verification

### Forgot Password Flow
1. User clicks "Quên mật khẩu?" on login
2. User enters email address
3. System sends OTP if email exists
4. User enters OTP and sets new password

### Admin Features
- Email configuration in `/config`
- Test email functionality
- Separate save button for email configs

## 🔍 API Endpoints Added

- `GET /verify-email` - Email verification page
- `POST /api/verify-otp` - Verify OTP code
- `POST /api/resend-otp` - Resend OTP code
- `GET /forgot-password` - Forgot password form
- `POST /forgot-password` - Process forgot password
- `GET /reset-password` - Reset password form
- `POST /reset-password` - Process password reset
- `POST /api/test-email` - Test email configuration

## 🧹 Maintenance

### Cleanup Expired Codes
```sql
SELECT cleanup_expired_verification_codes();
```

### Optional Cron Job
```bash
# Clean expired OTP codes daily at 2 AM
0 2 * * * psql -d your_db -c "SELECT cleanup_expired_verification_codes();"
```

## 🔒 Security Features

- OTP codes expire after 15 minutes (configurable)
- Maximum 3 attempts per OTP (configurable)
- IP address and user agent logging
- Automatic cleanup of expired codes
- Email verification required for new accounts

## 🎨 UI/UX Features

- Beautiful OTP input with 6 separate boxes
- Auto-focus and paste support
- Countdown timer for resend
- Loading states and validation
- Responsive design with CoreUI
- Password strength indicator

## 📧 Email Content

All emails are sent in Vietnamese with SAPMMO branding and include:
- Professional HTML templates
- Clear OTP codes with styling
- Expiry time information
- Sender information
- Security warnings

## ✅ Testing Checklist

- [ ] Database migration successful
- [ ] Email configuration working
- [ ] Registration with email verification
- [ ] OTP verification working
- [ ] Resend OTP functionality
- [ ] Forgot password flow
- [ ] Password reset working
- [ ] Email templates displaying correctly
- [ ] Mobile responsive design
- [ ] Error handling working

## 🆘 Troubleshooting

### Common Issues
1. **Emails not received**: Check spam folder, verify Mailtrap config
2. **OTP not working**: Check database for codes, verify expiry
3. **Registration fails**: Check Users table structure, verify email column
4. **Import errors**: Ensure email_verification_service.py uploaded

### Debug Commands
```python
# Check email configs
SELECT * FROM "SystemConfig" WHERE config_key LIKE '%mail%';

# Check OTP codes
SELECT * FROM "EmailVerificationCodes" ORDER BY created_at DESC LIMIT 10;

# Check user email verification status
SELECT username, email, email_verified FROM "Users" ORDER BY user_id DESC LIMIT 10;
```
