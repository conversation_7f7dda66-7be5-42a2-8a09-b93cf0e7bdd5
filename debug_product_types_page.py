#!/usr/bin/env python3
"""
Debug /admin/marketplace/product-types page specifically
"""

import sys
import os
import json

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def debug_product_types_page():
    """Debug product-types page and API calls"""
    
    print("🔍 Debugging /admin/marketplace/product-types page...")
    print("=" * 70)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Step 1: Check the page route
        print("\n🔧 Step 1: Checking page route...")
        
        try:
            from mip_system import app
            
            # Find the product-types page route
            page_routes = []
            for rule in app.url_map.iter_rules():
                if 'product-types' in rule.rule and 'admin' in rule.rule and 'api' not in rule.rule:
                    page_routes.append(f"{rule.rule} [{', '.join(rule.methods)}] -> {rule.endpoint}")
            
            if page_routes:
                print(f"  ✅ Found page routes:")
                for route in page_routes:
                    print(f"    - {route}")
            else:
                print(f"  ❌ No page routes found")
                
        except Exception as e:
            print(f"  ❌ Error checking page routes: {e}")
        
        # Step 2: Check template file
        print("\n🔧 Step 2: Checking template file...")
        
        template_path = "/var/www/sapmmo/templates/marketplace/admin/product_types.html"
        try:
            if os.path.exists(template_path):
                print(f"  ✅ Template file exists: {template_path}")
                
                # Check template content for API calls
                with open(template_path, 'r', encoding='utf-8') as f:
                    template_content = f.read()
                
                # Look for API calls in template
                api_calls = []
                if '/api/admin/marketplace/product-types' in template_content:
                    api_calls.append('/api/admin/marketplace/product-types')
                if 'fetch(' in template_content:
                    # Count fetch calls
                    fetch_count = template_content.count('fetch(')
                    api_calls.append(f'{fetch_count} fetch() calls found')
                
                if api_calls:
                    print(f"  📋 API calls in template:")
                    for call in api_calls:
                        print(f"    - {call}")
                else:
                    print(f"  ⚠️  No obvious API calls found in template")
                    
            else:
                print(f"  ❌ Template file does NOT exist: {template_path}")
                
        except Exception as e:
            print(f"  ❌ Error checking template: {e}")
        
        # Step 3: Test the exact API call that page would make
        print("\n🔧 Step 3: Testing API calls that page would make...")
        
        # Test 1: Basic GET without parameters
        try:
            print("  🧪 Test 1: Basic GET /api/admin/marketplace/product-types")
            
            query = '''
                SELECT pt.type_id, pt.name, pt.description, pt.icon, pt.is_default,
                       pt.is_active, pt.metadata, pt.created_at, pt.updated_at,
                       (SELECT COUNT(*) FROM "Products" p WHERE p.product_type_id = pt.type_id AND (p.is_deleted = FALSE OR p.is_deleted IS NULL)) as product_count
                FROM "ProductTypes" pt
                WHERE 1=1
                ORDER BY pt.is_default DESC, pt.name
            '''
            
            cursor.execute(query)
            rows = cursor.fetchall()
            
            print(f"    ✅ Query successful - {len(rows)} rows")
            
            # Format like API would
            product_types = []
            for row in rows:
                product_types.append({
                    'type_id': row[0],
                    'name': row[1],
                    'description': row[2],
                    'icon': row[3],
                    'is_default': row[4],
                    'is_active': row[5],
                    'metadata': row[6],
                    'created_at': row[7].isoformat() if row[7] else None,
                    'updated_at': row[8].isoformat() if row[8] else None,
                    'product_count': row[9]
                })
            
            print(f"    ✅ JSON formatting successful")
            print(f"    📋 Sample result: {product_types[0]['name']} (ID: {product_types[0]['type_id']})")
            
        except Exception as e:
            print(f"    ❌ API query failed: {e}")
            import traceback
            print(f"    📋 Traceback: {traceback.format_exc()}")
        
        # Test 2: GET with filter parameters
        try:
            print("  🧪 Test 2: GET with parameters (?status=&type=&category=&search=)")
            
            # Simulate the exact parameters from the error URL
            status = ''
            type_filter = ''
            category = ''
            search = ''
            
            query = '''
                SELECT pt.type_id, pt.name, pt.description, pt.icon, pt.is_default,
                       pt.is_active, pt.metadata, pt.created_at, pt.updated_at,
                       (SELECT COUNT(*) FROM "Products" p WHERE p.product_type_id = pt.type_id AND (p.is_deleted = FALSE OR p.is_deleted IS NULL)) as product_count
                FROM "ProductTypes" pt
                WHERE 1=1
            '''
            params = []
            
            # Apply filters (same logic as API)
            if status:
                if status == 'active':
                    query += ' AND pt.is_active = TRUE'
                elif status == 'inactive':
                    query += ' AND pt.is_active = FALSE'
            
            if search:
                query += ' AND (pt.name ILIKE %s OR pt.description ILIKE %s)'
                params.extend([f'%{search}%', f'%{search}%'])
            
            query += ' ORDER BY pt.is_default DESC, pt.name'
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            print(f"    ✅ Filtered query successful - {len(rows)} rows")
            
        except Exception as e:
            print(f"    ❌ Filtered query failed: {e}")
        
        # Step 4: Check for common issues
        print("\n🔧 Step 4: Checking for common issues...")
        
        # Check if get_db_connection is working
        try:
            from mip_system import get_db_connection
            test_conn = get_db_connection()
            test_cursor = test_conn.cursor()
            test_cursor.execute('SELECT version();')
            version = test_cursor.fetchone()[0]
            test_conn.close()
            print(f"  ✅ get_db_connection() works - PostgreSQL {version[:20]}...")
        except Exception as e:
            print(f"  ❌ get_db_connection() failed: {e}")
        
        # Check if json module works
        try:
            test_data = {'test': 'data', 'number': 123}
            json_str = json.dumps(test_data)
            parsed = json.loads(json_str)
            print(f"  ✅ JSON serialization works")
        except Exception as e:
            print(f"  ❌ JSON serialization failed: {e}")
        
        # Step 5: Check server logs pattern
        print("\n🔧 Step 5: Server logs analysis...")
        
        print("  📝 To check server logs when accessing the page:")
        print("    1. Open logs: sudo journalctl -u sapmmo -f")
        print("    2. Access page: https://sapmmo.com/admin/marketplace/product-types")
        print("    3. Look for error patterns:")
        print("       - Database connection failed")
        print("       - Import errors")
        print("       - Function not found")
        print("       - Template errors")
        
        # Step 6: Test direct function call
        print("\n🔧 Step 6: Testing direct function call...")
        
        try:
            # Try to call the API function directly
            from mip_system import api_admin_get_product_types
            
            # Mock request args
            class MockRequest:
                def __init__(self):
                    self.args = {'status': '', 'type': '', 'category': '', 'search': ''}
                def get(self, key, default=''):
                    return self.args.get(key, default)
            
            # Mock the request object
            import mip_system
            original_request = mip_system.request
            mip_system.request = MockRequest()
            
            # Call the function (this will fail due to @login_required but we can see other errors)
            try:
                result = api_admin_get_product_types('admin', 1)
                print(f"  ✅ Direct function call successful")
            except Exception as e:
                if 'session' in str(e) or 'login' in str(e).lower():
                    print(f"  ✅ Function exists but requires authentication (expected)")
                else:
                    print(f"  ❌ Direct function call failed: {e}")
            
            # Restore original request
            mip_system.request = original_request
            
        except ImportError as e:
            print(f"  ❌ Function import failed: {e}")
        except Exception as e:
            print(f"  ❌ Direct function test failed: {e}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Debug completed!")
        print("📝 Next steps:")
        print("   1. Check server logs while accessing the page")
        print("   2. Test the debug API: curl https://sapmmo.com/api/debug/product-types-test")
        print("   3. Compare with working local environment")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = debug_product_types_page()
    sys.exit(0 if success else 1)
