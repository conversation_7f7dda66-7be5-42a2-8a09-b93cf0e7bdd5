#!/usr/bin/env python3
"""
Fix missing tables from marketplace deployment
"""

import sys
import os

# Add app directory to path
APP_DIR = "/var/www/sapmmo"
sys.path.append(APP_DIR)

def fix_missing_tables():
    """Fix missing PackageAccounts and WarrantyRequests tables"""
    
    print("🔧 Fixing missing marketplace tables...")
    print("=" * 50)
    
    try:
        # Import config
        from db_config import PG_CONFIG
        import psycopg2
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        print("✅ Connected to database successfully")
        
        # Fix PackageAccounts table
        print("\n🔧 Creating PackageAccounts table...")
        try:
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS "PackageAccounts" (
                    id SERIAL PRIMARY KEY,
                    package_id INTEGER,
                    account_id INTEGER,
                    is_sold BOOLEAN DEFAULT FALSE,
                    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    sold_at TIMESTAMP,
                    order_item_id INTEGER
                );
            ''')
            
            # Add foreign key constraints if they don't exist
            cursor.execute('''
                DO $$ 
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.table_constraints 
                        WHERE constraint_name = 'packageaccounts_package_id_fkey'
                    ) THEN
                        ALTER TABLE "PackageAccounts" 
                        ADD CONSTRAINT packageaccounts_package_id_fkey 
                        FOREIGN KEY (package_id) REFERENCES "AccountPackages"(package_id);
                    END IF;
                END $$;
            ''')
            
            cursor.execute('''
                DO $$ 
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.table_constraints 
                        WHERE constraint_name = 'packageaccounts_account_id_fkey'
                    ) THEN
                        ALTER TABLE "PackageAccounts" 
                        ADD CONSTRAINT packageaccounts_account_id_fkey 
                        FOREIGN KEY (account_id) REFERENCES "Accounts"(account_id);
                    END IF;
                END $$;
            ''')
            
            print("✅ PackageAccounts table created successfully")
            
        except Exception as e:
            print(f"⚠️  PackageAccounts table issue: {e}")
        
        # Fix WarrantyRequests table
        print("\n🔧 Creating WarrantyRequests table...")
        try:
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS "WarrantyRequests" (
                    warranty_id SERIAL PRIMARY KEY,
                    user_id INTEGER,
                    order_item_id INTEGER,
                    account_id INTEGER,
                    reason VARCHAR(100) NOT NULL,
                    description TEXT,
                    status VARCHAR(20) DEFAULT 'pending',
                    replacement_account_id INTEGER,
                    admin_notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    resolved_at TIMESTAMP
                );
            ''')
            
            # Add foreign key constraints if they don't exist
            cursor.execute('''
                DO $$ 
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.table_constraints 
                        WHERE constraint_name = 'warrantyrequests_user_id_fkey'
                    ) THEN
                        ALTER TABLE "WarrantyRequests" 
                        ADD CONSTRAINT warrantyrequests_user_id_fkey 
                        FOREIGN KEY (user_id) REFERENCES "Users"(user_id);
                    END IF;
                END $$;
            ''')
            
            cursor.execute('''
                DO $$ 
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.table_constraints 
                        WHERE constraint_name = 'warrantyrequests_account_id_fkey'
                    ) THEN
                        ALTER TABLE "WarrantyRequests" 
                        ADD CONSTRAINT warrantyrequests_account_id_fkey 
                        FOREIGN KEY (account_id) REFERENCES "Accounts"(account_id);
                    END IF;
                END $$;
            ''')
            
            cursor.execute('''
                DO $$ 
                BEGIN
                    IF NOT EXISTS (
                        SELECT 1 FROM information_schema.table_constraints 
                        WHERE constraint_name = 'warrantyrequests_replacement_account_id_fkey'
                    ) THEN
                        ALTER TABLE "WarrantyRequests" 
                        ADD CONSTRAINT warrantyrequests_replacement_account_id_fkey 
                        FOREIGN KEY (replacement_account_id) REFERENCES "Accounts"(account_id);
                    END IF;
                END $$;
            ''')
            
            print("✅ WarrantyRequests table created successfully")
            
        except Exception as e:
            print(f"⚠️  WarrantyRequests table issue: {e}")
        
        # Create missing indexes
        print("\n🔧 Creating missing indexes...")
        try:
            indexes = [
                'CREATE INDEX IF NOT EXISTS idx_package_accounts_package ON "PackageAccounts"(package_id);',
                'CREATE INDEX IF NOT EXISTS idx_package_accounts_account ON "PackageAccounts"(account_id);',
                'CREATE INDEX IF NOT EXISTS idx_warranty_requests_user ON "WarrantyRequests"(user_id);',
                'CREATE INDEX IF NOT EXISTS idx_warranty_requests_status ON "WarrantyRequests"(status);',
                'CREATE INDEX IF NOT EXISTS idx_products_category ON "Products"(category_id);',
                'CREATE INDEX IF NOT EXISTS idx_products_type ON "Products"(product_type);',
                'CREATE INDEX IF NOT EXISTS idx_products_active ON "Products"(is_active);',
                'CREATE INDEX IF NOT EXISTS idx_orders_user ON "Orders"(user_id);',
                'CREATE INDEX IF NOT EXISTS idx_orders_status ON "Orders"(status);',
                'CREATE INDEX IF NOT EXISTS idx_order_items_order ON "OrderItems"(order_id);',
                'CREATE INDEX IF NOT EXISTS idx_subscriptions_user ON "UserSubscriptions"(user_id);',
                'CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON "UserSubscriptions"(status);',
                'CREATE INDEX IF NOT EXISTS idx_aff_packages_active ON "AFFPackages"(is_active);'
            ]
            
            for index_sql in indexes:
                cursor.execute(index_sql)
            
            print("✅ Indexes created successfully")
            
        except Exception as e:
            print(f"⚠️  Index creation issue: {e}")
        
        # Insert missing coffee configs
        print("\n🔧 Inserting missing coffee configs...")
        try:
            coffee_configs = [
                ('enable_coffee_display', 'false'),
                ('coffee_cup_value', '50000'),
                ('coffee_cup_icon', '☕'),
                ('coffee_icon_type', 'emoji'),
                ('coffee_icon_width', '20'),
                ('coffee_icon_height', '20')
            ]
            
            for key, value in coffee_configs:
                cursor.execute('''
                    INSERT INTO "Config" (config_key, config_value) 
                    VALUES (%s, %s)
                    ON CONFLICT (config_key) DO NOTHING;
                ''', (key, value))
            
            print("✅ Coffee configs inserted successfully")
            
        except Exception as e:
            print(f"⚠️  Coffee config issue: {e}")
        
        # Commit all changes
        conn.commit()
        
        # Validate all tables
        print("\n🔍 Final validation...")
        tables = [
            "ProductCategories", "Products", "ProductFiles", "AccountPackages", 
            "PackageAccounts", "Orders", "OrderItems", "AFFPackages", 
            "UserSubscriptions", "ProductTypes", "WarrantyRequests", 
            "MarketplaceTransactions", "Config"
        ]
        
        for table in tables:
            try:
                cursor.execute(f'SELECT COUNT(*) FROM "{table}";')
                count = cursor.fetchone()[0]
                print(f"✅ {table}: {count} records")
            except Exception as e:
                print(f"❌ {table}: {e}")
        
        # Check revenue_enabled column
        try:
            cursor.execute('SELECT COUNT(*) FROM "Accounts" WHERE revenue_enabled = TRUE;')
            enabled_count = cursor.fetchone()[0]
            cursor.execute('SELECT COUNT(*) FROM "Accounts" WHERE is_deleted = 0;')
            total_count = cursor.fetchone()[0]
            print(f"✅ Accounts.revenue_enabled: {enabled_count}/{total_count} enabled")
        except Exception as e:
            print(f"❌ revenue_enabled column: {e}")
        
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Fix completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import db_config: {e}")
        return False
        
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        return False

if __name__ == "__main__":
    success = fix_missing_tables()
    sys.exit(0 if success else 1)
