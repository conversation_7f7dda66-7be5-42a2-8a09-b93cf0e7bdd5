#!/usr/bin/env python3
"""
Test creating a video product with video links
"""

import psycopg2
import psycopg2.extras
import json

def get_db_connection():
    """Kết nối database PostgreSQL local"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def test_video_product_creation():
    """Test creating a video product"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Get Video product type
        cursor.execute('SELECT type_id FROM "ProductTypes" WHERE name = %s', ('Video',))
        video_type = cursor.fetchone()
        
        if not video_type:
            print("❌ Video product type not found")
            return False
        
        type_id = video_type['type_id']
        print(f"✅ Found Video product type: ID {type_id}")
        
        # Get available video links
        cursor.execute('SELECT link_id, name FROM "VideoLinks" WHERE status = %s LIMIT 3', ('available',))
        available_links = cursor.fetchall()
        
        if not available_links:
            print("❌ No available video links found")
            return False
        
        print(f"✅ Found {len(available_links)} available video links:")
        for link in available_links:
            print(f"   - {link['name']} (ID: {link['link_id']})")
        
        # Get default category
        cursor.execute('SELECT category_id FROM "ProductCategories" LIMIT 1')
        category = cursor.fetchone()
        
        if not category:
            print("❌ No product categories found")
            return False
        
        category_id = category['category_id']
        
        # Create test video product
        product_data = {
            'name': 'Test Video Package - 50 Videos Nuôi Kênh',
            'description': '''## Gói Video Nuôi Kênh TikTok

**Đặc điểm nổi bật:**
- 50 video chất lượng cao
- Thời lượng 15-30 giây
- Nội dung đa dạng, phù hợp nuôi kênh
- Link Google Drive dễ tải về

### Nội dung bao gồm:
- Video dance trending
- Video lifestyle
- Video funny moments
- Video motivation

### Hướng dẫn sử dụng:
1. Tải video từ Google Drive
2. Upload lên TikTok theo lịch
3. Tương tác với audience
4. Theo dõi analytics

**Lưu ý:** Không sử dụng quá 2-3 video/ngày để tránh spam.''',
            'short_description': 'Gói 50 video chất lượng cao để nuôi kênh TikTok',
            'price': 200000,  # 200k MP
            'stock': len(available_links),  # Stock = số video links
            'category_id': category_id,
            'product_type': 'videos',  # Use string type
            'product_type_id': type_id,  # Use integer type_id
            'is_active': True,
            'is_featured': True
        }

        # Insert product
        cursor.execute('''
            INSERT INTO "Products" (
                category_id, product_type_id, product_type, name, description, short_description,
                price, stock, is_active, is_featured, created_at
            ) VALUES (
                %(category_id)s, %(product_type_id)s, %(product_type)s, %(name)s, %(description)s, %(short_description)s,
                %(price)s, %(stock)s, %(is_active)s, %(is_featured)s, CURRENT_TIMESTAMP
            ) RETURNING product_id
        ''', product_data)
        
        product_id = cursor.fetchone()[0]
        print(f"✅ Created test video product: ID {product_id}")
        
        # Assign video links to product
        link_ids = [link['link_id'] for link in available_links]
        for link_id in link_ids:
            cursor.execute('''
                INSERT INTO "ProductVideoLinks" (product_id, link_id)
                VALUES (%s, %s)
            ''', (product_id, link_id))
        
        print(f"✅ Assigned {len(link_ids)} video links to product")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Test video product created successfully!")
        print(f"   Product ID: {product_id}")
        print(f"   Name: {product_data['name']}")
        print(f"   Price: {product_data['price']:,} MP")
        print(f"   Stock: {product_data['stock']} (video links)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating test product: {e}")
        conn.rollback()
        return False

def verify_product():
    """Verify the created product"""
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor(cursor_factory=psycopg2.extras.DictCursor)
        
        # Get the test product
        cursor.execute('''
            SELECT p.product_id, p.name, p.price, p.stock, pt.name as type_name,
                   COUNT(pvl.link_id) as assigned_links
            FROM "Products" p
            JOIN "ProductTypes" pt ON p.type_id = pt.type_id
            LEFT JOIN "ProductVideoLinks" pvl ON p.product_id = pvl.product_id
            WHERE p.name LIKE %s
            GROUP BY p.product_id, p.name, p.price, p.stock, pt.name
            ORDER BY p.created_at DESC
            LIMIT 1
        ''', ('%Test Video Package%',))
        
        product = cursor.fetchone()
        
        if product:
            print(f"\n🔍 Product verification:")
            print(f"   ID: {product['product_id']}")
            print(f"   Name: {product['name']}")
            print(f"   Type: {product['type_name']}")
            print(f"   Price: {product['price']:,} MP")
            print(f"   Stock: {product['stock']}")
            print(f"   Assigned video links: {product['assigned_links']}")
            
            if product['assigned_links'] > 0:
                print("✅ Product has video links assigned!")
                return True
            else:
                print("❌ No video links assigned to product")
                return False
        else:
            print("❌ Test product not found")
            return False
            
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error verifying product: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Testing Video Product Creation")
    print("=" * 50)
    
    # Create test product
    if test_video_product_creation():
        # Verify product
        verify_product()
        print("\n🎉 Video product system is working!")
        print("📋 Ready for user testing:")
        print("  1. Go to admin marketplace")
        print("  2. Create new product with type 'Video'")
        print("  3. Select video links")
        print("  4. Test purchase flow")
    else:
        print("\n❌ Failed to create test product")

if __name__ == "__main__":
    main()
