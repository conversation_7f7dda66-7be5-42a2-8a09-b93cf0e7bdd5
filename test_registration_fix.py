#!/usr/bin/env python3
"""
Test registration với username mới và password_hash column
"""

import psycopg2
import psycopg2.extras
from werkzeug.security import generate_password_hash
from datetime import datetime
import re
import secrets

def get_db_connection():
    """Kết nối database PostgreSQL"""
    try:
        conn = psycopg2.connect(
            host="localhost",
            database="sapmmo",
            user="alandoan",
            password="",
            port="5432"
        )
        return conn
    except Exception as e:
        print(f"❌ Lỗi kết nối database: {e}")
        return None

def clean_username_for_unit_code(username):
    """Loại bỏ tất cả ký tự đặc biệt khỏi username, chỉ giữ lại chữ cái và số"""
    cleaned = re.sub(r'[^a-zA-Z0-9]', '', username)
    return cleaned

def normalize_username(username):
    """Chuẩn hóa username: chuy<PERSON><PERSON> về chữ thường và chỉ cho phép chữ cái, số và dấu gạch dưới"""
    username = username.lower()
    username = re.sub(r'[^a-z0-9_]', '', username)
    return username

def generate_unit_code(username):
    """Tạo unit code từ username đã được làm sạch"""
    clean_username = clean_username_for_unit_code(username)
    random_number = secrets.randbelow(10000)
    return f"SAPMMO{clean_username}{random_number:04d}"

def validate_and_format_phone(phone):
    """Validate and format Vietnamese phone number"""
    if not phone:
        return None
    
    # Remove all non-digit characters
    phone = re.sub(r'[^\d]', '', phone)
    
    # Handle different formats
    if phone.startswith('84'):
        # Already has country code
        if len(phone) == 11:  # 84 + 9 digits
            return f"+{phone}"
    elif phone.startswith('0'):
        # Remove leading 0 and add country code
        phone = phone[1:]
        if len(phone) == 9:
            return f"+84{phone}"
    else:
        # No leading 0, just add country code
        if len(phone) == 9:
            return f"+84{phone}"
    
    # Invalid phone number
    return None

def test_registration():
    """Test registration process"""
    print("🧪 Testing registration process...")
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # Test data với các trường hợp khác nhau
        test_cases = [
            {
                'original_username': 'User.Name-123',
                'email': '<EMAIL>',
                'phone': '0123456789',
                'password': 'password123'
            },
            {
                'original_username': 'TEST_USER',
                'email': '<EMAIL>', 
                'phone': '84987654321',
                'password': 'testpass'
            },
            {
                'original_username': '<EMAIL>',
                'email': '<EMAIL>',
                'phone': '123456789',
                'password': 'adminpass'
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n--- Test Case {i} ---")
            print(f"Original username: '{test_case['original_username']}'")
            
            # Step 1: Normalize username
            username = normalize_username(test_case['original_username'])
            print(f"Normalized username: '{username}'")
            
            # Step 2: Validate phone
            formatted_phone = validate_and_format_phone(test_case['phone'])
            print(f"Formatted phone: '{formatted_phone}'")
            
            # Step 3: Generate unit code
            unit_code = generate_unit_code(username)
            print(f"Unit code: '{unit_code}'")
            
            # Step 4: Hash password
            password_hash = generate_password_hash(test_case['password'])
            print(f"Password hash: '{password_hash[:50]}...'")
            
            # Step 5: Test INSERT
            try:
                cursor.execute('SAVEPOINT test_insert;')
                
                cursor.execute('''
                    INSERT INTO "Users" (username, password_hash, role, email, phone, unit_code, mp_balance, is_deleted)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING user_id
                ''', (username, password_hash, 'leader', test_case['email'], formatted_phone, unit_code, 0, 0))
                
                user_id = cursor.fetchone()[0]
                print(f"✅ Registration successful! User ID: {user_id}")
                
                # Verify data
                cursor.execute('''
                    SELECT username, role, email, phone, unit_code
                    FROM "Users" 
                    WHERE user_id = %s
                ''', (user_id,))
                
                user_data = cursor.fetchone()
                print(f"📋 Saved data: {user_data}")
                
                # Rollback test
                cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
                print("🔄 Test data rolled back")
                
            except Exception as e:
                print(f"❌ Registration failed: {e}")
                cursor.execute('ROLLBACK TO SAVEPOINT test_insert;')
                return False
        
        print("\n✅ All registration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False
        
    finally:
        cursor.close()
        conn.close()

def test_duplicate_username():
    """Test duplicate username handling"""
    print("\n🧪 Testing duplicate username handling...")
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # Create a test user
        test_username = "testduplicate"
        password_hash = generate_password_hash("password123")
        unit_code = generate_unit_code(test_username)
        
        cursor.execute('''
            INSERT INTO "Users" (username, password_hash, role, email, phone, unit_code, mp_balance, is_deleted)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING user_id
        ''', (test_username, password_hash, 'leader', '<EMAIL>', '+84123456789', unit_code, 0, 0))
        
        user_id = cursor.fetchone()[0]
        print(f"✅ Created test user with ID: {user_id}")
        
        # Try to create duplicate
        try:
            cursor.execute('''
                INSERT INTO "Users" (username, password_hash, role, email, phone, unit_code, mp_balance, is_deleted)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                RETURNING user_id
            ''', (test_username, password_hash, 'leader', '<EMAIL>', '+84987654321', unit_code + '2', 0, 0))
            
            print("❌ Duplicate username was allowed - this should not happen!")
            return False
            
        except psycopg2.IntegrityError:
            print("✅ Duplicate username correctly rejected!")
            conn.rollback()
        
        # Clean up
        cursor.execute('DELETE FROM "Users" WHERE user_id = %s', (user_id,))
        conn.commit()
        print("🧹 Test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"❌ Duplicate test error: {e}")
        return False
        
    finally:
        cursor.close()
        conn.close()

def main():
    """Main function"""
    print("🚀 Testing registration fix...")
    print("=" * 60)
    
    # Test registration
    success1 = test_registration()
    
    # Test duplicate handling
    success2 = test_duplicate_username()
    
    if success1 and success2:
        print("\n🎉 All tests passed! Registration is working correctly!")
    else:
        print("\n❌ Some tests failed!")

if __name__ == "__main__":
    main()
